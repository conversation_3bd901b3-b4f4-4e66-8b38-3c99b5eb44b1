// Package jarm 提供JARM TLS指纹识别功能
// JARM是一种主动的TLS服务器指纹识别方法，通过发送多个特制的TLS握手包
// 并分析服务器响应来生成唯一的指纹哈希，用于识别和分类TLS服务器
package jarm

import (
	"context"                           // 上下文控制
	"fmt"                               // 格式化输出
	"net"                               // 网络操作
	"strings"                           // 字符串操作
	"time"                              // 时间处理
	"yaml_scan/pkg/fastdialer"          // 快速拨号器
	"yaml_scan/pkg/gologger"            // 日志记录器
	"yaml_scan/utils/conn"              // 连接池工具

	gojarm "github.com/hdm/jarm-go"     // JARM指纹识别库
)

// poolCount 定义连接池的大小
// JARM需要发送多个探测包，使用连接池可以提高效率和并发性
const poolCount = 3

// HashWithDialer 使用指定的拨号器对单个主机/端口执行JARM指纹识别
// 通过发送多个TLS握手探测包并分析服务器响应来生成JARM哈希
//
// 参数:
//   - dialer: 快速拨号器实例，用于建立网络连接
//   - host: 目标主机名或IP地址
//   - port: 目标端口号
//   - duration: 每个探测的超时时间（秒）
//
// 返回值:
//   - string: 生成的JARM指纹哈希字符串
//   - error: 指纹识别过程中的错误，成功时为nil
//
// JARM工作原理:
//   - 发送10个不同的TLS ClientHello探测包
//   - 每个探测包使用不同的TLS版本、密码套件、扩展组合
//   - 收集服务器对每个探测的响应
//   - 将所有响应组合并生成62字符的指纹哈希
//   - 相同配置的服务器会产生相同的JARM哈希
func HashWithDialer(dialer *fastdialer.Dialer, host string, port int, duration int) (string, error) {
	results := []string{}                                        // 存储每个探测的响应结果
	addr := net.JoinHostPort(host, fmt.Sprintf("%d", port))     // 构建目标地址

	timeout := time.Duration(duration) * time.Second           // 转换超时时间为Duration类型

	// 创建连接池，因为JARM需要发送多个探测包
	// 使用连接池可以复用连接，提高效率
	pool, err := connpool.NewOneTimePool(context.Background(), addr, poolCount)
	if err != nil {
		return "", err
	}
	pool.Dialer = dialer // 设置自定义拨号器

	defer pool.Close() // 确保连接池在函数结束时关闭

	// 启动连接池的后台运行
	go func() {
		if err := pool.Run(); err != nil {
			gologger.Error().Msgf("tlsx: jarm: failed to run connection pool: %v", err)
		}
	}()

	// 遍历所有JARM探测包
	for _, probe := range gojarm.GetProbes(host, port) {
		// 从连接池获取一个连接
		conn, err := pool.Acquire(context.TODO())
		if err != nil {
			continue // 获取连接失败，跳过此探测
		}
		if conn == nil {
			continue // 连接为空，跳过此探测
		}

		// 设置写入超时，防止连接挂起
		_ = conn.SetWriteDeadline(time.Now().Add(timeout))

		// 发送TLS ClientHello探测包
		_, err = conn.Write(gojarm.BuildProbe(probe))
		if err != nil {
			results = append(results, "")  // 发送失败，添加空结果
			_ = conn.Close()               // 关闭连接
			continue
		}

		// 设置读取超时，防止等待响应时挂起
		_ = conn.SetReadDeadline(time.Now().Add(timeout))

		// 读取服务器响应（最大1484字节，足够包含TLS ServerHello）
		buff := make([]byte, 1484)
		_, _ = conn.Read(buff)
		_ = conn.Close() // 读取完成后立即关闭连接

		// 解析服务器的ServerHello响应
		ans, err := gojarm.ParseServerHello(buff, probe)
		if err != nil {
			results = append(results, "")  // 解析失败，添加空结果
			continue
		}
		results = append(results, ans)     // 添加解析成功的结果
	}

	// 将所有探测结果组合并生成最终的JARM哈希
	hash := gojarm.RawHashToFuzzyHash(strings.Join(results, ","))
	return hash, nil
}