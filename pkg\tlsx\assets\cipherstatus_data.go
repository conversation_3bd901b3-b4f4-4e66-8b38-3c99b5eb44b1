//
// Author: chenjb
// Version: V1.0
// Date: 2025-06-24 14:14:54
// FilePath: /yaml_scan/pkg/tlsx/assets/cipherstatus_data.go
// Description: 密码套件安全级别数据和相关工具函数

package assets

import (
	"encoding/json"
	"yaml_scan/pkg/gologger"
	stringsutil "yaml_scan/utils/strings"
)

//go:embed cipherstatus_data.json
var CipherDataBin string  // 嵌入的密码套件安全级别数据，JSON格式

// CipherSecLevel 包含密码套件及其安全级别的映射
// 数据源: https://ciphersuite.info/
// 键为密码套件名称，值为安全级别字符串("Recommended"、"Secure"、"Weak"或"Insecure")
var CipherSecLevel map[string]string = map[string]string{}

// GetSecureCipherSuites 返回安全级别为"Recommended"和"Secure"的密码套件
// @return []string []string: 安全的密码套件名称列表
//	获取被标记为"Recommended"或"Secure"的密码套件列表。
//	这些密码套件是推荐在生产环境中使用的，提供良好的安全性。
//	示例: https://ciphersuite.info/cs/TLS_AES_128_CCM_8_SHA256/
func GetSecureCipherSuites() []string {
	return getCipherWithLevel("Recommended", "Secure")
}

// GetInSecureCipherSuites 返回安全级别为"Insecure"的密码套件
// @return []string []string: 不安全的密码套件名称列表
//	获取被标记为"Insecure"的密码套件列表。
//	不安全的密码套件要么不提供身份验证，要么不提供保密性，应避免使用。
//	示例: https://ciphersuite.info/cs/TLS_NULL_WITH_NULL_NULL/
func GetInSecureCipherSuites() []string {
	return getCipherWithLevel("Insecure")
}

// GetWeakCipherSuites 返回安全级别为"Weak"的密码套件
// @return []string []string: 弱密码套件名称列表
//	获取被标记为"Weak"的密码套件列表。
//	弱密码套件使用被证明较弱或可被破解的算法，应避免使用。
//	示例: https://ciphersuite.info/cs/TLS_RSA_WITH_AES_256_CBC_SHA/
func GetWeakCipherSuites() []string {
	return getCipherWithLevel("Weak")
}

// getCipherWithLevel 返回具有指定安全级别的密码套件
// @param level ...string: 变长参数，要检索的安全级别字符串列表
// @return []string []string: 符合指定安全级别的密码套件名称列表
func getCipherWithLevel(level ...string) []string {
	arr := []string{}
	for k, v := range CipherSecLevel {
		if stringsutil.EqualFoldAny(v, level...) {
			arr = append(arr, k)
		}
	}
	return arr
}

func init() {
	//	从嵌入的JSON数据(CipherDataBin)中加载密码套件安全级别映射到CipherSecLevel变量。
	err := json.Unmarshal([]byte(CipherDataBin), &CipherSecLevel)
	if err != nil {
		gologger.Error().Label("cipher").Msgf("failed to load cipherstatus_data.json, cipher-enum might return unexpected results: %v", err)
	}
}