//
// Author: chenjb
// Version: V1.0
// Date: 2025-06-26
// FilePath: /yaml_scan/pkg/tlsx/tlsx/auto/util_test.go
// Description: auto包util.go的单元测试

package auto

import (
	"testing"

	"github.com/stretchr/testify/require"
)

// TestGlobalVariablesInitialization 测试全局变量的初始化
// 验证包初始化时全局变量的正确设置
func TestGlobalVariablesInitialization(t *testing.T) {
	// 测试allCiphersNames全局变量
	t.Run("allCiphersNames初始化", func(t *testing.T) {
		require.NotNil(t, allCiphersNames, "allCiphersNames不应该为nil")
		require.NotEmpty(t, allCiphersNames, "allCiphersNames不应该为空")
		
		// 验证密码套件名称格式
		for _, cipher := range allCiphersNames {
			require.NotEmpty(t, cipher, "密码套件名称不应该为空")
			// 大多数密码套件都以TLS_或SSL_开头
			require.True(t, 
				len(cipher) >= 4 && (cipher[:4] == "TLS_" || cipher[:4] == "SSL_"), 
				"密码套件名称应该以TLS_或SSL_开头: %s", cipher)
		}
		
		t.Logf("总共聚合了 %d 个密码套件", len(allCiphersNames))
		
		// 显示前几个密码套件作为示例
		maxShow := 10
		if len(allCiphersNames) < maxShow {
			maxShow = len(allCiphersNames)
		}
		t.Logf("示例密码套件: %v", allCiphersNames[:maxShow])
	})

	// 测试supportedTlsVersions全局变量
	t.Run("supportedTlsVersions初始化", func(t *testing.T) {
		require.NotNil(t, supportedTlsVersions, "supportedTlsVersions不应该为nil")
		require.NotEmpty(t, supportedTlsVersions, "supportedTlsVersions不应该为空")
		
		// 验证TLS版本格式
		for _, version := range supportedTlsVersions {
			require.NotEmpty(t, version, "TLS版本不应该为空")
			// TLS版本通常是"1.0", "1.1", "1.2", "1.3"等格式
			require.Regexp(t, `^\d+\.\d+$`, version, "TLS版本格式应该正确: %s", version)
		}
		
		t.Logf("总共聚合了 %d 个TLS版本", len(supportedTlsVersions))
		t.Logf("支持的TLS版本: %v", supportedTlsVersions)
	})
}

// TestAllCiphersNamesUniqueness 测试密码套件名称的唯一性
// 验证聚合后的密码套件列表没有重复项
func TestAllCiphersNamesUniqueness(t *testing.T) {
	// 使用map检查重复项
	seen := make(map[string]bool)
	duplicates := []string{}
	
	for _, cipher := range allCiphersNames {
		if seen[cipher] {
			duplicates = append(duplicates, cipher)
		}
		seen[cipher] = true
	}
	
	require.Empty(t, duplicates, "密码套件列表中不应该有重复项: %v", duplicates)
	require.Equal(t, len(allCiphersNames), len(seen), "密码套件列表长度应该等于唯一项数量")
	
	t.Logf("验证通过：%d个密码套件都是唯一的", len(allCiphersNames))
}

// TestSupportedTlsVersionsUniqueness 测试TLS版本的唯一性
// 验证聚合后的TLS版本列表没有重复项
func TestSupportedTlsVersionsUniqueness(t *testing.T) {
	// 使用map检查重复项
	seen := make(map[string]bool)
	duplicates := []string{}
	
	for _, version := range supportedTlsVersions {
		if seen[version] {
			duplicates = append(duplicates, version)
		}
		seen[version] = true
	}
	
	require.Empty(t, duplicates, "TLS版本列表中不应该有重复项: %v", duplicates)
	require.Equal(t, len(supportedTlsVersions), len(seen), "TLS版本列表长度应该等于唯一项数量")
	
	t.Logf("验证通过：%d个TLS版本都是唯一的", len(supportedTlsVersions))
}

// TestCipherNamesContent 测试密码套件名称的内容
// 验证聚合的密码套件包含预期的常见密码套件
func TestCipherNamesContent(t *testing.T) {
	// 一些常见的密码套件，应该在聚合列表中
	expectedCiphers := []string{
		"TLS_RSA_WITH_AES_128_CBC_SHA",
		"TLS_RSA_WITH_AES_256_CBC_SHA",
		"TLS_RSA_WITH_AES_128_GCM_SHA256",
		"TLS_RSA_WITH_AES_256_GCM_SHA384",
	}
	
	// 将allCiphersNames转换为map以便快速查找
	cipherMap := make(map[string]bool)
	for _, cipher := range allCiphersNames {
		cipherMap[cipher] = true
	}
	
	// 检查常见密码套件是否存在（允许部分不存在，因为不同实现可能支持不同的密码套件）
	foundCount := 0
	for _, expected := range expectedCiphers {
		if cipherMap[expected] {
			foundCount++
			t.Logf("找到预期的密码套件: %s", expected)
		}
	}
	
	// 至少应该找到一些常见的密码套件
	require.Greater(t, foundCount, 0, "应该至少包含一些常见的密码套件")
	t.Logf("在 %d 个预期密码套件中找到了 %d 个", len(expectedCiphers), foundCount)
}

// TestTlsVersionsContent 测试TLS版本的内容
// 验证聚合的TLS版本包含预期的版本
func TestTlsVersionsContent(t *testing.T) {
	// 常见的TLS版本
	expectedVersions := []string{
		"1.0",
		"1.1", 
		"1.2",
		"1.3",
	}
	
	// 将supportedTlsVersions转换为map以便快速查找
	versionMap := make(map[string]bool)
	for _, version := range supportedTlsVersions {
		versionMap[version] = true
	}
	
	// 检查常见TLS版本是否存在
	foundCount := 0
	for _, expected := range expectedVersions {
		if versionMap[expected] {
			foundCount++
			t.Logf("找到预期的TLS版本: %s", expected)
		}
	}
	
	// 至少应该支持TLS 1.2和1.3
	require.True(t, versionMap["1.2"] || versionMap["1.3"], "应该至少支持TLS 1.2或1.3")
	t.Logf("在 %d 个预期TLS版本中找到了 %d 个", len(expectedVersions), foundCount)
}

// TestGlobalVariablesNotEmpty 测试全局变量非空性
// 确保聚合过程产生了有意义的结果
func TestGlobalVariablesNotEmpty(t *testing.T) {
	// 测试密码套件列表
	require.Greater(t, len(allCiphersNames), 0, "密码套件列表应该包含至少一个元素")
	require.Greater(t, len(allCiphersNames), 10, "密码套件列表应该包含足够多的元素")
	
	// 测试TLS版本列表
	require.Greater(t, len(supportedTlsVersions), 0, "TLS版本列表应该包含至少一个元素")
	require.GreaterOrEqual(t, len(supportedTlsVersions), 2, "TLS版本列表应该包含至少2个版本")
	
	t.Logf("聚合结果统计 - 密码套件: %d个, TLS版本: %d个", 
		len(allCiphersNames), len(supportedTlsVersions))
}

// TestCipherNamesFormat 测试密码套件名称格式的一致性
// 验证所有密码套件名称都符合预期的格式规范
func TestCipherNamesFormat(t *testing.T) {
	invalidCiphers := []string{}
	
	for _, cipher := range allCiphersNames {
		// 检查基本格式要求
		if len(cipher) < 4 {
			invalidCiphers = append(invalidCiphers, cipher)
			continue
		}
		
		// 检查前缀
		if cipher[:4] != "TLS_" && cipher[:4] != "SSL_" {
			invalidCiphers = append(invalidCiphers, cipher)
			continue
		}
		
		// 检查是否包含非法字符（密码套件名称应该只包含字母、数字和下划线）
		for _, char := range cipher {
			if !((char >= 'A' && char <= 'Z') || 
				 (char >= 'a' && char <= 'z') || 
				 (char >= '0' && char <= '9') || 
				 char == '_') {
				invalidCiphers = append(invalidCiphers, cipher)
				break
			}
		}
	}
	
	require.Empty(t, invalidCiphers, "发现格式不正确的密码套件: %v", invalidCiphers)
	t.Logf("所有 %d 个密码套件的格式都正确", len(allCiphersNames))
}

// TestTlsVersionsFormat 测试TLS版本格式的一致性
// 验证所有TLS版本都符合预期的格式规范
func TestTlsVersionsFormat(t *testing.T) {
	invalidVersions := []string{}
	
	for _, version := range supportedTlsVersions {
		// TLS版本应该是"主版本.次版本"的格式，如"1.2", "1.3"
		if !isValidTlsVersionFormat(version) {
			invalidVersions = append(invalidVersions, version)
		}
	}
	
	require.Empty(t, invalidVersions, "发现格式不正确的TLS版本: %v", invalidVersions)
	t.Logf("所有 %d 个TLS版本的格式都正确", len(supportedTlsVersions))
}

// isValidTlsVersionFormat 检查TLS版本格式是否有效
// 辅助函数，用于验证TLS版本字符串格式
func isValidTlsVersionFormat(version string) bool {
	// 基本长度检查
	if len(version) < 3 {
		return false
	}
	
	// 查找点号位置
	dotIndex := -1
	for i, char := range version {
		if char == '.' {
			if dotIndex != -1 {
				// 多个点号
				return false
			}
			dotIndex = i
		}
	}
	
	// 必须有且仅有一个点号
	if dotIndex == -1 || dotIndex == 0 || dotIndex == len(version)-1 {
		return false
	}
	
	// 检查点号前后都是数字
	for i, char := range version {
		if i == dotIndex {
			continue
		}
		if char < '0' || char > '9' {
			return false
		}
	}
	
	return true
}
