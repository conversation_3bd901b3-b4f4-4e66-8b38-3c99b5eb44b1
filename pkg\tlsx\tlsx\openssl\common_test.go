//
// Author: chenjb
// Version: V1.0
// Date: 2025-06-26
// FilePath: /yaml_scan/pkg/tlsx/tlsx/openssl/common_test.go
// Description: openssl包common.go的单元测试

package openssl

import (
	"context"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"strings"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
)

// TestErrorDefinitions 测试错误定义
// 验证预定义错误的正确性和一致性
func TestErrorDefinitions(t *testing.T) {
	t.Run("错误对象非空验证", func(t *testing.T) {
		errors := map[string]interface{}{
			"ErrParse":          ErrParse,
			"ErrCertParse":      ErrCertParse,
			"ErrNotImplemented": ErrNotImplemented,
			"ErrNotAvailable":   ErrNotAvailable,
			"ErrNoSession":      ErrNoSession,
		}

		for name, err := range errors {
			require.NotNil(t, err, "%s不应该为nil", name)
		}
	})

	t.Run("错误消息内容验证", func(t *testing.T) {
		testCases := []struct {
			name     string
			err      error
			contains []string
		}{
			{
				name:     "ErrParse",
				err:      ErrParse,
				contains: []string{"parse", "openssl", "response"},
			},
			{
				name:     "ErrCertParse",
				err:      ErrCertParse,
				contains: []string{"parse", "server", "certificate"},
			},
			{
				name:     "ErrNotImplemented",
				err:      ErrNotImplemented,
				contains: []string{"not implemented"},
			},
			{
				name:     "ErrNotAvailable",
				err:      ErrNotAvailable,
				contains: []string{"not installed", "PATH"},
			},
			{
				name:     "ErrNoSession",
				err:      ErrNoSession,
				contains: []string{"session", "not"},
			},
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				errMsg := tc.err.Error()
				require.NotEmpty(t, errMsg, "%s的错误消息不应该为空", tc.name)

				for _, keyword := range tc.contains {
					require.Contains(t, strings.ToLower(errMsg), strings.ToLower(keyword),
						"%s应该包含关键词'%s'", tc.name, keyword)
				}

				// 验证所有错误都包含openssl标签
				require.Contains(t, strings.ToLower(errMsg), "openssl",
					"%s应该包含'openssl'标签", tc.name)
			})
		}
	})

	t.Run("错误类型一致性验证", func(t *testing.T) {
		// 验证所有错误都实现了error接口
		var _ error = ErrParse
		var _ error = ErrCertParse
		var _ error = ErrNotImplemented
		var _ error = ErrNotAvailable
		var _ error = ErrNoSession

		t.Log("所有错误都正确实现了error接口")
	})

	t.Log("所有错误定义验证通过")
}

// TestGlobalVariablesInitialization 测试全局变量的初始化
// 验证包初始化时全局变量的状态和一致性
func TestGlobalVariablesInitialization(t *testing.T) {
	t.Run("全局变量类型验证", func(t *testing.T) {
		require.IsType(t, "", BinaryPath, "BinaryPath应该是字符串类型")
		require.IsType(t, "", OPENSSL_CONF, "OPENSSL_CONF应该是字符串类型")
		require.IsType(t, false, IsLibreSSL, "IsLibreSSL应该是布尔类型")
		require.IsType(t, "", PkgTag, "PkgTag应该是字符串类型")
	})

	t.Run("全局变量状态记录", func(t *testing.T) {
		t.Logf("全局变量状态:")
		t.Logf("  BinaryPath: '%s'", BinaryPath)
		t.Logf("  OPENSSL_CONF: '%s'", OPENSSL_CONF)
		t.Logf("  IsLibreSSL: %v", IsLibreSSL)
		t.Logf("  PkgTag: '%s'", PkgTag)
	})

	t.Run("变量一致性验证", func(t *testing.T) {
		// 如果BinaryPath为空，其他变量也应该处于未初始化状态
		if BinaryPath == "" {
			t.Log("BinaryPath为空，OpenSSL不可用")
			// 当OpenSSL不可用时，配置相关变量应该为空
			require.Empty(t, OPENSSL_CONF, "BinaryPath为空时，OPENSSL_CONF也应该为空")
			require.Empty(t, PkgTag, "BinaryPath为空时，PkgTag也应该为空")
		} else {
			t.Log("BinaryPath已设置，OpenSSL可用")
			// 当OpenSSL可用时，应该有版本信息
			require.NotEmpty(t, PkgTag, "BinaryPath设置时，PkgTag不应该为空")

			// 验证PkgTag与IsLibreSSL的一致性
			if IsLibreSSL {
				require.Contains(t, strings.ToLower(PkgTag), "libressl",
					"IsLibreSSL为true时，PkgTag应该包含LibreSSL")
			} else {
				require.Contains(t, strings.ToLower(PkgTag), "openssl",
					"IsLibreSSL为false时，PkgTag应该包含OpenSSL")
			}
		}
	})

	t.Run("IsAvailable函数一致性", func(t *testing.T) {
		// IsAvailable()的结果应该与BinaryPath的状态一致
		available := IsAvailable()
		if BinaryPath == "" {
			require.False(t, available, "BinaryPath为空时，IsAvailable()应该返回false")
		} else {
			require.True(t, available, "BinaryPath不为空时，IsAvailable()应该返回true")
		}
	})
}

// TestIsAvailable 测试IsAvailable函数
// 验证OpenSSL可用性检测功能
func TestIsAvailable(t *testing.T) {
	t.Run("基本可用性检测", func(t *testing.T) {
		available := IsAvailable()

		// 可用性应该与BinaryPath的状态一致
		if BinaryPath == "" {
			require.False(t, available, "BinaryPath为空时应该返回false")
			t.Log("OpenSSL不可用")
		} else {
			require.True(t, available, "BinaryPath不为空时应该返回true")
			t.Log("OpenSSL可用")
		}
	})

	t.Run("多次调用一致性", func(t *testing.T) {
		// 多次调用应该返回相同结果
		result1 := IsAvailable()
		result2 := IsAvailable()
		result3 := IsAvailable()

		require.Equal(t, result1, result2, "多次调用IsAvailable应该返回相同结果")
		require.Equal(t, result2, result3, "多次调用IsAvailable应该返回相同结果")
	})
}

// TestUseOpenSSLBinary 测试UseOpenSSLBinary函数
// 验证自定义OpenSSL二进制文件设置功能
func TestUseOpenSSLBinary(t *testing.T) {
	// 保存原始状态
	originalBinaryPath := BinaryPath
	originalOPENSSL_CONF := OPENSSL_CONF
	originalIsLibreSSL := IsLibreSSL
	originalPkgTag := PkgTag

	// 测试完成后恢复原始状态
	defer func() {
		BinaryPath = originalBinaryPath
		OPENSSL_CONF = originalOPENSSL_CONF
		IsLibreSSL = originalIsLibreSSL
		PkgTag = originalPkgTag
	}()

	t.Run("设置无效路径", func(t *testing.T) {
		// 设置一个不存在的路径
		invalidPath := "/nonexistent/path/to/openssl"

		// 这个测试可能会导致程序退出，所以我们只验证函数存在
		// 在实际环境中，这会调用gologger.Fatal()
		t.Logf("UseOpenSSLBinary函数存在，可以设置路径: %s", invalidPath)

		// 注意：实际调用UseOpenSSLBinary(invalidPath)会导致程序退出
		// 所以这里只是验证函数的存在性
	})

	t.Run("验证函数签名", func(t *testing.T) {
		// 验证函数可以接受字符串参数
		// 这里不实际调用，只是验证编译时类型检查
		var testFunc func(string) = UseOpenSSLBinary
		require.NotNil(t, testFunc, "UseOpenSSLBinary函数应该存在")
	})
}

// TestOpenSSLConfigContent 测试OpenSSL配置内容
// 验证自定义OpenSSL配置的格式和内容
func TestOpenSSLConfigContent(t *testing.T) {
	t.Run("配置基本验证", func(t *testing.T) {
		require.NotEmpty(t, openSSLConfig, "OpenSSL配置不应该为空")
		require.Greater(t, len(openSSLConfig), 50, "配置内容应该有合理的长度")
	})

	t.Run("配置节验证", func(t *testing.T) {
		expectedSections := []string{
			"openssl_conf",
			"default_conf",
			"ssl_conf",
			"ssl_sect",
			"system_default",
		}

		for _, section := range expectedSections {
			require.Contains(t, openSSLConfig, section, "配置应该包含: %s", section)
		}
	})

	t.Run("配置格式验证", func(t *testing.T) {
		// 验证INI格式的基本元素
		require.Contains(t, openSSLConfig, "[", "配置应该包含节标记开始符")
		require.Contains(t, openSSLConfig, "]", "配置应该包含节标记结束符")
		require.Contains(t, openSSLConfig, "=", "配置应该包含赋值符号")

		// 验证关键配置项
		require.Contains(t, openSSLConfig, "MinProtocol", "配置应该包含MinProtocol设置")
		require.Contains(t, openSSLConfig, "CipherString", "配置应该包含CipherString设置")
		require.Contains(t, openSSLConfig, "SSLv3", "配置应该允许SSLv3协议")
		require.Contains(t, openSSLConfig, "SECLEVEL=0", "配置应该设置安全级别为0")
	})

	t.Run("配置结构验证", func(t *testing.T) {
		lines := strings.Split(openSSLConfig, "\n")

		// 统计节和键值对
		sectionCount := 0
		keyValueCount := 0

		for _, line := range lines {
			line = strings.TrimSpace(line)
			if line == "" || strings.HasPrefix(line, "#") {
				continue
			}

			if strings.HasPrefix(line, "[") && strings.HasSuffix(line, "]") {
				sectionCount++
			} else if strings.Contains(line, "=") {
				keyValueCount++
			}
		}

		require.Greater(t, sectionCount, 0, "配置应该包含至少一个节")
		require.Greater(t, keyValueCount, 0, "配置应该包含至少一个键值对")

		t.Logf("配置结构: %d个节, %d个键值对", sectionCount, keyValueCount)
	})

	t.Logf("OpenSSL配置验证通过，长度: %d字符", len(openSSLConfig))
}

// TestBinaryPathValidation 测试二进制路径验证
// 验证BinaryPath的有效性（如果已设置）
func TestBinaryPathValidation(t *testing.T) {
	if BinaryPath == "" {
		t.Log("BinaryPath未设置，跳过路径验证")
		return
	}

	// 验证路径是否为绝对路径
	require.True(t, filepath.IsAbs(BinaryPath), "BinaryPath应该是绝对路径")

	// 验证文件是否存在
	_, err := os.Stat(BinaryPath)
	require.NoError(t, err, "BinaryPath指向的文件应该存在")

	t.Logf("BinaryPath验证通过: %s", BinaryPath)
}

// TestOpenSSLConfValidation 测试OpenSSL配置文件路径验证
// 验证OPENSSL_CONF的有效性（如果已设置）
func TestOpenSSLConfValidation(t *testing.T) {
	if OPENSSL_CONF == "" {
		t.Log("OPENSSL_CONF未设置，跳过配置文件验证")
		return
	}

	// 验证路径是否为绝对路径
	require.True(t, filepath.IsAbs(OPENSSL_CONF), "OPENSSL_CONF应该是绝对路径")

	// 验证文件是否存在
	_, err := os.Stat(OPENSSL_CONF)
	require.NoError(t, err, "OPENSSL_CONF指向的文件应该存在")

	t.Logf("OPENSSL_CONF验证通过: %s", OPENSSL_CONF)
}

// TestPkgTagFormat 测试包标签格式
// 验证PkgTag的格式（如果已设置）
func TestPkgTagFormat(t *testing.T) {
	if PkgTag == "" {
		t.Log("PkgTag未设置，跳过格式验证")
		return
	}

	// 验证包标签不为空且包含版本信息
	require.NotEmpty(t, PkgTag, "PkgTag不应该为空")

	// 包标签通常包含OpenSSL或LibreSSL字样
	hasOpenSSL := false
	hasLibreSSL := false
	
	if len(PkgTag) > 0 {
		pkgLower := strings.ToLower(PkgTag)
		hasOpenSSL = strings.Contains(pkgLower, "openssl")
		hasLibreSSL = strings.Contains(pkgLower, "libressl")
	}

	require.True(t, hasOpenSSL || hasLibreSSL, "PkgTag应该包含OpenSSL或LibreSSL信息")

	t.Logf("PkgTag格式验证通过: %s", PkgTag)
}

// TestIsLibreSSLConsistency 测试LibreSSL标识的一致性
// 验证IsLibreSSL标识与PkgTag的一致性
func TestIsLibreSSLConsistency(t *testing.T) {
	if PkgTag == "" {
		t.Log("PkgTag未设置，跳过一致性验证")
		return
	}

	pkgLower := strings.ToLower(PkgTag)
	hasLibreSSL := strings.Contains(pkgLower, "libressl")

	// 验证IsLibreSSL标识与PkgTag内容的一致性
	if hasLibreSSL {
		require.True(t, IsLibreSSL, "PkgTag包含LibreSSL时，IsLibreSSL应该为true")
	} else {
		require.False(t, IsLibreSSL, "PkgTag不包含LibreSSL时，IsLibreSSL应该为false")
	}

	t.Logf("LibreSSL标识一致性验证通过 - IsLibreSSL: %v, PkgTag: %s", IsLibreSSL, PkgTag)
}

// TestConfigurationSections 测试配置文件的各个部分
// 验证OpenSSL配置的结构完整性
func TestConfigurationSections(t *testing.T) {
	// 分割配置为行
	lines := strings.Split(openSSLConfig, "\n")
	
	// 查找各个配置节
	foundSections := make(map[string]bool)
	expectedSections := []string{
		"[ default_conf ]",
		"[ssl_sect]",
		"[system_default_sect]",
	}

	for _, line := range lines {
		line = strings.TrimSpace(line)
		for _, section := range expectedSections {
			if line == section {
				foundSections[section] = true
			}
		}
	}

	// 验证所有必要的节都存在
	for _, section := range expectedSections {
		require.True(t, foundSections[section], "配置应该包含节: %s", section)
	}

	t.Logf("配置节验证通过，找到 %d 个必要节", len(foundSections))
}

// TestConfigurationKeyValuePairs 测试配置的键值对
// 验证OpenSSL配置的键值对格式
func TestConfigurationKeyValuePairs(t *testing.T) {
	// 分割配置为行
	lines := strings.Split(openSSLConfig, "\n")
	
	// 查找键值对
	keyValuePairs := make(map[string]string)
	
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" || strings.HasPrefix(line, "[") || strings.HasPrefix(line, "#") {
			continue
		}
		
		if strings.Contains(line, "=") {
			parts := strings.SplitN(line, "=", 2)
			if len(parts) == 2 {
				key := strings.TrimSpace(parts[0])
				value := strings.TrimSpace(parts[1])
				keyValuePairs[key] = value
			}
		}
	}

	// 验证必要的键值对
	expectedKeys := []string{
		"openssl_conf",
		"ssl_conf",
		"system_default",
	}

	for _, key := range expectedKeys {
		require.Contains(t, keyValuePairs, key, "配置应该包含键: %s", key)
		require.NotEmpty(t, keyValuePairs[key], "键 %s 的值不应该为空", key)
	}

	t.Logf("配置键值对验证通过，找到 %d 个键值对", len(keyValuePairs))
}

// TestBinaryPathDetection 测试二进制文件路径检测
// 验证不同操作系统下的OpenSSL检测逻辑
func TestBinaryPathDetection(t *testing.T) {
	t.Run("操作系统特定检测", func(t *testing.T) {
		// 验证当前操作系统的检测逻辑
		if runtime.GOOS == "windows" {
			t.Log("Windows系统：应该查找openssl.exe")
			if BinaryPath != "" {
				require.True(t, strings.HasSuffix(BinaryPath, ".exe") ||
					strings.Contains(BinaryPath, "openssl"),
					"Windows下的BinaryPath应该包含openssl或以.exe结尾")
			}
		} else {
			t.Log("Unix-like系统：应该查找openssl")
			if BinaryPath != "" {
				require.Contains(t, BinaryPath, "openssl",
					"Unix-like系统下的BinaryPath应该包含openssl")
			}
		}
	})

	t.Run("路径有效性验证", func(t *testing.T) {
		if BinaryPath == "" {
			t.Log("BinaryPath未设置，跳过路径验证")
			return
		}

		// 验证路径是否为绝对路径
		require.True(t, filepath.IsAbs(BinaryPath), "BinaryPath应该是绝对路径")

		// 验证文件是否存在
		_, err := os.Stat(BinaryPath)
		require.NoError(t, err, "BinaryPath指向的文件应该存在")

		// 验证文件是否可执行（Unix-like系统）
		if runtime.GOOS != "windows" {
			info, err := os.Stat(BinaryPath)
			require.NoError(t, err)
			mode := info.Mode()
			require.True(t, mode&0111 != 0, "OpenSSL二进制文件应该具有执行权限")
		}

		t.Logf("BinaryPath验证通过: %s", BinaryPath)
	})
}

// TestOpenSSLSetupFunction 测试openSSLSetup函数的行为
// 验证OpenSSL设置过程的各个方面
func TestOpenSSLSetupFunction(t *testing.T) {
	// 只有在OpenSSL可用时才进行测试
	if !IsAvailable() {
		t.Skip("OpenSSL不可用，跳过设置函数测试")
	}

	t.Run("版本信息解析", func(t *testing.T) {
		// 验证PkgTag包含版本信息
		require.NotEmpty(t, PkgTag, "PkgTag应该包含版本信息")

		// 验证版本格式
		if IsLibreSSL {
			require.Contains(t, PkgTag, "LibreSSL", "LibreSSL的PkgTag应该包含LibreSSL")
		} else {
			require.Contains(t, PkgTag, "OpenSSL", "OpenSSL的PkgTag应该包含OpenSSL")
		}

		t.Logf("版本信息: %s", PkgTag)
	})

	t.Run("配置文件创建验证", func(t *testing.T) {
		if IsLibreSSL {
			// LibreSSL不创建配置文件
			require.Empty(t, OPENSSL_CONF, "LibreSSL不应该创建配置文件")
			t.Log("LibreSSL模式：不创建配置文件")
		} else {
			// OpenSSL应该创建配置文件
			if OPENSSL_CONF != "" {
				require.True(t, filepath.IsAbs(OPENSSL_CONF), "OPENSSL_CONF应该是绝对路径")

				// 验证配置文件是否存在
				_, err := os.Stat(OPENSSL_CONF)
				require.NoError(t, err, "配置文件应该存在")

				// 验证配置文件内容
				content, err := os.ReadFile(OPENSSL_CONF)
				require.NoError(t, err, "应该能够读取配置文件")
				require.Contains(t, string(content), "openssl_conf", "配置文件应该包含正确内容")

				t.Logf("OpenSSL配置文件: %s", OPENSSL_CONF)
			} else {
				t.Log("OpenSSL配置文件创建失败或被跳过")
			}
		}
	})
}

// TestConcurrentAccess 测试并发访问全局变量
// 验证多线程环境下的安全性
func TestConcurrentAccess(t *testing.T) {
	t.Run("并发读取全局变量", func(t *testing.T) {
		const numGoroutines = 50
		const numIterations = 100

		var wg sync.WaitGroup
		errors := make(chan error, numGoroutines)

		// 启动多个goroutine并发读取全局变量
		for i := 0; i < numGoroutines; i++ {
			wg.Add(1)
			go func(id int) {
				defer wg.Done()

				for j := 0; j < numIterations; j++ {
					// 并发读取所有全局变量
					_ = BinaryPath
					_ = OPENSSL_CONF
					_ = IsLibreSSL
					_ = PkgTag

					// 并发调用IsAvailable函数
					available := IsAvailable()

					// 验证一致性
					if (BinaryPath == "") != (!available) {
						errors <- fmt.Errorf("goroutine %d: BinaryPath和IsAvailable()不一致", id)
						return
					}
				}
			}(i)
		}

		wg.Wait()
		close(errors)

		// 检查是否有错误
		for err := range errors {
			t.Error(err)
		}

		t.Logf("并发测试完成: %d个goroutine，每个执行%d次迭代", numGoroutines, numIterations)
	})
}

// TestEdgeCases 测试边界条件和异常情况
// 验证各种边界条件下的行为
func TestEdgeCases(t *testing.T) {
	t.Run("空字符串处理", func(t *testing.T) {
		// 测试全局变量可能的空值情况
		if BinaryPath == "" {
			t.Log("BinaryPath为空字符串 - 正常情况")
		}

		if OPENSSL_CONF == "" {
			t.Log("OPENSSL_CONF为空字符串 - 可能的情况")
		}

		if PkgTag == "" {
			t.Log("PkgTag为空字符串 - OpenSSL不可用时的情况")
		}
	})

	t.Run("配置内容边界测试", func(t *testing.T) {
		// 测试配置字符串的边界情况
		require.NotEmpty(t, openSSLConfig, "配置不应该为空")

		// 验证配置不会过长（合理的上限）
		require.Less(t, len(openSSLConfig), 10000, "配置长度应该在合理范围内")

		// 验证配置包含可打印字符
		for i, char := range openSSLConfig {
			if char < 32 && char != '\n' && char != '\r' && char != '\t' {
				t.Errorf("配置在位置%d包含不可打印字符: %d", i, char)
			}
		}
	})

	t.Run("路径长度测试", func(t *testing.T) {
		if BinaryPath != "" {
			// 验证路径长度在合理范围内
			require.Less(t, len(BinaryPath), 1000, "BinaryPath长度应该在合理范围内")
			require.Greater(t, len(BinaryPath), 0, "BinaryPath不应该为空")
		}

		if OPENSSL_CONF != "" {
			require.Less(t, len(OPENSSL_CONF), 1000, "OPENSSL_CONF长度应该在合理范围内")
			require.Greater(t, len(OPENSSL_CONF), 0, "OPENSSL_CONF不应该为空")
		}
	})
}

// TestPerformance 性能测试
// 验证函数在高频调用下的性能表现
func TestPerformance(t *testing.T) {
	// 只在需要时运行性能测试
	if testing.Short() {
		t.Skip("跳过性能测试（使用 -short 标志）")
	}

	t.Run("IsAvailable性能测试", func(t *testing.T) {
		const iterations = 10000

		start := time.Now()
		for i := 0; i < iterations; i++ {
			_ = IsAvailable()
		}
		duration := time.Since(start)

		avgDuration := duration / iterations
		t.Logf("IsAvailable性能测试: %d次调用，总耗时: %v，平均耗时: %v",
			iterations, duration, avgDuration)

		// 性能要求：平均每次调用应该在1微秒以内
		require.Less(t, avgDuration, time.Microsecond, "IsAvailable调用应该非常快")
	})

	t.Run("全局变量访问性能", func(t *testing.T) {
		const iterations = 100000

		start := time.Now()
		for i := 0; i < iterations; i++ {
			_ = BinaryPath
			_ = OPENSSL_CONF
			_ = IsLibreSSL
			_ = PkgTag
		}
		duration := time.Since(start)

		avgDuration := duration / iterations
		t.Logf("全局变量访问性能测试: %d次访问，总耗时: %v，平均耗时: %v",
			iterations, duration, avgDuration)

		// 全局变量访问应该非常快
		require.Less(t, avgDuration, 100*time.Nanosecond, "全局变量访问应该非常快")
	})
}

// TestIntegration 集成测试
// 验证与实际OpenSSL二进制文件的集成
func TestIntegration(t *testing.T) {
	if !IsAvailable() {
		t.Skip("OpenSSL不可用，跳过集成测试")
	}

	t.Run("OpenSSL版本查询", func(t *testing.T) {
		// 尝试执行OpenSSL version命令
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()

		cmd := exec.CommandContext(ctx, BinaryPath, "version")
		if OPENSSL_CONF != "" {
			cmd.Env = append(os.Environ(), "OPENSSL_CONF="+OPENSSL_CONF)
		}

		output, err := cmd.Output()
		if err != nil {
			t.Logf("执行OpenSSL version失败: %v", err)
			// 不要求这个测试必须成功，因为可能有权限或环境问题
			return
		}

		versionStr := string(output)
		require.NotEmpty(t, versionStr, "版本输出不应该为空")

		// 验证版本信息与PkgTag的一致性
		if IsLibreSSL {
			require.Contains(t, strings.ToLower(versionStr), "libressl",
				"版本输出应该包含LibreSSL")
		} else {
			require.Contains(t, strings.ToLower(versionStr), "openssl",
				"版本输出应该包含OpenSSL")
		}

		t.Logf("OpenSSL版本信息: %s", strings.TrimSpace(versionStr))
	})

	t.Run("OpenSSL配置验证", func(t *testing.T) {
		if OPENSSL_CONF == "" {
			t.Log("未设置OPENSSL_CONF，跳过配置验证")
			return
		}

		// 验证配置文件可以被OpenSSL读取
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		cmd := exec.CommandContext(ctx, BinaryPath, "version", "-a")
		cmd.Env = append(os.Environ(), "OPENSSL_CONF="+OPENSSL_CONF)

		output, err := cmd.Output()
		if err != nil {
			t.Logf("使用自定义配置执行OpenSSL失败: %v", err)
			// 不要求这个测试必须成功
			return
		}

		require.NotEmpty(t, output, "配置验证输出不应该为空")
		t.Logf("OpenSSL配置验证成功")
	})
}

// TestEnvironmentVariables 测试环境变量处理
// 验证环境变量对OpenSSL行为的影响
func TestEnvironmentVariables(t *testing.T) {
	t.Run("OPENSSL_CONF环境变量", func(t *testing.T) {
		// 获取当前环境中的OPENSSL_CONF
		envOpenSSLConf := os.Getenv("OPENSSL_CONF")

		if envOpenSSLConf != "" {
			t.Logf("环境中已设置OPENSSL_CONF: %s", envOpenSSLConf)

			// 验证文件是否存在
			if _, err := os.Stat(envOpenSSLConf); err == nil {
				t.Log("环境中的OPENSSL_CONF文件存在")
			} else {
				t.Logf("环境中的OPENSSL_CONF文件不存在: %v", err)
			}
		} else {
			t.Log("环境中未设置OPENSSL_CONF")
		}

		// 验证包设置的OPENSSL_CONF与环境变量的关系
		if OPENSSL_CONF != "" && envOpenSSLConf != "" {
			if OPENSSL_CONF != envOpenSSLConf {
				t.Logf("包设置的OPENSSL_CONF与环境变量不同")
				t.Logf("  包设置: %s", OPENSSL_CONF)
				t.Logf("  环境变量: %s", envOpenSSLConf)
			} else {
				t.Log("包设置的OPENSSL_CONF与环境变量相同")
			}
		}
	})

	t.Run("PATH环境变量", func(t *testing.T) {
		// 验证BinaryPath是否在PATH中
		if BinaryPath == "" {
			t.Log("BinaryPath未设置，无法验证PATH")
			return
		}

		pathEnv := os.Getenv("PATH")
		require.NotEmpty(t, pathEnv, "PATH环境变量不应该为空")

		// 检查BinaryPath的目录是否在PATH中
		binaryDir := filepath.Dir(BinaryPath)
		pathDirs := strings.Split(pathEnv, string(os.PathListSeparator))

		inPath := false
		for _, dir := range pathDirs {
			if dir == binaryDir {
				inPath = true
				break
			}
		}

		if inPath {
			t.Logf("OpenSSL目录在PATH中: %s", binaryDir)
		} else {
			t.Logf("OpenSSL目录不在PATH中: %s", binaryDir)
		}
	})
}
