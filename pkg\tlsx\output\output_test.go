//
// Author: chenjb
// Version: V1.0
// Date: 2025-06-30
// FilePath: /yaml_scan/pkg/tlsx/output/output_test.go
// Description: output.go的单元测试

package output

import (
	"crypto/x509"
	"fmt"
	"os"
	"strings"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"yaml_scan/pkg/tlsx/tlsx/clients"
)

// TestNew 测试New函数
// 验证输出写入器的创建和初始化
func TestNew(t *testing.T) {
	t.Run("基本写入器创建", func(t *testing.T) {
		options := &clients.Options{
			JSON:    false,
			NoColor: false,
		}

		writer, err := New(options)
		require.NoError(t, err, "创建写入器应该成功")
		require.NotNil(t, writer, "写入器不应该为nil")

		err = writer.Close()
		require.NoError(t, err, "关闭写入器应该成功")
	})

	t.Run("JSON模式写入器", func(t *testing.T) {
		options := &clients.Options{
			JSON:    true,
			NoColor: false,
		}

		writer, err := New(options)
		require.NoError(t, err, "创建JSON写入器应该成功")
		require.NotNil(t, writer, "写入器不应该为nil")

		// 验证是StandardWriter类型
		standardWriter, ok := writer.(*StandardWriter)
		require.True(t, ok, "应该是StandardWriter类型")
		require.True(t, standardWriter.json, "应该启用JSON模式")

		err = writer.Close()
		require.NoError(t, err, "关闭写入器应该成功")
	})

	t.Run("无颜色模式写入器", func(t *testing.T) {
		options := &clients.Options{
			JSON:    false,
			NoColor: true,
		}

		writer, err := New(options)
		require.NoError(t, err, "创建无颜色写入器应该成功")
		require.NotNil(t, writer, "写入器不应该为nil")

		err = writer.Close()
		require.NoError(t, err, "关闭写入器应该成功")
	})

	t.Run("文件输出写入器", func(t *testing.T) {
		// 创建临时输出文件
		tmpFile, err := os.CreateTemp("", "tlsx_output_test_*.txt")
		require.NoError(t, err, "创建临时文件应该成功")
		tmpFile.Close()
		defer os.Remove(tmpFile.Name())

		options := &clients.Options{
			JSON:       false,
			NoColor:    false,
			OutputFile: tmpFile.Name(),
		}

		writer, err := New(options)
		require.NoError(t, err, "创建文件输出写入器应该成功")
		require.NotNil(t, writer, "写入器不应该为nil")

		// 验证是StandardWriter类型且有文件输出器
		standardWriter, ok := writer.(*StandardWriter)
		require.True(t, ok, "应该是StandardWriter类型")
		require.NotNil(t, standardWriter.outputFile, "应该有文件输出器")

		err = writer.Close()
		require.NoError(t, err, "关闭写入器应该成功")
	})

	t.Run("无效输出文件路径", func(t *testing.T) {
		options := &clients.Options{
			JSON:       false,
			NoColor:    false,
			OutputFile: "/invalid/path/output.txt",
		}

		writer, err := New(options)
		require.Error(t, err, "无效文件路径应该返回错误")
		require.Nil(t, writer, "错误时写入器应该为nil")
		require.Contains(t, err.Error(), "could not create output file", "错误信息应该正确")
	})
}

// TestStandardWriter 测试StandardWriter结构体
// 验证StandardWriter的基本功能
func TestStandardWriter(t *testing.T) {
	t.Run("StandardWriter字段验证", func(t *testing.T) {
		options := &clients.Options{
			JSON:    true,
			NoColor: true,
		}

		writer, err := New(options)
		require.NoError(t, err, "创建写入器应该成功")

		standardWriter, ok := writer.(*StandardWriter)
		require.True(t, ok, "应该是StandardWriter类型")

		require.True(t, standardWriter.json, "JSON字段应该正确设置")
		require.NotNil(t, standardWriter.aurora, "aurora字段不应该为nil")
		require.NotNil(t, standardWriter.outputMutex, "outputMutex字段不应该为nil")
		require.NotNil(t, standardWriter.options, "options字段不应该为nil")

		err = writer.Close()
		require.NoError(t, err, "关闭写入器应该成功")
	})
}

// TestWrite 测试Write方法
// 验证TLS响应写入功能
func TestWrite(t *testing.T) {
	t.Run("写入基本响应", func(t *testing.T) {
		options := &clients.Options{
			JSON:    false,
			NoColor: true,
		}

		writer, err := New(options)
		require.NoError(t, err, "创建写入器应该成功")
		defer writer.Close()

		// 创建测试响应
		response := &clients.Response{
			Timestamp: time.Now(),
			Host:      "example.com",
			Port:      "443",
			ProbeStatus: true,
		}

		err = writer.Write(response)
		require.NoError(t, err, "写入响应应该成功")
	})

	t.Run("写入JSON响应", func(t *testing.T) {
		options := &clients.Options{
			JSON:    true,
			NoColor: true,
		}

		writer, err := New(options)
		require.NoError(t, err, "创建写入器应该成功")
		defer writer.Close()

		// 创建测试响应
		response := &clients.Response{
			Timestamp: time.Now(),
			Host:      "example.com",
			Port:      "443",
			ProbeStatus: true,
		}

		err = writer.Write(response)
		require.NoError(t, err, "写入JSON响应应该成功")
	})

	t.Run("写入到文件", func(t *testing.T) {
		// 创建临时输出文件
		tmpFile, err := os.CreateTemp("", "tlsx_write_test_*.txt")
		require.NoError(t, err, "创建临时文件应该成功")
		tmpFile.Close()
		defer os.Remove(tmpFile.Name())

		options := &clients.Options{
			JSON:       true,
			NoColor:    true,
			OutputFile: tmpFile.Name(),
		}

		writer, err := New(options)
		require.NoError(t, err, "创建写入器应该成功")

		// 创建测试响应
		response := &clients.Response{
			Timestamp: time.Now(),
			Host:      "example.com",
			Port:      "443",
			ProbeStatus: true,
		}

		err = writer.Write(response)
		require.NoError(t, err, "写入到文件应该成功")

		err = writer.Close()
		require.NoError(t, err, "关闭写入器应该成功")

		// 验证文件内容
		content, err := os.ReadFile(tmpFile.Name())
		require.NoError(t, err, "读取文件应该成功")
		require.NotEmpty(t, content, "文件内容不应该为空")
		require.Contains(t, string(content), "example.com", "文件应该包含主机名")
	})

	t.Run("写入nil响应", func(t *testing.T) {
		options := &clients.Options{
			JSON:    false,
			NoColor: true,
		}

		writer, err := New(options)
		require.NoError(t, err, "创建写入器应该成功")
		defer writer.Close()

		err = writer.Write(nil)
		require.NoError(t, err, "写入nil响应应该成功（被忽略）")
	})
}

// TestGlobalDedupe 测试全局去重功能
// 验证域名去重映射表的功能
func TestGlobalDedupe(t *testing.T) {
	t.Run("全局去重映射表基本功能", func(t *testing.T) {
		// 清理全局去重表
		globalDedupe.Clear()

		// 测试添加和检查
		key := "test.example.com"
		globalDedupe.Set(key, struct{}{})

		exists := globalDedupe.Has(key)
		require.True(t, exists, "添加的键应该存在")

		// 测试不存在的键
		notExists := globalDedupe.Has("nonexistent.com")
		require.False(t, notExists, "不存在的键应该返回false")
	})

	t.Run("全局去重并发安全", func(t *testing.T) {
		// 清理全局去重表
		globalDedupe.Clear()

		const numGoroutines = 50
		const numOperations = 100
		var wg sync.WaitGroup

		// 并发添加操作
		for i := 0; i < numGoroutines; i++ {
			wg.Add(1)
			go func(id int) {
				defer wg.Done()
				for j := 0; j < numOperations; j++ {
					key := fmt.Sprintf("test%d-%d.example.com", id, j)
					globalDedupe.Set(key, struct{}{})
				}
			}(i)
		}

		// 并发读取操作
		for i := 0; i < numGoroutines; i++ {
			wg.Add(1)
			go func(id int) {
				defer wg.Done()
				for j := 0; j < numOperations; j++ {
					key := fmt.Sprintf("test%d-%d.example.com", id, j)
					globalDedupe.Has(key)
				}
			}(i)
		}

		wg.Wait()
		t.Log("并发去重测试完成")
	})
}

// TestDecolorizerRegex 测试颜色代码移除正则表达式
// 验证ANSI颜色代码的正确移除
func TestDecolorizerRegex(t *testing.T) {
	t.Run("移除ANSI颜色代码", func(t *testing.T) {
		testCases := []struct {
			name     string
			input    string
			expected string
		}{
			{
				name:     "基本颜色代码",
				input:    "\x1B[31mRed Text\x1B[0m",
				expected: "Red Text",
			},
			{
				name:     "多个颜色代码",
				input:    "\x1B[32mGreen\x1B[0m and \x1B[34mBlue\x1B[0m",
				expected: "Green and Blue",
			},
			{
				name:     "复杂颜色代码",
				input:    "\x1B[1;31;40mBold Red on Black\x1B[0m",
				expected: "Bold Red on Black",
			},
			{
				name:     "无颜色代码",
				input:    "Plain text without colors",
				expected: "Plain text without colors",
			},
			{
				name:     "空字符串",
				input:    "",
				expected: "",
			},
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				result := decolorizerRegex.ReplaceAllString(tc.input, "")
				require.Equal(t, tc.expected, result, "颜色代码移除结果应该匹配")
			})
		}
	})
}

// TestFormatJSON 测试formatJSON方法
// 验证JSON格式化功能
func TestFormatJSON(t *testing.T) {
	options := &clients.Options{
		JSON:    true,
		NoColor: true,
	}

	writer, err := New(options)
	require.NoError(t, err, "创建写入器应该成功")
	defer writer.Close()

	standardWriter := writer.(*StandardWriter)

	t.Run("格式化基本响应", func(t *testing.T) {
		response := &clients.Response{
			Timestamp:   time.Now(),
			Host:        "example.com",
			Port:        "443",
			ProbeStatus: true,
			Version:     "tls1.3",
			Cipher:      "TLS_AES_256_GCM_SHA384",
		}

		data, err := standardWriter.formatJSON(response)
		require.NoError(t, err, "JSON格式化应该成功")
		require.NotEmpty(t, data, "JSON数据不应该为空")

		// 验证JSON包含关键字段
		jsonStr := string(data)
		require.Contains(t, jsonStr, "example.com", "JSON应该包含主机名")
		require.Contains(t, jsonStr, "443", "JSON应该包含端口")
		require.Contains(t, jsonStr, "tls1.3", "JSON应该包含TLS版本")
	})

	t.Run("格式化带证书的响应", func(t *testing.T) {
		response := &clients.Response{
			Timestamp:   time.Now(),
			Host:        "example.com",
			Port:        "443",
			ProbeStatus: true,
			CertificateResponse: &clients.CertificateResponse{
				SubjectCN:    "example.com",
				SubjectAN:    []string{"example.com", "www.example.com"},
				SubjectOrg:   []string{"Example Corp"},
				Expired:      false,
				SelfSigned:   false,
				MisMatched:   false,
				Revoked:      false,
				Untrusted:    false,
				WildCardCert: false,
				Serial:       "123456789",
				FingerprintHash: clients.FingerprintHash{
					MD5:    "d41d8cd98f00b204e9800998ecf8427e",
					SHA1:   "da39a3ee5e6b4b0d3255bfef95601890afd80709",
					SHA256: "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855",
				},
			},
		}

		data, err := standardWriter.formatJSON(response)
		require.NoError(t, err, "JSON格式化应该成功")
		require.NotEmpty(t, data, "JSON数据不应该为空")

		// 验证JSON包含证书信息
		jsonStr := string(data)
		require.Contains(t, jsonStr, "SubjectCN", "JSON应该包含证书主题CN")
		require.Contains(t, jsonStr, "SubjectAN", "JSON应该包含证书主题AN")
		require.Contains(t, jsonStr, "Example Corp", "JSON应该包含组织信息")
	})

	t.Run("格式化nil响应", func(t *testing.T) {
		data, err := standardWriter.formatJSON(nil)
		require.NoError(t, err, "格式化nil应该成功")
		require.Equal(t, "null", string(data), "nil应该格式化为null")
	})
}

// TestFormatStandard 测试formatStandard方法
// 验证标准格式化功能
func TestFormatStandard(t *testing.T) {
	t.Run("格式化基本响应", func(t *testing.T) {
		options := &clients.Options{
			JSON:    false,
			NoColor: true,
		}

		writer, err := New(options)
		require.NoError(t, err, "创建写入器应该成功")
		defer writer.Close()

		standardWriter := writer.(*StandardWriter)

		response := &clients.Response{
			Timestamp:   time.Now(),
			Host:        "example.com",
			Port:        "443",
			ProbeStatus: true,
			CertificateResponse: &clients.CertificateResponse{
				SubjectCN: "example.com",
			},
		}

		data, err := standardWriter.formatStandard(response)
		require.NoError(t, err, "标准格式化应该成功")
		require.NotEmpty(t, data, "格式化数据不应该为空")

		output := string(data)
		require.Contains(t, output, "example.com:443", "输出应该包含主机名和端口")
	})

	t.Run("格式化nil响应", func(t *testing.T) {
		options := &clients.Options{
			JSON:    false,
			NoColor: true,
		}

		writer, err := New(options)
		require.NoError(t, err, "创建写入器应该成功")
		defer writer.Close()

		standardWriter := writer.(*StandardWriter)

		data, err := standardWriter.formatStandard(nil)
		require.Error(t, err, "格式化nil响应应该返回错误")
		require.Contains(t, err.Error(), "empty certificate response", "错误信息应该正确")
		require.Nil(t, data, "错误时数据应该为nil")
	})

	t.Run("格式化无证书响应", func(t *testing.T) {
		options := &clients.Options{
			JSON:    false,
			NoColor: true,
		}

		writer, err := New(options)
		require.NoError(t, err, "创建写入器应该成功")
		defer writer.Close()

		standardWriter := writer.(*StandardWriter)

		response := &clients.Response{
			Timestamp:           time.Now(),
			Host:                "example.com",
			Port:                "443",
			ProbeStatus:         true,
			CertificateResponse: nil, // 无证书响应
		}

		data, err := standardWriter.formatStandard(response)
		require.Error(t, err, "无证书响应应该返回错误")
		require.Contains(t, err.Error(), "empty leaf certificate", "错误信息应该正确")
		require.Nil(t, data, "错误时数据应该为nil")
	})
}

// TestFormatStandardOptions 测试formatStandard方法的各种选项
// 验证不同输出选项的格式化功能
func TestFormatStandardOptions(t *testing.T) {
	// 创建基础测试响应
	baseResponse := &clients.Response{
		Timestamp:   time.Now(),
		Host:        "example.com",
		Port:        "443",
		IP:          "***********",
		ProbeStatus: true,
		Version:     "tls1.3",
		Cipher:      "TLS_AES_256_GCM_SHA384",
		ServerName:  "test.example.com",
		JarmHash:    "jarm123456",
		Ja3Hash:     "ja3123456",
		Ja3sHash:    "ja3s123456",
		CertificateResponse: &clients.CertificateResponse{
			SubjectCN:    "example.com",
			SubjectAN:    []string{"example.com", "www.example.com", "*.example.com"},
			SubjectOrg:   []string{"Example Corp", "Test Org"},
			Domains:      []string{"example.com", "www.example.com", "api.example.com"},
			Expired:      false,
			SelfSigned:   false,
			MisMatched:   false,
			Revoked:      false,
			Untrusted:    false,
			WildCardCert: true,
			Serial:       "123456789ABCDEF",
			FingerprintHash: clients.FingerprintHash{
				MD5:    "d41d8cd98f00b204e9800998ecf8427e",
				SHA1:   "da39a3ee5e6b4b0d3255bfef95601890afd80709",
				SHA256: "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855",
			},
		},
	}

	t.Run("DNS模式输出", func(t *testing.T) {
		// 清理全局去重表
		globalDedupe.Clear()

		options := &clients.Options{
			JSON:       false,
			NoColor:    true,
			DisplayDns: true,
		}

		writer, err := New(options)
		require.NoError(t, err, "创建写入器应该成功")
		defer writer.Close()

		standardWriter := writer.(*StandardWriter)

		data, err := standardWriter.formatStandard(baseResponse)
		require.NoError(t, err, "DNS模式格式化应该成功")

		output := string(data)
		require.Contains(t, output, "example.com", "DNS输出应该包含域名")
		require.Contains(t, output, "www.example.com", "DNS输出应该包含子域名")
		require.Contains(t, output, "api.example.com", "DNS输出应该包含API域名")
	})

	t.Run("SAN模式输出", func(t *testing.T) {
		options := &clients.Options{
			JSON:    false,
			NoColor: true,
			SAN:     true,
		}

		writer, err := New(options)
		require.NoError(t, err, "创建写入器应该成功")
		defer writer.Close()

		standardWriter := writer.(*StandardWriter)

		data, err := standardWriter.formatStandard(baseResponse)
		require.NoError(t, err, "SAN模式格式化应该成功")

		output := string(data)
		require.Contains(t, output, "example.com", "SAN输出应该包含主域名")
		require.Contains(t, output, "www.example.com", "SAN输出应该包含子域名")
	})

	t.Run("CN模式输出", func(t *testing.T) {
		options := &clients.Options{
			JSON:    false,
			NoColor: true,
			CN:      true,
		}

		writer, err := New(options)
		require.NoError(t, err, "创建写入器应该成功")
		defer writer.Close()

		standardWriter := writer.(*StandardWriter)

		data, err := standardWriter.formatStandard(baseResponse)
		require.NoError(t, err, "CN模式格式化应该成功")

		output := string(data)
		require.Contains(t, output, "example.com", "CN输出应该包含通用名称")
	})

	t.Run("RespOnly模式输出", func(t *testing.T) {
		options := &clients.Options{
			JSON:     false,
			NoColor:  true,
			SAN:      true,
			RespOnly: true,
		}

		writer, err := New(options)
		require.NoError(t, err, "创建写入器应该成功")
		defer writer.Close()

		standardWriter := writer.(*StandardWriter)

		data, err := standardWriter.formatStandard(baseResponse)
		require.NoError(t, err, "RespOnly模式格式化应该成功")

		output := string(data)
		// RespOnly模式不应该包含主机名和端口前缀
		require.NotContains(t, output, "example.com:443", "RespOnly输出不应该包含主机端口前缀")
		require.Contains(t, output, "example.com", "RespOnly输出应该包含域名")
	})

	t.Run("多IP扫描模式输出", func(t *testing.T) {
		options := &clients.Options{
			JSON:       false,
			NoColor:    true,
			ScanAllIPs: true,
		}

		writer, err := New(options)
		require.NoError(t, err, "创建写入器应该成功")
		defer writer.Close()

		standardWriter := writer.(*StandardWriter)

		data, err := standardWriter.formatStandard(baseResponse)
		require.NoError(t, err, "多IP模式格式化应该成功")

		output := string(data)
		require.Contains(t, output, "example.com:443", "多IP输出应该包含主机端口")
		require.Contains(t, output, "(***********)", "多IP输出应该包含实际IP地址")
	})

	t.Run("探测状态输出", func(t *testing.T) {
		options := &clients.Options{
			JSON:        false,
			NoColor:     true,
			ProbeStatus: true,
		}

		writer, err := New(options)
		require.NoError(t, err, "创建写入器应该成功")
		defer writer.Close()

		standardWriter := writer.(*StandardWriter)

		// 测试成功状态
		successResponse := *baseResponse
		successResponse.ProbeStatus = true

		data, err := standardWriter.formatStandard(&successResponse)
		require.NoError(t, err, "成功状态格式化应该成功")

		output := string(data)
		require.Contains(t, output, "success", "成功状态输出应该包含success")

		// 测试失败状态
		failResponse := *baseResponse
		failResponse.ProbeStatus = false

		data, err = standardWriter.formatStandard(&failResponse)
		require.NoError(t, err, "失败状态格式化应该成功")

		output = string(data)
		require.Contains(t, output, "failed", "失败状态输出应该包含failed")
	})

	t.Run("证书状态标识输出", func(t *testing.T) {
		options := &clients.Options{
			JSON:              false,
			NoColor:           true,
			Expired:           true,
			SelfSigned:        true,
			MisMatched:        true,
			Revoked:           true,
			Untrusted:         true,
			WildcardCertCheck: true,
		}

		writer, err := New(options)
		require.NoError(t, err, "创建写入器应该成功")
		defer writer.Close()

		standardWriter := writer.(*StandardWriter)

		// 创建带有各种状态的证书响应
		statusResponse := *baseResponse
		statusResponse.CertificateResponse = &clients.CertificateResponse{
			SubjectCN:    "example.com",
			Expired:      true,
			SelfSigned:   true,
			MisMatched:   true,
			Revoked:      true,
			Untrusted:    true,
			WildCardCert: true,
		}

		data, err := standardWriter.formatStandard(&statusResponse)
		require.NoError(t, err, "证书状态格式化应该成功")

		output := string(data)
		require.Contains(t, output, "expired", "输出应该包含过期标识")
		require.Contains(t, output, "self-signed", "输出应该包含自签名标识")
		require.Contains(t, output, "mismatched", "输出应该包含不匹配标识")
		require.Contains(t, output, "revoked", "输出应该包含已撤销标识")
		require.Contains(t, output, "untrusted", "输出应该包含不受信任标识")
		require.Contains(t, output, "wildcard", "输出应该包含通配符标识")
	})
}
