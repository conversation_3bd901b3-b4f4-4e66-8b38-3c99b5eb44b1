/* TLS扫描库文档自定义样式 */
/* Author: chenjb */
/* Version: V1.0 */
/* Date: 2025-07-01 */

/* 自定义颜色主题 */
:root {
    /* 主色调 */
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(45deg, #2563eb, #7c3aed);
    --success-gradient: linear-gradient(45deg, #059669, #10b981);
    --warning-gradient: linear-gradient(45deg, #d97706, #f59e0b);
    --danger-gradient: linear-gradient(45deg, #dc2626, #ef4444);
    
    /* 背景色 */
    --bg-primary: rgba(255, 255, 255, 0.95);
    --bg-secondary: rgba(248, 250, 252, 0.9);
    --bg-dark: #1f2937;
    
    /* 阴影效果 */
    --shadow-sm: 0 5px 15px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 10px 30px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 20px 40px rgba(0, 0, 0, 0.1);
    
    /* 边框圆角 */
    --radius-sm: 8px;
    --radius-md: 15px;
    --radius-lg: 20px;
    
    /* 动画时间 */
    --transition-fast: 0.2s;
    --transition-normal: 0.3s;
    --transition-slow: 0.5s;
}

/* 深色主题 */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-primary: rgba(31, 41, 55, 0.95);
        --bg-secondary: rgba(17, 24, 39, 0.9);
        --text-color: #f9fafb;
        --text-muted: #d1d5db;
    }
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-gradient);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-gradient);
}

/* 选择文本样式 */
::selection {
    background: rgba(37, 99, 235, 0.3);
    color: inherit;
}

/* 自定义动画 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

/* 加载动画 */
.loading {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
}

/* 悬浮效果增强 */
.hover-lift {
    transition: transform var(--transition-normal), box-shadow var(--transition-normal);
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

/* 按钮样式增强 */
.btn-gradient {
    background: var(--primary-gradient);
    border: none;
    color: white;
    padding: 12px 24px;
    border-radius: var(--radius-md);
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.btn-gradient::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--transition-slow);
}

.btn-gradient:hover::before {
    left: 100%;
}

.btn-gradient:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* 卡片样式增强 */
.card-enhanced {
    background: var(--bg-primary);
    backdrop-filter: blur(10px);
    border-radius: var(--radius-lg);
    padding: 30px;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.card-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
}

.card-enhanced:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

/* 代码块增强 */
.code-enhanced {
    position: relative;
    background: var(--bg-dark);
    border-radius: var(--radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.code-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 30px;
    background: linear-gradient(90deg, #ff5f56, #ffbd2e, #27ca3f);
    display: flex;
    align-items: center;
    padding: 0 15px;
}

.code-enhanced::after {
    content: '● ● ●';
    position: absolute;
    top: 8px;
    left: 15px;
    color: #333;
    font-size: 12px;
    letter-spacing: 3px;
}

.code-enhanced pre {
    margin-top: 30px;
    padding: 20px;
}

/* 表格增强 */
.table-enhanced {
    border-radius: var(--radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    background: white;
}

.table-enhanced th {
    background: var(--primary-gradient);
    color: white;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table-enhanced tr {
    transition: background-color var(--transition-fast);
}

.table-enhanced tr:hover {
    background: rgba(37, 99, 235, 0.05);
}

/* 徽章样式 */
.badge-enhanced {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    color: white;
    text-decoration: none;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.badge-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.2);
    transition: left var(--transition-normal);
}

.badge-enhanced:hover::before {
    left: 100%;
}

.badge-enhanced:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-sm);
}

/* 响应式增强 */
@media (max-width: 768px) {
    .card-enhanced {
        padding: 20px;
        margin: 10px 0;
    }
    
    .btn-gradient {
        padding: 10px 20px;
        font-size: 0.9rem;
    }
    
    .badge-enhanced {
        padding: 4px 8px;
        font-size: 0.8rem;
    }
}

/* 打印样式 */
@media print {
    .nav, .footer, .btn-gradient {
        display: none !important;
    }
    
    .section {
        break-inside: avoid;
        page-break-inside: avoid;
    }
    
    .code-block {
        break-inside: avoid;
    }
}
