//
// Author: chenjb
// Version: V1.0
// Date: 2025-06-26
// FilePath: /yaml_scan/pkg/tlsx/tlsx/tls/util_test.go
// Description: tls包util.go的单元测试

package tls

import (
	"testing"

	"github.com/stretchr/testify/require"
)

// TestGlobalVariablesInitialization 测试全局变量的初始化
// 验证包初始化时全局变量的正确设置
func TestGlobalVariablesInitialization(t *testing.T) {
	// 测试AllCiphers全局变量
	t.Run("AllCiphers初始化", func(t *testing.T) {
		require.NotNil(t, AllCiphers, "AllCiphers不应该为nil")
		require.NotEmpty(t, AllCiphers, "AllCiphers不应该为空")
		
		// 验证密码套件数值的有效性
		for _, cipher := range AllCiphers {
			require.Greater(t, cipher, uint16(0), "密码套件数值应该大于0")
		}
		
		t.Logf("总共初始化了 %d 个密码套件数值", len(AllCiphers))
	})

	// 测试AllCiphersNames全局变量
	t.Run("AllCiphersNames初始化", func(t *testing.T) {
		require.NotNil(t, AllCiphersNames, "AllCiphersNames不应该为nil")
		require.NotEmpty(t, AllCiphersNames, "AllCiphersNames不应该为空")
		
		// 验证密码套件名称格式
		for _, cipher := range AllCiphersNames {
			require.NotEmpty(t, cipher, "密码套件名称不应该为空")
			require.Contains(t, cipher, "TLS_", "密码套件名称应该包含TLS_前缀")
		}
		
		t.Logf("总共初始化了 %d 个密码套件名称", len(AllCiphersNames))
		
		// 显示前几个密码套件作为示例
		maxShow := 5
		if len(AllCiphersNames) < maxShow {
			maxShow = len(AllCiphersNames)
		}
		t.Logf("示例密码套件: %v", AllCiphersNames[:maxShow])
	})

	// 测试SupportedTlsVersions全局变量
	t.Run("SupportedTlsVersions初始化", func(t *testing.T) {
		require.NotNil(t, SupportedTlsVersions, "SupportedTlsVersions不应该为nil")
		require.NotEmpty(t, SupportedTlsVersions, "SupportedTlsVersions不应该为空")
		
		// 验证TLS版本格式
		expectedVersions := []string{"tls10", "tls11", "tls12", "tls13"}
		for _, expected := range expectedVersions {
			require.Contains(t, SupportedTlsVersions, expected, "应该包含TLS版本: %s", expected)
		}
		
		t.Logf("支持的TLS版本: %v", SupportedTlsVersions)
	})
}

// TestAllCiphersConsistency 测试密码套件数组的一致性
// 验证AllCiphers和AllCiphersNames的长度一致性
func TestAllCiphersConsistency(t *testing.T) {
	require.Equal(t, len(AllCiphers), len(AllCiphersNames), 
		"AllCiphers和AllCiphersNames的长度应该一致")
	
	t.Logf("密码套件数组一致性验证通过：%d个密码套件", len(AllCiphers))
}

// TestAllCiphersUniqueness 测试密码套件的唯一性
// 验证密码套件数组中没有重复项
func TestAllCiphersUniqueness(t *testing.T) {
	// 测试密码套件数值的唯一性
	t.Run("密码套件数值唯一性", func(t *testing.T) {
		seen := make(map[uint16]bool)
		duplicates := []uint16{}
		
		for _, cipher := range AllCiphers {
			if seen[cipher] {
				duplicates = append(duplicates, cipher)
			}
			seen[cipher] = true
		}
		
		require.Empty(t, duplicates, "密码套件数值中不应该有重复项: %v", duplicates)
		t.Logf("验证通过：%d个密码套件数值都是唯一的", len(AllCiphers))
	})

	// 测试密码套件名称的唯一性
	t.Run("密码套件名称唯一性", func(t *testing.T) {
		seen := make(map[string]bool)
		duplicates := []string{}
		
		for _, cipher := range AllCiphersNames {
			if seen[cipher] {
				duplicates = append(duplicates, cipher)
			}
			seen[cipher] = true
		}
		
		require.Empty(t, duplicates, "密码套件名称中不应该有重复项: %v", duplicates)
		t.Logf("验证通过：%d个密码套件名称都是唯一的", len(AllCiphersNames))
	})
}

// TestSupportedTlsVersionsUniqueness 测试TLS版本的唯一性
// 验证TLS版本数组中没有重复项
func TestSupportedTlsVersionsUniqueness(t *testing.T) {
	seen := make(map[string]bool)
	duplicates := []string{}
	
	for _, version := range SupportedTlsVersions {
		if seen[version] {
			duplicates = append(duplicates, version)
		}
		seen[version] = true
	}
	
	require.Empty(t, duplicates, "TLS版本中不应该有重复项: %v", duplicates)
	require.Equal(t, len(SupportedTlsVersions), len(seen), "TLS版本数组长度应该等于唯一项数量")
	
	t.Logf("验证通过：%d个TLS版本都是唯一的", len(SupportedTlsVersions))
}

// TestToTLSCiphers 测试密码套件名称到数值的转换功能
// 验证toTLSCiphers函数的正确性
func TestToTLSCiphers(t *testing.T) {
	tests := []struct {
		name        string   // 测试用例名称
		items       []string // 输入的密码套件名称
		description string   // 测试描述
	}{
		{
			name:        "空列表",
			items:       []string{},
			description: "空列表应该返回空结果",
		},
		{
			name:        "有效密码套件",
			items:       []string{"TLS_RSA_WITH_AES_128_CBC_SHA"},
			description: "有效的密码套件名称应该能正确转换",
		},
		{
			name:        "多个密码套件",
			items:       []string{"TLS_RSA_WITH_AES_128_CBC_SHA", "TLS_RSA_WITH_AES_256_CBC_SHA"},
			description: "多个密码套件应该都能正确转换",
		},
		{
			name:        "无效密码套件",
			items:       []string{"INVALID_CIPHER"},
			description: "无效的密码套件名称应该被忽略",
		},
		{
			name:        "混合有效和无效密码套件",
			items:       []string{"TLS_RSA_WITH_AES_128_CBC_SHA", "INVALID_CIPHER"},
			description: "混合列表应该只转换有效的密码套件",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := toTLSCiphers(tt.items)
			
			// 基本验证
			require.NoError(t, err, "转换过程不应该出错")
			require.NotNil(t, result, "结果不应该为nil")
			
			// 验证结果长度不超过输入长度
			require.LessOrEqual(t, len(result), len(tt.items), "结果长度不应该超过输入长度")
			
			// 验证所有结果都是有效的密码套件数值
			for _, cipher := range result {
				require.Greater(t, cipher, uint16(0), "密码套件数值应该大于0")
			}
			
			t.Logf("转换结果: %d个有效密码套件", len(result))
		})
	}
}

// TestToTLSCiphersWithKnownCiphers 测试已知密码套件的转换
// 使用已知存在的密码套件进行转换测试
func TestToTLSCiphersWithKnownCiphers(t *testing.T) {
	// 使用前几个已知的密码套件进行测试
	maxTest := 5
	if len(AllCiphersNames) < maxTest {
		maxTest = len(AllCiphersNames)
	}
	
	testCiphers := AllCiphersNames[:maxTest]
	
	result, err := toTLSCiphers(testCiphers)
	require.NoError(t, err, "转换已知密码套件不应该出错")
	require.Len(t, result, len(testCiphers), "所有已知密码套件都应该能转换成功")
	
	// 验证转换结果都是有效的数值
	for i, cipher := range result {
		require.Greater(t, cipher, uint16(0), "密码套件数值应该大于0")
		t.Logf("密码套件 '%s' 转换为数值: %d", testCiphers[i], cipher)
	}
}

// TestCipherNamesContent 测试密码套件名称的内容
// 验证包含预期的常见密码套件
func TestCipherNamesContent(t *testing.T) {
	// 一些常见的密码套件，应该在列表中
	expectedCiphers := []string{
		"TLS_RSA_WITH_AES_128_CBC_SHA",
		"TLS_RSA_WITH_AES_256_CBC_SHA",
		"TLS_RSA_WITH_AES_128_GCM_SHA256",
		"TLS_RSA_WITH_AES_256_GCM_SHA384",
	}
	
	// 将AllCiphersNames转换为map以便快速查找
	cipherMap := make(map[string]bool)
	for _, cipher := range AllCiphersNames {
		cipherMap[cipher] = true
	}
	
	// 检查常见密码套件是否存在
	foundCount := 0
	for _, expected := range expectedCiphers {
		if cipherMap[expected] {
			foundCount++
			t.Logf("找到预期的密码套件: %s", expected)
		}
	}
	
	// 至少应该找到一些常见的密码套件
	require.Greater(t, foundCount, 0, "应该至少包含一些常见的密码套件")
	t.Logf("在 %d 个预期密码套件中找到了 %d 个", len(expectedCiphers), foundCount)
}

// TestCipherNamesFormat 测试密码套件名称格式的一致性
// 验证所有密码套件名称都符合预期的格式规范
func TestCipherNamesFormat(t *testing.T) {
	invalidCiphers := []string{}
	
	for _, cipher := range AllCiphersNames {
		// 检查基本格式要求
		if len(cipher) < 4 {
			invalidCiphers = append(invalidCiphers, cipher)
			continue
		}
		
		// 检查TLS_前缀
		if cipher[:4] != "TLS_" {
			invalidCiphers = append(invalidCiphers, cipher)
			continue
		}
		
		// 检查是否包含非法字符（密码套件名称应该只包含字母、数字和下划线）
		for _, char := range cipher {
			if !((char >= 'A' && char <= 'Z') || 
				 (char >= 'a' && char <= 'z') || 
				 (char >= '0' && char <= '9') || 
				 char == '_') {
				invalidCiphers = append(invalidCiphers, cipher)
				break
			}
		}
	}
	
	require.Empty(t, invalidCiphers, "发现格式不正确的密码套件: %v", invalidCiphers)
	t.Logf("所有 %d 个密码套件的格式都正确", len(AllCiphersNames))
}

// TestTlsVersionsContent 测试TLS版本的内容完整性
// 验证包含所有预期的TLS版本
func TestTlsVersionsContent(t *testing.T) {
	// 预期的TLS版本
	expectedVersions := []string{"tls10", "tls11", "tls12", "tls13"}
	
	// 验证每个预期版本都存在
	for _, expected := range expectedVersions {
		require.Contains(t, SupportedTlsVersions, expected, "应该包含TLS版本: %s", expected)
	}
	
	// 验证版本数量
	require.Equal(t, len(expectedVersions), len(SupportedTlsVersions), 
		"TLS版本数量应该匹配预期")
	
	t.Logf("TLS版本内容验证通过：%v", SupportedTlsVersions)
}
