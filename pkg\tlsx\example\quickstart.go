//
// Author: chenjb
// Version: V1.0
// Date: 2025-07-01
// FilePath: /yaml_scan/pkg/tlsx/example/quickstart.go
// Description: TLS扫描库快速开始示例

package main

import (
	"fmt"
	"log"

	"yaml_scan/pkg/tlsx/tlsx"
	"yaml_scan/pkg/tlsx/tlsx/clients"
)

// quickStart 快速开始示例
// 演示最简单的TLS扫描用法
func quickStart() {
	// 1. 创建基本配置
	options := &clients.Options{
		ScanMode: "ctls",  // 使用Go原生TLS
		Timeout:  10,      // 10秒超时
		Retries:  3,       // 重试3次
	}

	// 2. 创建TLS服务
	service, err := tlsx.New(options)
	if err != nil {
		log.Fatalf("创建TLS服务失败: %v", err)
	}

	// 3. 连接目标主机
	response, err := service.Connect("www.baidu.com", "", "443")
	if err != nil {
		log.Fatalf("连接失败: %v", err)
	}

	// 4. 输出结果
	fmt.Printf("主机: %s:%s\n", response.Host, response.Port)
	fmt.Printf("TLS版本: %s\n", response.Version)
	fmt.Printf("密码套件: %s\n", response.Cipher)
	
	if response.CertificateResponse != nil {
		cert := response.CertificateResponse
		fmt.Printf("证书CN: %s\n", cert.SubjectCN)
		fmt.Printf("证书组织: %v\n", cert.SubjectOrg)
		fmt.Printf("证书过期: %t\n", cert.Expired)
	}
}

func main() {
	fmt.Println("TLS扫描库快速开始示例")
	fmt.Println("===================")
	quickStart()
}
