// Author: chenjb
// Version: V1.0
// Date: 2025-05-26 20:13:11
// FilePath: /yaml_scan/pkg/fastdialer/dialer_test.go
// Description:
package fastdialer

import (
	"context"
	"encoding/gob"
	"net"
	"testing"
	"time"

	"yaml_scan/pkg/hybridMap/hybrid"

	"github.com/miekg/dns"
	"github.com/stretchr/testify/require"
)

// TestNewDialer 测试创建新的拨号器实例
func TestNewDialer(t *testing.T) {
	// 使用默认选项创建拨号器
	dialer, err := NewDialer(DefaultOptions)
	require.NoError(t, err, "使用默认选项创建拨号器应该成功")
	require.NotNil(t, dialer, "拨号器实例不应为nil")
	
	// 验证拨号器各部分是否正确初始化
	require.NotNil(t, dialer.dnsclient, "DNS客户端应该已初始化")
	require.NotNil(t, dialer.networkpolicy, "网络策略应该已初始化")
	require.NotNil(t, dialer.dialer, "标准拨号器应该已初始化")
	
	// 验证配置选项是否正确应用
	require.Equal(t, &DefaultOptions, dialer.options, "拨号器应使用提供的选项")
	
	// 完成后关闭拨号器（释放资源）
	dialer.Close()
}

// TestGetHMAPDBType 测试获取混合映射数据库类型函数
func TestGetHMAPDBType(t *testing.T) {
	// 测试LevelDB类型
	options := DefaultOptions
	options.DiskDbType = LevelDB
	dbType := getHMAPDBType(options)
	require.Equal(t, dbType, hybrid.LevelDB, "应该返回LevelDB类型")
	
	// 测试Pogreb类型
	options.DiskDbType = Pogreb
	dbType = getHMAPDBType(options)
	require.Equal(t, dbType, hybrid.PogrebDB, "应该返回Pogreb类型")
}

// TestGetDNSData 测试获取DNS数据
func TestGetDNSData(t *testing.T) {
	// 创建拨号器
	dialer, err := NewDialer(DefaultOptions)
	require.NoError(t, err, "创建拨号器应该成功")
	defer dialer.Close()
	
	// 测试IPv4地址
	data, err := dialer.GetDNSData("127.0.0.1")
	require.NoError(t, err, "解析本地IPv4地址应该成功")
	require.NotNil(t, data, "DNS数据不应为nil")
	require.Equal(t, []string{"127.0.0.1"}, data.A, "IPv4地址应正确解析")
	require.Empty(t, data.AAAA, "IPv4地址不应有AAAA记录")
	
	// 测试IPv6地址
	data, err = dialer.GetDNSData("::1")
	require.NoError(t, err, "解析本地IPv6地址应该成功")
	require.NotNil(t, data, "DNS数据不应为nil")
	require.Equal(t, []string{"::1"}, data.AAAA, "IPv6地址应正确解析")
	require.Empty(t, data.A, "IPv6地址不应有A记录")
	
	// 测试IPv6格式 [::1]
	data, err = dialer.GetDNSData("[::1]")
	require.NoError(t, err, "解析带方括号的IPv6地址应该成功")
	require.NotNil(t, data, "DNS数据不应为nil")
	require.Equal(t, []string{"::1"}, data.AAAA, "带方括号的IPv6地址应正确解析")
	
	// 测试域名解析
	gob.Register(&dns.AAAA{})
	gob.Register(&dns.OPT{})
	data, err = dialer.GetDNSData("example.com")
	require.NoError(t, err, "解析域名应该成功")
	require.NotNil(t, data, "DNS数据不应为nil")
	require.True(t, len(data.A) > 0 || len(data.AAAA) > 0, "应该解析出至少一个IP地址")
}

// TestDial 测试基本的拨号功能
func TestDial(t *testing.T) {
	// 创建标准拨号器
	dialer, err := NewDialer(DefaultOptions)
	require.NoError(t, err, "创建拨号器应该成功")
	defer dialer.Close()
	
	// 尝试连接本地回环地址
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	
	// 启动简单的TCP服务器
	listener, err := net.Listen("tcp", "127.0.0.1:0")
	require.NoError(t, err, "创建本地TCP监听器应该成功")
	defer listener.Close()
	
	// 拨号到服务器
	conn, err := dialer.Dial(ctx, "tcp", listener.Addr().String())
	if err != nil {
		t.Logf("拨号错误: %v", err)
	} else {
		require.NotNil(t, conn, "连接不应为nil")
		conn.Close()
	}
}

// TestGetDialedIP 测试获取拨号IP功能
func TestGetDialedIP(t *testing.T) {
	// 创建带拨号历史记录的拨号器
	options := DefaultOptions
	options.WithDialerHistory = true
	dialer, err := NewDialer(options)
	require.NoError(t, err, "创建拨号器应该成功")
	defer dialer.Close()
	
	// 未拨号时应返回空
	ip := dialer.GetDialedIP("baidu.com")
	require.Equal(t, "", ip, "未拨号主机应返回空IP")
	
	// 禁用拨号历史记录的拨号器
	options.WithDialerHistory = false
	dialerWithoutHistory, err := NewDialer(options)
	require.NoError(t, err, "创建无历史记录的拨号器应该成功")
	defer dialerWithoutHistory.Close()
	
	// 禁用历史记录时应返回空
	ip = dialerWithoutHistory.GetDialedIP("example.com")
	require.Equal(t, "", ip, "禁用历史记录时应返回空IP")
} 

func TestFastDialerIP(t *testing.T) {

	options := DefaultOptions
	dialer, err := NewDialer(options)
	require.NoError(t, err, "创建拨号器应该成功")
	defer dialer.Close()

	t.Run("Dial TCP IP", func(t *testing.T) {
		t.Parallel()
		// scanme.sh ip
		conn, err := dialer.Dial(context.TODO(), "tcp", "***************:80")
		require.Nil(t, err)
		require.NotNil(t, conn)
		_ = conn.Close()
	})

	t.Run("Dial UDP IP", func(t *testing.T) {
		t.Parallel()
		// scanme.sh ip
		conn, err := dialer.Dial(context.TODO(), "udp", "***************:53")
		require.Nil(t, err)
		require.NotNil(t, conn)
		_ = conn.Close()
	})
}