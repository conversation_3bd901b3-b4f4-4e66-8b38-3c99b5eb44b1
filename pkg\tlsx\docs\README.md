# TLS扫描库文档

本目录包含了 `yaml_scan/pkg/tlsx` 包的完整技术文档，提供了结构化、可视化的文档体验。

## 📚 文档结构

### 🌟 主要文档

- **[index.html](index.html)** - 完整技术文档
  - 详细的功能介绍和架构说明
  - 完整的API文档和使用示例
  - 丰富的图表和可视化内容
  - 配置选项详解和性能优化建议

- **[quick-reference.html](quick-reference.html)** - 快速参考手册
  - 精简的API参考
  - 常用配置示例
  - 性能调优建议
  - 适合快速查阅

## 🎨 文档特色

### 视觉设计
- **🌈 丰富色彩** - 使用现代化的渐变色彩方案
- **📱 响应式设计** - 支持桌面和移动设备
- **🎯 清晰布局** - 结构化的信息展示
- **✨ 动画效果** - 平滑的交互体验

### 技术特性
- **📊 Mermaid图表** - 架构图和流程图
- **💻 代码高亮** - Prism.js语法高亮
- **📋 复制功能** - 一键复制代码示例
- **🔗 平滑滚动** - 流畅的页面导航

## 🚀 快速开始

### 在线查看
直接在浏览器中打开HTML文件即可查看文档：

```bash
# 查看完整文档
open pkg/tlsx/docs/index.html

# 查看快速参考
open pkg/tlsx/docs/quick-reference.html
```

### 本地服务器
如果需要完整的功能体验（如Mermaid图表），建议使用本地服务器：

```bash
# 使用Python启动简单服务器
cd pkg/tlsx/docs
python -m http.server 8080

# 或使用Node.js
npx http-server -p 8080

# 然后访问 http://localhost:8080
```

## 📖 文档内容

### 完整文档 (index.html)

1. **项目概述**
   - 核心优势和特性介绍
   - 应用场景说明

2. **系统架构**
   - 分层架构设计图
   - 数据流程图
   - 组件关系说明

3. **功能特性**
   - 证书分析功能
   - 版本和密码套件枚举
   - 指纹识别技术
   - 安全检查能力

4. **API文档**
   - 核心API接口说明
   - 配置选项详解
   - 返回值结构说明

5. **使用示例**
   - 快速开始示例
   - 高级功能演示
   - 批量扫描示例

6. **配置选项**
   - 扫描模式对比
   - 性能调优建议
   - 安全检查配置
   - 输出格式示例

### 快速参考 (quick-reference.html)

- 🚀 快速开始代码
- 🔧 核心API列表
- ⚙️ 扫描模式对比
- ✨ 主要功能清单
- 📋 常用配置模板
- ⚡ 性能建议表格
- 🛡️ 证书检查选项
- 📄 输出格式配置

## 🎯 使用建议

### 新用户
1. 先查看 **快速参考** 了解基本概念
2. 运行 **示例代码** 快速上手
3. 查看 **完整文档** 深入了解

### 开发者
1. 查看 **API文档** 了解接口详情
2. 参考 **配置选项** 进行定制
3. 使用 **性能建议** 优化应用

### 运维人员
1. 关注 **安全检查** 功能
2. 了解 **批量扫描** 能力
3. 掌握 **输出格式** 配置

## 🔧 技术栈

- **HTML5** - 现代化的文档结构
- **CSS3** - 响应式设计和动画效果
- **JavaScript** - 交互功能和代码复制
- **Mermaid.js** - 图表和流程图渲染
- **Prism.js** - 代码语法高亮
- **Font Awesome** - 图标库

## 📝 更新日志

- **v1.0** (2025-07-01)
  - 初始版本发布
  - 完整的技术文档
  - 快速参考手册
  - 响应式设计
  - 代码高亮和复制功能

## 🤝 贡献指南

欢迎对文档进行改进：

1. **内容更新** - 补充或修正技术内容
2. **样式优化** - 改进视觉设计和用户体验
3. **功能增强** - 添加新的交互功能
4. **多语言支持** - 提供英文版本

## 📞 联系我们

- **GitHub**: [yaml_scan项目](https://github.com/yaml_scan)
- **文档问题**: 请在项目中提交Issue
- **功能建议**: 欢迎提交Pull Request

---

*本文档使用现代化的Web技术构建，为开发者提供最佳的阅读体验。*
