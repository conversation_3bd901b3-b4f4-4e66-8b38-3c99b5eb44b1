// Package output 提供TLS扫描结果的输出处理功能
// 支持JSON和标准格式的输出，包含颜色高亮、文件输出和去重功能
// 负责将TLS扫描结果格式化为用户友好的输出格式
package output

import (
	"bytes"                                 // 字节缓冲区操作
	"fmt"                                   // 格式化输出
	"os"                                    // 操作系统接口
	"regexp"                                // 正则表达式
	"strings"                               // 字符串操作
	"sync"                                  // 同步原语
	"yaml_scan/pkg/tlsx/tlsx/clients"       // TLS客户端响应结构
	mapsutil "yaml_scan/utils/maps"         // 映射工具

	jsoniter "github.com/json-iterator/go"  // 高性能JSON库
	errorutil "yaml_scan/utils/errors"      // 错误处理工具
	"github.com/logrusorgru/aurora"         // 终端颜色库
	"golang.org/x/exp/maps"                 // 映射操作扩展
)

var (
	// globalDedupe 全局域名去重映射表
	// 用于在使用`-dns`标志时显示唯一域名，避免重复输出
	// TLS JSON结构已经包含每个证书的唯一域名
	// 此全局去重主要用于CLI模式下的多输入场景
	// 例如：google.com和youtube.com可能有相同的通配符证书或重叠域名
	globalDedupe = mapsutil.NewSyncLockMap[string, struct{}]()
)

// Writer 输出写入器接口
// 定义了将TLS扫描结果写入到文件和/或屏幕的标准接口
type Writer interface {
	// Close 关闭输出写入器接口
	// 释放相关资源，如文件句柄等
	Close() error

	// Write 将TLS扫描响应事件写入到文件和/或屏幕
	// 根据配置选择JSON或标准格式输出
	Write(*clients.Response) error
}

// decolorizerRegex 用于移除ANSI颜色代码的正则表达式
// 在写入文件时需要移除颜色代码，保持文件内容的纯文本格式
var decolorizerRegex = regexp.MustCompile(`\x1B\[[0-9;]*[a-zA-Z]`)

// StandardWriter 标准输出写入器结构
// 实现Writer接口，提供TLS扫描结果的格式化和输出功能
type StandardWriter struct {
	json        bool          // 是否使用JSON格式输出
	aurora      aurora.Aurora // 颜色高亮处理器，用于终端彩色输出
	outputFile  *fileWriter   // 文件输出写入器，可选
	outputMutex *sync.Mutex   // 输出互斥锁，确保并发安全

	options *clients.Options  // 扫描配置选项，控制输出内容和格式
}

// New 创建新的输出写入器实例
// 根据配置选项初始化输出写入器，支持屏幕和文件输出
//
// 参数:
//   - options: TLS扫描的配置选项，包含输出格式和文件路径等设置
//
// 返回值:
//   - Writer: 输出写入器接口实例
//   - error: 初始化过程中的错误，成功时为nil
//
// 初始化内容:
//   - JSON格式设置：根据options.JSON决定输出格式
//   - 颜色支持：根据options.NoColor决定是否启用颜色
//   - 文件输出：如果指定了输出文件则创建文件写入器
//   - 并发安全：初始化互斥锁保证多协程输出安全
func New(options *clients.Options) (Writer, error) {
	var outputFile *fileWriter

	// 如果指定了输出文件，创建文件写入器
	if options.OutputFile != "" {
		output, err := newFileOutputWriter(options.OutputFile)
		if err != nil {
			return nil, errorutil.NewWithErr(err).Msgf("could not create output file")
		}
		outputFile = output
	}

	// 创建标准写入器实例
	writer := &StandardWriter{
		json:        options.JSON,                    // 设置JSON输出模式
		aurora:      aurora.NewAurora(!options.NoColor), // 设置颜色支持（NoColor取反）
		outputFile:  outputFile,                      // 设置文件输出器
		outputMutex: &sync.Mutex{},                   // 初始化输出互斥锁
		options:     options,                         // 保存配置选项
	}
	return writer, nil
}

// Write 将TLS扫描响应事件写入到文件和/或屏幕
// 该方法是输出处理的核心，负责格式化数据并输出到指定目标
//
// 参数:
//   - event: TLS扫描响应事件，包含证书信息、连接详情等
//
// 返回值:
//   - error: 写入过程中的错误，成功时为nil
//
// 输出流程:
//   1. 根据配置选择JSON或标准格式进行格式化
//   2. 处理空数据情况（去重导致的空输出）
//   3. 使用互斥锁确保并发输出的安全性
//   4. 同时输出到标准输出和文件（如果配置）
//   5. 文件输出时移除颜色代码保持纯文本
func (w *StandardWriter) Write(event *clients.Response) error {
	var data []byte
	var err error

	// 根据配置选择输出格式
	if w.json {
		data, err = w.formatJSON(event)      // JSON格式化
	} else {
		data, err = w.formatStandard(event)  // 标准格式化
	}
	if err != nil {
		return errorutil.NewWithErr(err).Msgf("could not format output")
	}

	// 移除末尾换行符，统一处理换行
	data = bytes.TrimSuffix(data, []byte("\n"))
	if len(data) == 0 {
		// 当使用-dns标志且两个域名有相同证书时会发生去重，导致空数据
		return nil
	}

	// 使用互斥锁确保并发输出安全
	w.outputMutex.Lock()
	defer w.outputMutex.Unlock()

	// 输出到标准输出（屏幕）
	_, _ = os.Stdout.Write(data)
	_, _ = os.Stdout.Write([]byte("\n"))

	// 输出到文件（如果配置了文件输出）
	if w.outputFile != nil {
		// 非JSON格式需要移除颜色代码，保持文件内容为纯文本
		if !w.json {
			data = decolorizerRegex.ReplaceAll(data, []byte(""))
		}
		if writeErr := w.outputFile.Write(data); writeErr != nil {
			return errorutil.NewWithErr(err).Msgf("could not write to output")
		}
	}
	return nil
}

// Close 关闭输出写入器
// 释放相关资源，主要是关闭文件写入器
//
// 返回值:
//   - error: 关闭过程中的错误，成功时为nil
//
// 清理操作:
//   - 关闭文件写入器（如果存在）
//   - 确保所有缓冲数据都被刷新到磁盘
func (w *StandardWriter) Close() error {
	var err error
	if w.outputFile != nil {
		err = w.outputFile.Close() // 关闭文件写入器
	}
	return err
}

// formatJSON 将输出格式化为JSON格式
// 使用高性能的jsoniter库进行JSON序列化
//
// 参数:
//   - output: TLS扫描响应结构
//
// 返回值:
//   - []byte: JSON格式的字节数据
//   - error: 序列化过程中的错误
//
// JSON输出特点:
//   - 包含完整的扫描结果信息
//   - 便于程序化处理和解析
//   - 保持结构化数据的完整性
func (w *StandardWriter) formatJSON(output *clients.Response) ([]byte, error) {
	return jsoniter.Marshal(output)
}

// formatStandard 将输出格式化为标准客户端格式
// 根据配置选项生成用户友好的文本输出，支持颜色高亮
//
// 参数:
//   - output: TLS扫描响应结构
//
// 返回值:
//   - []byte: 格式化后的文本数据
//   - error: 格式化过程中的错误
//
// 格式化特性:
//   - 支持多种输出模式（DNS、SAN、CN等）
//   - 颜色高亮显示不同类型的信息
//   - 条件性显示各种证书属性和状态
//   - 支持密码套件枚举和版本枚举的特殊格式
func (w *StandardWriter) formatStandard(output *clients.Response) ([]byte, error) {
	// 验证输出数据的有效性
	if output == nil {
		return nil, errorutil.New("empty certificate response")
	}

	if output.CertificateResponse == nil {
		return nil, errorutil.New("empty leaf certificate")
	}
	cert := output.CertificateResponse
	builder := &bytes.Buffer{} // 用于构建输出字符串的缓冲区

	// DNS模式：仅显示证书中的域名列表
	if w.options.DisplayDns {
		for _, hname := range cert.Domains {
			// 使用全局去重避免重复域名输出
			if _, ok := globalDedupe.Get(hname); ok {
				continue // 域名已存在，跳过
			}
			_ = globalDedupe.Set(hname, struct{}{}) // 标记域名为已处理
			builder.WriteString(hname)
			builder.WriteString("\n")
		}
		outputdata := builder.Bytes()
		return outputdata, nil
	}

	// 构建输出前缀：主机名和端口信息
	if !w.options.RespOnly {
		builder.WriteString(output.Host)  // 主机名
		builder.WriteString(":")
		builder.WriteString(output.Port)  // 端口号

		// 多IP扫描模式下显示实际连接的IP地址
		if (w.options.ScanAllIPs || len(w.options.IPVersion) > 0) && output.IP != "" {
			builder.WriteString(" (")
			builder.WriteString(output.IP)  // 实际IP地址
			builder.WriteString(")")
		}
	}
	outputPrefix := builder.String() // 保存输出前缀
	builder.Reset()                  // 重置缓冲区用于后续内容

	// 处理证书名称输出（SAN和CN）
	var names []string
	if w.options.SAN {
		// 添加主题备用名称（Subject Alternative Names）
		names = append(names, cert.SubjectAN...)
	}
	if w.options.CN {
		// 添加通用名称（Common Name）
		names = append(names, cert.SubjectCN)
	}

	// 对证书名称进行去重和规范化处理
	uniqueNames := uniqueNormalizeCertNames(names)
	if len(uniqueNames) > 0 {
		for _, name := range uniqueNames {
			if w.options.RespOnly {
				// 仅响应模式：只输出名称，不包含前缀
				builder.WriteString(name)
				builder.WriteString("\n")
			} else {
				// 标准模式：包含前缀和颜色高亮
				builder.WriteString(outputPrefix)
				builder.WriteString(" [")
				builder.WriteString(w.aurora.Cyan(name).String()) // 青色高亮显示名称
				builder.WriteString("]\n")
			}
		}
	}

	// 基础输出：当没有特殊显示选项时，显示基本的主机端口信息
	if !w.options.SAN && !w.options.CN && !w.options.TlsCiphersEnum {
		builder.WriteString(outputPrefix)
	}

	// 探测状态显示
	if !output.ProbeStatus {
		// 探测失败：红色显示"failed"
		builder.WriteString(" [")
		builder.WriteString(w.aurora.Red("failed").String())
		builder.WriteString("]")
	}
	if w.options.ProbeStatus && output.ProbeStatus {
		// 探测成功：绿色显示"success"（仅在启用状态显示时）
		builder.WriteString(" [")
		builder.WriteString(w.aurora.Green("success").String())
		builder.WriteString("]")
	}

	// 服务器名称指示（SNI）显示
	if w.options.ServerName != nil {
		builder.WriteString(" [")
		builder.WriteString(w.aurora.Blue(output.ServerName).String()) // 蓝色显示SNI
		builder.WriteString("]")
	}

	// 证书主题组织信息显示
	if w.options.SO && len(cert.SubjectOrg) > 0 {
		builder.WriteString(" [")
		// 亮黄色显示组织信息，多个组织用逗号分隔
		builder.WriteString(w.aurora.BrightYellow(strings.Join(cert.SubjectOrg, ",")).String())
		builder.WriteString("]")
	}

	// TLS版本显示
	if w.options.TLSVersion {
		builder.WriteString(" [")
		// 蓝色显示TLS版本（转为大写）
		builder.WriteString(w.aurora.Blue(strings.ToUpper(output.Version)).String())
		builder.WriteString("]")
	}

	// 密码套件显示
	if w.options.Cipher {
		builder.WriteString(" [")
		builder.WriteString(w.aurora.Green(output.Cipher).String()) // 绿色显示密码套件
		builder.WriteString("]")
	}
	// 证书状态标识显示

	// 过期证书标识
	if w.options.Expired && cert.Expired {
		builder.WriteString(" [")
		builder.WriteString(w.aurora.Red("expired").String()) // 红色显示"expired"
		builder.WriteString("]")
	}

	// 自签名证书标识
	if w.options.SelfSigned && cert.SelfSigned {
		builder.WriteString(" [")
		builder.WriteString(w.aurora.Yellow("self-signed").String()) // 黄色显示"self-signed"
		builder.WriteString("]")
	}

	// 主机名不匹配标识
	if w.options.MisMatched && cert.MisMatched {
		builder.WriteString(" [")
		builder.WriteString(w.aurora.Yellow("mismatched").String()) // 黄色显示"mismatched"
		builder.WriteString("]")
	}

	// 已撤销证书标识
	if w.options.Revoked && cert.Revoked {
		builder.WriteString(" [")
		builder.WriteString(w.aurora.Red("revoked").String()) // 红色显示"revoked"
		builder.WriteString("]")
	}

	// 不受信任证书标识
	if w.options.Untrusted && cert.Untrusted {
		builder.WriteString(" [")
		builder.WriteString(w.aurora.Yellow("untrusted").String()) // 黄色显示"untrusted"
		builder.WriteString("]")
	}

	// 通配符证书标识
	if w.options.WildcardCertCheck && cert.WildCardCert {
		builder.WriteString(" [")
		builder.WriteString(w.aurora.Yellow("wildcard").String()) // 黄色显示"wildcard"
		builder.WriteString("]")
	}

	// 证书序列号显示
	if w.options.Serial {
		builder.WriteString(" [")
		builder.WriteString(w.aurora.BrightCyan(cert.Serial).String()) // 亮青色显示序列号
		builder.WriteString("]")
	}
	// 证书指纹哈希显示
	if w.options.Hash != "" {
		// 支持多种哈希算法，用逗号分隔
		hashOpts := strings.Split(w.options.Hash, ",")

		for _, hash := range hashOpts {
			var value string
			builder.WriteString(" [")

			// 根据指定的哈希类型选择相应的指纹值
			switch hash {
			case "md5":
				value = cert.FingerprintHash.MD5     // MD5指纹
			case "sha1":
				value = cert.FingerprintHash.SHA1    // SHA1指纹
			case "sha256":
				value = cert.FingerprintHash.SHA256  // SHA256指纹
			}
			// 亮洋红色显示哈希值
			builder.WriteString(w.aurora.BrightMagenta(value).String())
			builder.WriteString("]")
		}
	}

	// JARM指纹显示（TLS服务器指纹识别）
	if w.options.Jarm && output.JarmHash != "" {
		builder.WriteString(" [")
		builder.WriteString(w.aurora.Magenta(output.JarmHash).String()) // 洋红色显示JARM哈希
		builder.WriteString("]")
	}

	// JA3指纹显示（TLS客户端指纹识别）
	if w.options.Ja3 && output.Ja3Hash != "" {
		builder.WriteString(" [")
		builder.WriteString(w.aurora.Magenta(output.Ja3Hash).String()) // 洋红色显示JA3哈希
		builder.WriteString("]")
	}

	// JA3S指纹显示（TLS服务器响应指纹识别）
	if w.options.Ja3s && output.Ja3sHash != "" {
		builder.WriteString(" [")
		builder.WriteString(w.aurora.Magenta(output.Ja3sHash).String()) // 洋红色显示JA3S哈希
		builder.WriteString("]")
	}

	// 密码套件枚举结果显示
	if w.options.TlsCiphersEnum {
		for _, v := range output.TlsCiphers {
			// 获取按安全级别分类的密码套件，并应用颜色编码
			ct := v.Ciphers.ColorCode(w.aurora)
			all := []string{}

			// 按安全级别顺序收集所有密码套件
			all = append(all, ct.Insecure...) // 不安全的密码套件
			all = append(all, ct.Weak...)     // 弱密码套件
			all = append(all, ct.Secure...)   // 安全的密码套件
			all = append(all, ct.Unknown...)  // 未知安全级别的密码套件

			// 如果有密码套件，显示版本和密码套件列表
			if len(all) > 0 {
				builder.WriteString(outputPrefix)
				// 格式：[TLS版本] [密码套件列表]
				builder.WriteString(fmt.Sprintf(" [%v] [%v]\n",
					w.aurora.Magenta(v.Version),        // 洋红色显示TLS版本
					strings.Join(all, ",")))           // 逗号分隔的密码套件列表
			}
		}
	} else if w.options.TlsVersionsEnum {
		// TLS版本枚举结果显示
		builder.WriteString(" [")
		// 洋红色显示支持的TLS版本列表（逗号分隔）
		builder.WriteString(w.aurora.Magenta(strings.Join(output.VersionEnum, ",")).String())
		builder.WriteString("]")
	}

	outputdata := builder.Bytes()
	return outputdata, nil
}

// uniqueNormalizeCertNames 从证书备用名称中移除通配符并去重
// 该函数处理证书中的域名列表，移除通配符前缀并返回唯一的域名列表
//
// 参数:
//   - names: 证书中的域名列表，可能包含通配符域名
//
// 返回值:
//   - []string: 去重和规范化后的域名列表
//
// 处理逻辑:
//   - 移除通配符前缀"*."（如*.example.com -> example.com）
//   - 使用map进行去重处理
//   - 返回唯一的域名列表
//
// 应用场景:
//   - 证书SAN和CN字段的规范化处理
//   - 避免通配符域名的重复显示
//   - 提供清洁的域名输出
func uniqueNormalizeCertNames(names []string) []string {
	unique := make(map[string]struct{})
	for _, value := range names {
		// 移除通配符前缀"*."
		replaced := strings.Replace(value, "*.", "", -1)
		unique[replaced] = struct{}{} // 使用map进行去重
	}
	return maps.Keys(unique) // 返回去重后的域名列表
}