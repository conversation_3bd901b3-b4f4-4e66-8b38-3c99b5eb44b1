<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>pkg/tlsx - TLS扫描库技术文档</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #7c3aed;
            --success-color: #059669;
            --warning-color: #d97706;
            --danger-color: #dc2626;
            --info-color: #0891b2;
            --dark-color: #1f2937;
            --light-color: #f8fafc;
            --border-color: #e5e7eb;
            --text-color: #374151;
            --text-muted: #6b7280;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .header h1 {
            font-size: 3rem;
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }

        .header .subtitle {
            font-size: 1.2rem;
            color: var(--text-muted);
            margin-bottom: 20px;
        }

        .badges {
            display: flex;
            justify-content: center;
            gap: 10px;
            flex-wrap: wrap;
        }

        .badge {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            color: white;
            text-decoration: none;
            transition: transform 0.2s;
        }

        .badge:hover {
            transform: translateY(-2px);
        }

        .badge.go { background: linear-gradient(45deg, #00ADD8, #5DC9E2); }
        .badge.security { background: linear-gradient(45deg, var(--danger-color), #f56565); }
        .badge.network { background: linear-gradient(45deg, var(--success-color), #48bb78); }
        .badge.crypto { background: linear-gradient(45deg, var(--warning-color), #ed8936); }

        .nav {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .nav ul {
            list-style: none;
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 20px;
        }

        .nav a {
            color: var(--text-color);
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 10px;
            transition: all 0.3s;
            font-weight: 500;
        }

        .nav a:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-2px);
        }

        .section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .section h2 {
            font-size: 2.5rem;
            margin-bottom: 20px;
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .section h3 {
            font-size: 1.8rem;
            color: var(--primary-color);
            margin: 30px 0 15px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin: 30px 0;
        }

        .feature-card {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 15px;
            padding: 25px;
            border-left: 5px solid var(--primary-color);
            transition: transform 0.3s, box-shadow 0.3s;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }

        .feature-card h4 {
            color: var(--primary-color);
            margin-bottom: 10px;
            font-size: 1.3rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .code-block {
            background: #2d3748;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            overflow-x: auto;
            position: relative;
        }

        .code-block::before {
            content: attr(data-lang);
            position: absolute;
            top: 10px;
            right: 15px;
            background: var(--primary-color);
            color: white;
            padding: 4px 8px;
            border-radius: 5px;
            font-size: 0.8rem;
        }

        .api-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .api-table th {
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 15px;
            text-align: left;
        }

        .api-table td {
            padding: 15px;
            border-bottom: 1px solid var(--border-color);
        }

        .api-table tr:hover {
            background: #f8fafc;
        }

        .footer {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            color: var(--text-muted);
        }

        @media (max-width: 768px) {
            .header h1 { font-size: 2rem; }
            .section { padding: 20px; }
            .nav ul { flex-direction: column; text-align: center; }
        }

        .mermaid {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <header class="header">
            <h1><i class="fas fa-shield-alt"></i> pkg/tlsx</h1>
            <p class="subtitle">yaml_scan项目TLS扫描库 - 完整技术文档</p>
            <div class="badges">
                <span class="badge go"><i class="fab fa-golang"></i> Go语言</span>
                <span class="badge security"><i class="fas fa-lock"></i> TLS安全</span>
                <span class="badge network"><i class="fas fa-certificate"></i> 证书分析</span>
                <span class="badge crypto"><i class="fas fa-fingerprint"></i> 指纹识别</span>
            </div>
        </header>

        <!-- 导航 -->
        <nav class="nav">
            <ul>
                <li><a href="#overview"><i class="fas fa-eye"></i> 包概述</a></li>
                <li><a href="#structure"><i class="fas fa-sitemap"></i> 包结构</a></li>
                <li><a href="#api"><i class="fas fa-code"></i> 核心API</a></li>
                <li><a href="#clients"><i class="fas fa-plug"></i> 客户端实现</a></li>
                <li><a href="#configuration"><i class="fas fa-cog"></i> 配置选项</a></li>
                <li><a href="#examples"><i class="fas fa-play"></i> 使用示例</a></li>
                <li><a href="#output"><i class="fas fa-file-export"></i> 输出格式</a></li>
            </ul>
        </nav>

        <!-- 包概述 -->
        <section id="overview" class="section">
            <h2><i class="fas fa-eye"></i> pkg/tlsx 包概述</h2>
            <p style="font-size: 1.1rem; margin-bottom: 30px;">
                <strong>pkg/tlsx</strong> 是yaml_scan项目中的TLS安全扫描核心包，提供统一的TLS扫描和分析工具包。
                支持多种TLS实现方式（原生TLS、ztls、openssl和自动模式），可以进行TLS版本枚举、密码套件枚举、JARM指纹识别等功能。
                该包采用模块化设计，通过统一的接口抽象支持不同的TLS客户端实现。
            </p>

            <h3><i class="fas fa-star"></i> 核心特性</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4><i class="fas fa-plug" style="color: var(--primary-color);"></i> 多客户端支持</h4>
                    <ul>
                        <li><strong>ctls</strong> - Go原生crypto/tls实现，稳定性好</li>
                        <li><strong>ztls</strong> - ZMap zcrypto/tls实现，功能丰富</li>
                        <li><strong>openssl</strong> - OpenSSL命令行实现，兼容性强</li>
                        <li><strong>auto</strong> - 智能自动选择最佳实现</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4><i class="fas fa-certificate" style="color: var(--success-color);"></i> 证书分析</h4>
                    <ul>
                        <li>X.509证书解析和验证</li>
                        <li>证书过期状态检查</li>
                        <li>自签名证书检测</li>
                        <li>主机名匹配验证</li>
                        <li>证书撤销状态检查</li>
                        <li>通配符证书识别</li>
                        <li>不受信任CA检测</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4><i class="fas fa-fingerprint" style="color: var(--warning-color);"></i> 指纹识别</h4>
                    <ul>
                        <li><strong>JARM</strong> - TLS服务器指纹识别</li>
                        <li><strong>JA3</strong> - 客户端指纹哈希</li>
                        <li><strong>JA3S</strong> - 服务器响应指纹</li>
                        <li>证书MD5/SHA1/SHA256指纹</li>
                        <li>支持指纹数据库匹配</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4><i class="fas fa-list-ol" style="color: var(--danger-color);"></i> 协议枚举</h4>
                    <ul>
                        <li>TLS版本枚举（1.0-1.3）</li>
                        <li>密码套件枚举和分类</li>
                        <li>安全级别评估（安全/弱/不安全）</li>
                        <li>协议详细信息提取</li>
                        <li>客户端Hello/服务器Hello分析</li>
                    </ul>
                </div>
            </div>

            <h3><i class="fas fa-cogs"></i> 主要功能模块</h3>
            <div class="api-table">
                <table>
                    <thead>
                        <tr>
                            <th>模块</th>
                            <th>功能描述</th>
                            <th>主要接口</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>tlsx</strong></td>
                            <td>核心TLS服务，提供统一的扫描接口</td>
                            <td>New(), Connect(), ConnectWithOptions()</td>
                        </tr>
                        <tr>
                            <td><strong>runner</strong></td>
                            <td>批量扫描器，支持并发任务调度</td>
                            <td>New(), Execute(), Close()</td>
                        </tr>
                        <tr>
                            <td><strong>output</strong></td>
                            <td>输出处理器，支持多种格式输出</td>
                            <td>New(), Write(), Close()</td>
                        </tr>
                        <tr>
                            <td><strong>clients</strong></td>
                            <td>客户端接口定义和通用功能</td>
                            <td>Implementation接口，Options配置</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </section>

        <!-- 包结构 -->
        <section id="structure" class="section">
            <h2><i class="fas fa-sitemap"></i> 包结构详解</h2>

            <h3><i class="fas fa-folder-tree"></i> 目录结构</h3>
            <div class="code-block" data-lang="text">
<pre><code>pkg/tlsx/
├── tlsx/                    # 核心TLS服务模块
│   ├── tlsx.go             # 主服务接口实现
│   ├── clients/            # 客户端接口和通用功能
│   │   ├── clients.go      # Implementation接口定义
│   │   ├── options.go      # 配置选项结构
│   │   ├── response.go     # 响应数据结构
│   │   ├── ciphers.go      # 密码套件分类
│   │   └── util.go         # 工具函数
│   ├── tls/                # Go原生TLS客户端实现
│   ├── ztls/               # ZMap TLS客户端实现
│   │   ├── ztls.go         # 主实现文件
│   │   └── ja3/            # JA3指纹识别
│   ├── openssl/            # OpenSSL客户端实现
│   ├── auto/               # 自动选择客户端实现
│   └── jarm/               # JARM指纹识别
├── runner/                  # 批量扫描器
│   └── runner.go           # 扫描任务调度和管理
├── output/                  # 输出处理模块
│   ├── output.go           # 输出接口实现
│   ├── file_writer.go      # 文件输出写入器
│   └── stats/              # 统计信息收集
├── assets/                  # 资源文件
│   ├── cipherstatus_data.go # 密码套件安全级别数据
│   └── root_cert_data.go   # 根证书数据
├── example/                 # 使用示例
│   ├── main.go             # 完整使用示例
│   ├── quickstart.go       # 快速开始示例
│   ├── advanced.go         # 高级功能示例
│   └── config_example.go   # 配置示例
└── docs/                    # 文档目录
    ├── index.html          # 主文档（本文件）
    ├── api-reference.html  # API参考文档
    └── quick-reference.html # 快速参考</code></pre>
            </div>

            <h3><i class="fas fa-layer-group"></i> 核心模块说明</h3>
            <div class="api-table">
                <table>
                    <thead>
                        <tr>
                            <th>模块</th>
                            <th>文件路径</th>
                            <th>主要功能</th>
                            <th>关键类型</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>tlsx</strong></td>
                            <td>tlsx/tlsx.go</td>
                            <td>TLS扫描服务的主入口，提供统一的扫描接口</td>
                            <td>Service结构体</td>
                        </tr>
                        <tr>
                            <td><strong>clients</strong></td>
                            <td>tlsx/clients/</td>
                            <td>客户端接口定义、配置选项、响应结构</td>
                            <td>Implementation接口、Options、Response</td>
                        </tr>
                        <tr>
                            <td><strong>runner</strong></td>
                            <td>runner/runner.go</td>
                            <td>批量扫描任务的调度和执行管理</td>
                            <td>Runner结构体</td>
                        </tr>
                        <tr>
                            <td><strong>output</strong></td>
                            <td>output/output.go</td>
                            <td>扫描结果的格式化和输出处理</td>
                            <td>Writer接口</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <h3><i class="fas fa-plug"></i> 客户端实现模块</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4><i class="fab fa-golang" style="color: #00ADD8;"></i> tls模块</h4>
                    <p><strong>路径:</strong> <code>tlsx/tls/</code></p>
                    <p><strong>实现:</strong> Go标准库crypto/tls</p>
                    <ul>
                        <li>✅ 稳定性最高，生产环境首选</li>
                        <li>✅ 内存占用低，性能优秀</li>
                        <li>✅ 完整支持TLS 1.3</li>
                        <li>⚠️ 功能相对基础</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h4><i class="fas fa-microscope" style="color: #7c3aed;"></i> ztls模块</h4>
                    <p><strong>路径:</strong> <code>tlsx/ztls/</code></p>
                    <p><strong>实现:</strong> ZMap zcrypto/tls</p>
                    <ul>
                        <li>✅ 功能最丰富，分析能力强</li>
                        <li>✅ 宽松的ASN.1解析</li>
                        <li>✅ 详细的协议信息提取</li>
                        <li>✅ 支持JA3/JA3S指纹</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h4><i class="fas fa-terminal" style="color: #059669;"></i> openssl模块</h4>
                    <p><strong>路径:</strong> <code>tlsx/openssl/</code></p>
                    <p><strong>实现:</strong> OpenSSL命令行工具</p>
                    <ul>
                        <li>✅ 兼容性最强</li>
                        <li>✅ 支持特殊协议和配置</li>
                        <li>✅ 复杂证书链处理能力</li>
                        <li>⚠️ 依赖外部OpenSSL程序</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h4><i class="fas fa-magic" style="color: #f59e0b;"></i> auto模块</h4>
                    <p><strong>路径:</strong> <code>tlsx/auto/</code></p>
                    <p><strong>实现:</strong> 智能选择机制</p>
                    <ul>
                        <li>✅ 自动fallback机制</li>
                        <li>✅ 智能优化选择</li>
                        <li>✅ 适应复杂网络环境</li>
                        <li>✅ 减少手动配置</li>
                    </ul>
                </div>
            </div>
        </section>

        <!-- 核心API -->
        <section id="api" class="section">
            <h2><i class="fas fa-code"></i> 核心API接口</h2>

            <h3><i class="fas fa-server"></i> Service 核心服务</h3>
            <p>Service是tlsx包的核心结构体，封装了所有TLS扫描功能。</p>

            <div class="code-block" data-lang="go">
<pre><code class="language-go">// Service TLS扫描服务结构体
type Service struct {
    options *clients.Options       // 配置选项
    client  clients.Implementation // TLS客户端实现
}</code></pre>
            </div>

            <h4>func New(options *clients.Options) (*Service, error)</h4>
            <p>创建新的TLS扫描服务实例，根据配置选项初始化相应的客户端实现。</p>
            <div class="code-block" data-lang="go">
<pre><code class="language-go">// 创建基础TLS服务
options := &clients.Options{
    ScanMode: "ctls",  // 扫描模式：ctls/ztls/openssl/auto
    Timeout:  10,      // 连接超时时间（秒）
    Retries:  3,       // 重试次数
}

service, err := tlsx.New(options)
if err != nil {
    return err
}</code></pre>
            </div>

            <h4>func (s *Service) Connect(host, ip, port string) (*clients.Response, error)</h4>
            <p>连接到指定主机和端口，使用默认连接选项进行TLS握手。</p>
            <div class="code-block" data-lang="go">
<pre><code class="language-go">// 基础TLS连接
response, err := service.Connect("www.baidu.com", "", "443")
if err != nil {
    return err
}

fmt.Printf("TLS版本: %s\n", response.Version)
fmt.Printf("密码套件: %s\n", response.Cipher)
fmt.Printf("证书CN: %s\n", response.SubjectCN)</code></pre>
            </div>

            <h4>func (s *Service) ConnectWithOptions(host, ip, port string, options ConnectOptions) (*clients.Response, error)</h4>
            <p>使用自定义连接选项进行TLS连接，支持SNI、TLS版本指定、枚举模式等高级功能。</p>
            <div class="code-block" data-lang="go">
<pre><code class="language-go">// 带SNI的连接
connectOptions := clients.ConnectOptions{
    SNI:        "api.example.com",
    VersionTLS: "1.3",
    EnumMode:   clients.Version,
}

response, err := service.ConnectWithOptions(
    "example.com", "", "443", connectOptions)</code></pre>
            </div>

            <h3><i class="fas fa-interface"></i> Implementation 接口</h3>
            <p>所有TLS客户端实现必须遵循的统一接口规范。</p>
            <div class="code-block" data-lang="go">
<pre><code class="language-go">// Implementation TLS客户端接口
type Implementation interface {
    // 使用指定选项连接TLS服务器
    ConnectWithOptions(hostname, ip, port string, options ConnectOptions) (*Response, error)

    // 枚举服务器支持的密码套件
    EnumerateCiphers(hostname, ip, port string, options ConnectOptions) ([]string, error)

    // 获取客户端支持的TLS版本
    SupportedTLSVersions() ([]string, error)

    // 获取客户端支持的密码套件
    SupportedTLSCiphers() ([]string, error)
}</code></pre>
            </div>

            <h3><i class="fas fa-list"></i> Runner 批量扫描器</h3>
            <p>Runner提供批量扫描功能，支持并发处理多个目标。</p>

            <h4>func New(options *clients.Options) (*Runner, error)</h4>
            <p>创建批量扫描器实例，用于处理大量目标的并发扫描。</p>
            <div class="code-block" data-lang="go">
<pre><code class="language-go">// 批量扫描配置
options := &clients.Options{
    ScanMode:    "ctls",
    Concurrency: 10,                           // 并发数
    Ports:       []string{"443", "8443"},      // 扫描端口
    Inputs:      []string{"host1.com", "host2.com"}, // 目标列表
    JSON:        true,                         // JSON输出
    OutputFile:  "results.json",               // 输出文件
}

runner, err := runner.New(options)
if err != nil {
    return err
}
defer runner.Close()

// 执行批量扫描
err = runner.Execute()</code></pre>
            </div>

            <h3><i class="fas fa-file-export"></i> Output 输出处理</h3>
            <p>Output模块提供灵活的输出格式化和写入功能。</p>

            <h4>func New(options *clients.Options) (Writer, error)</h4>
            <p>创建输出写入器，支持多种输出格式和目标。</p>
            <div class="code-block" data-lang="go">
<pre><code class="language-go">// 创建JSON输出处理器
options := &clients.Options{
    JSON:       true,
    OutputFile: "results.json",
    NoColor:    true,
}

writer, err := output.New(options)
if err != nil {
    return err
}
defer writer.Close()

// 写入扫描结果
err = writer.Write(response)</code></pre>
            </div>
        </section>

        <!-- 客户端实现 -->
        <section id="clients" class="section">
            <h2><i class="fas fa-plug"></i> 客户端实现详解</h2>

            <h3><i class="fas fa-cogs"></i> 客户端实现对比</h3>
            <table class="api-table">
                <thead>
                    <tr>
                        <th>实现</th>
                        <th>文件位置</th>
                        <th>依赖库</th>
                        <th>主要特点</th>
                        <th>适用场景</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>tls</strong></td>
                        <td>tlsx/tls/</td>
                        <td>Go标准库crypto/tls</td>
                        <td>稳定、高性能、内存占用低</td>
                        <td>生产环境、基础扫描</td>
                    </tr>
                    <tr>
                        <td><strong>ztls</strong></td>
                        <td>tlsx/ztls/</td>
                        <td>github.com/zmap/zcrypto/tls</td>
                        <td>功能丰富、分析详细、支持指纹</td>
                        <td>安全研究、深度分析</td>
                    </tr>
                    <tr>
                        <td><strong>openssl</strong></td>
                        <td>tlsx/openssl/</td>
                        <td>OpenSSL命令行工具</td>
                        <td>兼容性强、功能全面</td>
                        <td>特殊协议、兼容性要求</td>
                    </tr>
                    <tr>
                        <td><strong>auto</strong></td>
                        <td>tlsx/auto/</td>
                        <td>智能选择机制</td>
                        <td>自动fallback、智能优化</td>
                        <td>自动化扫描、复杂环境</td>
                    </tr>
                </tbody>
            </table>

            <h3><i class="fas fa-code"></i> 客户端使用示例</h3>
            <div class="code-block" data-lang="go">
<pre><code class="language-go">// 1. 使用tls客户端（Go原生实现）
tlsOptions := &clients.Options{
    ScanMode: "ctls",  // 或 "tls"
    Timeout:  10,
}
service, _ := tlsx.New(tlsOptions)

// 2. 使用ztls客户端进行深度分析
ztlsOptions := &clients.Options{
    ScanMode:        "ztls",
    TlsVersionsEnum: true,    // 启用TLS版本枚举
    TlsCiphersEnum:  true,    // 启用密码套件枚举
    Jarm:            true,    // 启用JARM指纹
    Ja3:             true,    // 启用JA3指纹
    Ja3s:            true,    // 启用JA3S指纹
}
service, _ = tlsx.New(ztlsOptions)

// 3. 使用auto模式自动选择最佳实现
autoOptions := &clients.Options{
    ScanMode: "auto",
    Timeout:  15,
}
service, _ = tlsx.New(autoOptions)

// 4. 使用openssl处理特殊情况
opensslOptions := &clients.Options{
    ScanMode:      "openssl",
    OpenSSLBinary: "/usr/local/bin/openssl", // 自定义OpenSSL路径
    Timeout:       20,
}
service, _ = tlsx.New(opensslOptions)</code></pre>
            </div>

            <h3><i class="fas fa-fingerprint"></i> 指纹识别功能</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4><i class="fas fa-fingerprint" style="color: var(--primary-color);"></i> JARM指纹</h4>
                    <p><strong>位置:</strong> <code>tlsx/jarm/</code></p>
                    <ul>
                        <li>发送10个特制TLS握手包</li>
                        <li>分析服务器响应模式</li>
                        <li>生成62字符唯一哈希</li>
                        <li>用于TLS服务器识别和分类</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h4><i class="fas fa-user-secret" style="color: var(--success-color);"></i> JA3/JA3S指纹</h4>
                    <p><strong>位置:</strong> <code>tlsx/ztls/ja3/</code></p>
                    <ul>
                        <li>JA3: 客户端Hello消息指纹</li>
                        <li>JA3S: 服务器Hello消息指纹</li>
                        <li>提取TLS握手参数</li>
                        <li>生成MD5哈希标识</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h4><i class="fas fa-certificate" style="color: var(--warning-color);"></i> 证书指纹</h4>
                    <p><strong>算法:</strong> MD5/SHA1/SHA256</p>
                    <ul>
                        <li>MD5: 128位哈希（兼容性）</li>
                        <li>SHA1: 160位哈希（传统）</li>
                        <li>SHA256: 256位哈希（推荐）</li>
                        <li>用于证书唯一性识别</li>
                    </ul>
                </div>
            </div>

            <h3><i class="fas fa-shield-alt"></i> 证书验证功能</h3>
            <div class="code-block" data-lang="go">
<pre><code class="language-go">// 启用完整证书验证
options := &clients.Options{
    ScanMode:          "ztls",
    Expired:           true,  // 检查证书过期
    SelfSigned:        true,  // 检查自签名证书
    MisMatched:        true,  // 检查主机名不匹配
    Revoked:           true,  // 检查证书撤销状态
    Untrusted:         true,  // 检查不受信任CA
    WildcardCertCheck: true,  // 检查通配符证书
    Hash:              "md5,sha1,sha256", // 计算证书指纹
}

service, err := tlsx.New(options)
response, err := service.Connect("example.com", "", "443")

// 检查证书状态
if response.CertificateResponse != nil {
    cert := response.CertificateResponse
    fmt.Printf("证书过期: %t\n", cert.Expired)
    fmt.Printf("自签名: %t\n", cert.SelfSigned)
    fmt.Printf("主机名不匹配: %t\n", cert.MisMatched)
    fmt.Printf("已撤销: %t\n", cert.Revoked)
    fmt.Printf("不受信任: %t\n", cert.Untrusted)
    fmt.Printf("通配符证书: %t\n", cert.WildCardCert)
}</code></pre>
            </div>
        </section>

        <!-- 配置选项 -->
        <section id="configuration" class="section">
            <h2><i class="fas fa-cog"></i> 配置选项详解</h2>

            <h3><i class="fas fa-wrench"></i> Options 配置结构</h3>
            <p>clients.Options包含pkg/tlsx的所有配置参数，涵盖输入输出、连接参数、扫描模式等各个方面。</p>

            <div class="code-block" data-lang="go">
<pre><code class="language-go">type Options struct {
    // === 基础配置 ===
    ScanMode    string   // 扫描模式: ctls, ztls, openssl, auto
    Timeout     int      // 连接超时时间(秒)，默认10
    Retries     int      // 重试次数，默认3
    Concurrency int      // 并发数，默认25

    // === 输入输出配置 ===
    Inputs     goflags.StringSlice // 要扫描的目标列表
    InputList  string              // 包含目标列表的文件路径
    OutputFile string              // 输出文件路径
    Ports      goflags.StringSlice // 要扫描的端口列表

    // === TLS连接配置 ===
    ServerName               goflags.StringSlice // SNI列表
    RandomForEmptyServerName bool                // SNI为空时使用随机值
    ReversePtrSNI            bool                // 执行反向PTR查询获取SNI

    // === 输出控制选项 ===
    JSON       bool // JSON格式输出
    Verbose    bool // 详细输出模式
    Silent     bool // 静默模式
    NoColor    bool // 禁用颜色输出
    RespOnly   bool // 仅显示TLS响应数据

    // === 证书验证选项 ===
    Expired    bool // 检查证书过期状态
    SelfSigned bool // 检查自签名证书
    Untrusted  bool // 检查不受信任证书
    MisMatched bool // 检查主机名不匹配
    Revoked    bool // 检查证书撤销状态
    HardFail   bool // 撤销检查失败时视为已撤销

    // === 证书信息显示选项 ===
    SAN        bool   // 显示主题备用名称
    CN         bool   // 显示主题通用名称
    SO         bool   // 显示主题组织名称
    TLSVersion bool   // 显示TLS版本
    Cipher     bool   // 显示密码套件
    Serial     bool   // 显示证书序列号
    Hash       string // 证书哈希类型(md5/sha1/sha256)

    // === 指纹和哈希选项 ===
    Jarm bool // 计算JARM TLS指纹
    Ja3  bool // 显示JA3客户端指纹
    Ja3s bool // 显示JA3S服务器指纹
    Cert bool // 以PEM格式显示完整证书

    // === 高级扫描选项 ===
    WildcardCertCheck bool     // 启用通配符证书检查
    TlsVersionsEnum   bool     // 枚举所有支持的TLS版本
    TlsCiphersEnum    bool     // 枚举密码套件
    TLsCipherLevel    []string // 密码套件级别过滤器

    // === 协议详细信息选项 ===
    ClientHello bool // 包含客户端Hello消息详情
    ServerHello bool // 包含服务器Hello消息详情

    // === 网络配置 ===
    ScanAllIPs bool                  // 扫描域名的所有IP
    IPVersion  goflags.StringSlice   // IP版本(4/6)
    Resolvers  goflags.StringSlice   // 自定义DNS解析器

    // === 内部组件 ===
    Fastdialer *fastdialer.Dialer // 快速拨号器实例
}</code></pre>
            </div>

            <h3><i class="fas fa-layer-group"></i> 扫描模式详细对比</h3>
            <table class="api-table">
                <thead>
                    <tr>
                        <th>模式</th>
                        <th>实现库</th>
                        <th>性能</th>
                        <th>功能</th>
                        <th>稳定性</th>
                        <th>推荐场景</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><code>ctls</code></td>
                        <td>crypto/tls</td>
                        <td>⭐⭐⭐⭐⭐</td>
                        <td>⭐⭐⭐</td>
                        <td>⭐⭐⭐⭐⭐</td>
                        <td>生产环境、基础扫描</td>
                    </tr>
                    <tr>
                        <td><code>ztls</code></td>
                        <td>zcrypto/tls</td>
                        <td>⭐⭐⭐⭐</td>
                        <td>⭐⭐⭐⭐⭐</td>
                        <td>⭐⭐⭐⭐</td>
                        <td>安全研究、深度分析</td>
                    </tr>
                    <tr>
                        <td><code>openssl</code></td>
                        <td>OpenSSL CLI</td>
                        <td>⭐⭐⭐</td>
                        <td>⭐⭐⭐⭐</td>
                        <td>⭐⭐⭐⭐</td>
                        <td>特殊协议、兼容性</td>
                    </tr>
                    <tr>
                        <td><code>auto</code></td>
                        <td>智能选择</td>
                        <td>⭐⭐⭐⭐</td>
                        <td>⭐⭐⭐⭐</td>
                        <td>⭐⭐⭐⭐⭐</td>
                        <td>自动化、复杂环境</td>
                    </tr>
                </tbody>
            </table>

            <h3><i class="fas fa-sliders-h"></i> 性能调优参数</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4><i class="fas fa-tachometer-alt" style="color: var(--success-color);"></i> 并发控制</h4>
                    <ul>
                        <li><strong>Concurrency</strong>: 并发线程数</li>
                        <li><strong>小规模</strong>: 1-5个并发</li>
                        <li><strong>中等规模</strong>: 5-20个并发</li>
                        <li><strong>大规模</strong>: 20-50个并发</li>
                        <li><strong>注意</strong>: 过高并发可能导致网络拥塞</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h4><i class="fas fa-clock" style="color: var(--warning-color);"></i> 超时设置</h4>
                    <ul>
                        <li><strong>Timeout</strong>: 连接超时时间</li>
                        <li><strong>快速扫描</strong>: 5-10秒</li>
                        <li><strong>标准扫描</strong>: 10-15秒</li>
                        <li><strong>深度分析</strong>: 15-30秒</li>
                        <li><strong>JARM指纹</strong>: 建议20秒以上</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h4><i class="fas fa-redo" style="color: var(--info-color);"></i> 重试策略</h4>
                    <ul>
                        <li><strong>Retries</strong>: 失败重试次数</li>
                        <li><strong>稳定网络</strong>: 1-2次重试</li>
                        <li><strong>不稳定网络</strong>: 3-5次重试</li>
                        <li><strong>生产环境</strong>: 建议3次重试</li>
                        <li><strong>测试环境</strong>: 可适当增加</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h4><i class="fas fa-list" style="color: var(--danger-color);"></i> 目标管理</h4>
                    <ul>
                        <li><strong>Inputs</strong>: 目标主机列表</li>
                        <li><strong>Ports</strong>: 扫描端口列表</li>
                        <li><strong>支持格式</strong>: 域名、IP、CIDR</li>
                        <li><strong>批量处理</strong>: 支持文件输入</li>
                    </ul>
                </div>
            </div>
        </section>

        <!-- 使用示例 -->
        <section id="examples" class="section">
            <h2><i class="fas fa-play"></i> 使用示例</h2>

            <h3><i class="fas fa-rocket"></i> 基础TLS连接示例</h3>
            <p>演示如何使用pkg/tlsx进行基本的TLS连接和证书信息获取。</p>
            <div class="code-block" data-lang="go">
<pre><code class="language-go">package main

import (
    "fmt"
    "log"
    "yaml_scan/pkg/tlsx/tlsx"
    "yaml_scan/pkg/tlsx/tlsx/clients"
)

func main() {
    // 创建TLS扫描选项
    options := &clients.Options{
        ScanMode:    "ctls",    // 使用Go原生TLS实现
        Timeout:     10,        // 连接超时10秒
        Retries:     3,         // 重试3次
        Concurrency: 1,         // 单线程
    }

    // 创建TLS服务实例
    service, err := tlsx.New(options)
    if err != nil {
        log.Fatalf("创建TLS服务失败: %v", err)
    }

    // 连接到目标主机
    response, err := service.Connect("www.baidu.com", "", "443")
    if err != nil {
        log.Printf("连接失败: %v", err)
        return
    }

    // 输出基本信息
    fmt.Printf("主机: %s:%s\n", response.Host, response.Port)
    fmt.Printf("TLS版本: %s\n", response.Version)
    fmt.Printf("密码套件: %s\n", response.Cipher)
    fmt.Printf("探测状态: %t\n", response.ProbeStatus)

    // 输出证书信息
    if response.CertificateResponse != nil {
        cert := response.CertificateResponse
        fmt.Printf("证书主题CN: %s\n", cert.SubjectCN)
        fmt.Printf("证书组织: %v\n", cert.SubjectOrg)
        fmt.Printf("证书域名: %v\n", cert.SubjectAN)
        fmt.Printf("证书过期: %t\n", cert.Expired)
        fmt.Printf("自签名证书: %t\n", cert.SelfSigned)
        fmt.Printf("证书序列号: %s\n", cert.Serial)
    }
}</code></pre>
            </div>

            <h3><i class="fas fa-search"></i> TLS版本和密码套件枚举</h3>
            <p>演示如何使用ztls客户端进行TLS版本和密码套件的深度枚举。</p>
            <div class="code-block" data-lang="go">
<pre><code class="language-go">// 创建带枚举功能的TLS扫描选项
options := &clients.Options{
    ScanMode:         "ztls",  // 使用ztls实现，支持更好的枚举功能
    Timeout:          10,
    Retries:          2,
    Concurrency:      1,
    TlsVersionsEnum:  true,    // 启用TLS版本枚举
    TlsCiphersEnum:   true,    // 启用密码套件枚举
}

// 创建TLS服务实例
service, err := tlsx.New(options)
if err != nil {
    log.Fatalf("创建TLS服务失败: %v", err)
}

// 连接并进行枚举
response, err := service.Connect("www.baidu.com", "", "443")
if err != nil {
    log.Printf("连接失败: %v", err)
    return
}

// 输出枚举结果
fmt.Printf("主机: %s:%s\n", response.Host, response.Port)
fmt.Printf("支持的TLS版本: %v\n", response.VersionEnum)

// 输出密码套件信息
if len(response.TlsCiphers) > 0 {
    fmt.Println("支持的密码套件:")
    for _, tlsCipher := range response.TlsCiphers {
        fmt.Printf("  TLS版本 %s:\n", tlsCipher.Version)
        if len(tlsCipher.Ciphers.Secure) > 0 {
            fmt.Printf("    安全密码套件: %v\n", tlsCipher.Ciphers.Secure)
        }
        if len(tlsCipher.Ciphers.Weak) > 0 {
            fmt.Printf("    弱密码套件: %v\n", tlsCipher.Ciphers.Weak)
        }
        if len(tlsCipher.Ciphers.Insecure) > 0 {
            fmt.Printf("    不安全密码套件: %v\n", tlsCipher.Ciphers.Insecure)
        }
    }
}</code></pre>
            </div>

            <h3><i class="fas fa-fingerprint"></i> JARM指纹识别示例</h3>
            <p>演示如何启用JARM指纹识别功能来识别TLS服务器。</p>
            <div class="code-block" data-lang="go">
<pre><code class="language-go">// 创建带JARM指纹识别的TLS扫描选项
options := &clients.Options{
    ScanMode:    "auto",  // 使用自动模式
    Timeout:     15,      // JARM需要更长的超时时间
    Retries:     2,
    Concurrency: 1,
    Jarm:        true,    // 启用JARM指纹识别
}

// 创建TLS服务实例
service, err := tlsx.New(options)
if err != nil {
    log.Fatalf("创建TLS服务失败: %v", err)
}

// 连接并获取JARM指纹
response, err := service.Connect("www.baidu.com", "", "443")
if err != nil {
    log.Printf("连接失败: %v", err)
    return
}

// 输出JARM指纹
fmt.Printf("主机: %s:%s\n", response.Host, response.Port)
fmt.Printf("JARM指纹: %s\n", response.JarmHash)</code></pre>
            </div>

            <h3><i class="fas fa-list"></i> 批量扫描示例</h3>
            <p>演示如何使用Runner进行多目标、多端口的批量TLS扫描。</p>
            <div class="code-block" data-lang="go">
<pre><code class="language-go">import (
    "time"
    "yaml_scan/pkg/tlsx/runner"
)

// 批量扫描配置
options := &clients.Options{
    ScanMode:    "ctls",
    Timeout:     10,
    Retries:     2,
    Concurrency: 3,                                    // 3个并发线程
    Ports:       []string{"443", "8443"},              // 扫描多个端口
    Inputs:      []string{"www.baidu.com", "github.com"}, // 多个目标
    JSON:        false,                                 // 使用标准输出格式
    NoColor:     true,                                  // 禁用颜色
    SAN:         true,                                  // 显示证书SAN信息
    TLSVersion:  true,                                  // 显示TLS版本
    Cipher:      true,                                  // 显示密码套件
}

// 创建运行器
tlsRunner, err := runner.New(options)
if err != nil {
    log.Fatalf("创建运行器失败: %v", err)
}
defer tlsRunner.Close()

// 执行批量扫描
fmt.Println("开始批量扫描...")
err = tlsRunner.Execute()
if err != nil {
    log.Printf("批量扫描失败: %v", err)
    return
}
fmt.Println("批量扫描完成")</code></pre>
            </div>

            <h3><i class="fas fa-file-export"></i> 自定义输出示例</h3>
            <p>演示如何使用output模块进行自定义格式的结果输出。</p>
            <div class="code-block" data-lang="go">
<pre><code class="language-go">import "yaml_scan/pkg/tlsx/output"

// 创建JSON输出选项
options := &clients.Options{
    ScanMode:    "ctls",
    Timeout:     10,
    Retries:     2,
    Concurrency: 1,
    JSON:        true,    // 启用JSON输出
    NoColor:     true,
}

// 创建输出写入器
writer, err := output.New(options)
if err != nil {
    log.Fatalf("创建输出写入器失败: %v", err)
}
defer writer.Close()

// 创建TLS服务
service, err := tlsx.New(options)
if err != nil {
    log.Fatalf("创建TLS服务失败: %v", err)
}

// 连接并获取响应
response, err := service.Connect("www.baidu.com", "", "443")
if err != nil {
    log.Printf("连接失败: %v", err)
    return
}

// 使用自定义输出写入器输出结果
fmt.Println("JSON格式输出:")
err = writer.Write(response)
if err != nil {
    log.Printf("输出失败: %v", err)
}</code></pre>
            </div>
        </section>

        <!-- 配置参数 -->
        <section id="configuration" class="section">
            <h2><i class="fas fa-cog"></i> 配置参数详解</h2>

            <h3><i class="fas fa-wrench"></i> clients.Options 结构体</h3>
            <p>pkg/tlsx包的核心配置结构，包含所有可配置的参数：</p>
            <div class="code-block" data-lang="go">
<pre><code class="language-go">type Options struct {
    // === 基础配置 ===
    ScanMode    string   `json:"scan_mode"`    // 扫描模式: ctls, ztls, openssl, auto
    Timeout     int      `json:"timeout"`      // 连接超时时间(秒)，默认10
    Retries     int      `json:"retries"`      // 重试次数，默认3
    Concurrency int      `json:"concurrency"`  // 并发数，默认25
    Ports       []string `json:"ports"`        // 扫描端口列表，默认["443"]
    Inputs      []string `json:"inputs"`       // 目标主机列表

    // === TLS功能开关 ===
    TlsVersionsEnum   bool `json:"tls_versions_enum"`   // TLS版本枚举
    TlsCiphersEnum    bool `json:"tls_ciphers_enum"`    // 密码套件枚举
    Jarm              bool `json:"jarm"`                // JARM指纹识别
    Ja3               bool `json:"ja3"`                 // JA3客户端指纹
    Ja3s              bool `json:"ja3s"`                // JA3S服务器指纹

    // === 证书检查选项 ===
    Expired           bool `json:"expired"`             // 检查过期证书
    SelfSigned        bool `json:"self_signed"`         // 检查自签名证书
    MisMatched        bool `json:"mismatched"`          // 检查主机名不匹配
    Revoked           bool `json:"revoked"`             // 检查撤销证书
    Untrusted         bool `json:"untrusted"`           // 检查不受信任证书
    WildcardCertCheck bool `json:"wildcard_cert_check"` // 检查通配符证书

    // === 输出控制 ===
    JSON       bool   `json:"json"`        // JSON格式输出
    OutputFile string `json:"output_file"` // 输出文件路径
    NoColor    bool   `json:"no_color"`    // 禁用颜色输出
    Verbose    bool   `json:"verbose"`     // 详细输出模式
    Silent     bool   `json:"silent"`      // 静默模式

    // === 显示选项 ===
    SAN        bool   `json:"san"`         // 显示证书主题备用名称
    CN         bool   `json:"cn"`          // 显示证书通用名称
    TLSVersion bool   `json:"tls_version"` // 显示TLS版本
    Cipher     bool   `json:"cipher"`      // 显示密码套件
    Serial     bool   `json:"serial"`      // 显示证书序列号
    Hash       string `json:"hash"`        // 证书指纹哈希算法

    // === 高级选项 ===
    ProbeStatus       bool   `json:"probe_status"`        // 显示探测状态
    RespOnly          bool   `json:"resp_only"`           // 仅响应模式
    ScanAllIPs        bool   `json:"scan_all_ips"`        // 扫描所有IP
    VerifyServerCertificate bool `json:"verify_server_certificate"` // 验证服务器证书
}</code></pre>
            </div>

            <h3><i class="fas fa-layer-group"></i> 扫描模式详细对比</h3>
            <table class="api-table">
                <thead>
                    <tr>
                        <th>模式</th>
                        <th>实现库</th>
                        <th>性能</th>
                        <th>功能</th>
                        <th>稳定性</th>
                        <th>推荐场景</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><code>ctls</code></td>
                        <td>crypto/tls</td>
                        <td>⭐⭐⭐⭐⭐</td>
                        <td>⭐⭐⭐</td>
                        <td>⭐⭐⭐⭐⭐</td>
                        <td>生产环境、基础扫描</td>
                    </tr>
                    <tr>
                        <td><code>ztls</code></td>
                        <td>zcrypto/tls</td>
                        <td>⭐⭐⭐⭐</td>
                        <td>⭐⭐⭐⭐⭐</td>
                        <td>⭐⭐⭐⭐</td>
                        <td>安全研究、深度分析</td>
                    </tr>
                    <tr>
                        <td><code>openssl</code></td>
                        <td>OpenSSL CLI</td>
                        <td>⭐⭐⭐</td>
                        <td>⭐⭐⭐⭐</td>
                        <td>⭐⭐⭐⭐</td>
                        <td>特殊协议、兼容性</td>
                    </tr>
                    <tr>
                        <td><code>auto</code></td>
                        <td>智能选择</td>
                        <td>⭐⭐⭐⭐</td>
                        <td>⭐⭐⭐⭐</td>
                        <td>⭐⭐⭐⭐⭐</td>
                        <td>自动化、复杂环境</td>
                    </tr>
                </tbody>
            </table>

            <h3><i class="fas fa-sliders-h"></i> 性能调优参数</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4><i class="fas fa-tachometer-alt" style="color: var(--success-color);"></i> 并发控制</h4>
                    <ul>
                        <li><strong>Concurrency</strong>: 并发线程数</li>
                        <li><strong>小规模</strong>: 1-5个并发</li>
                        <li><strong>中等规模</strong>: 5-20个并发</li>
                        <li><strong>大规模</strong>: 20-50个并发</li>
                        <li><strong>注意</strong>: 过高并发可能导致网络拥塞</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h4><i class="fas fa-clock" style="color: var(--warning-color);"></i> 超时设置</h4>
                    <ul>
                        <li><strong>Timeout</strong>: 连接超时时间</li>
                        <li><strong>快速扫描</strong>: 5-10秒</li>
                        <li><strong>标准扫描</strong>: 10-15秒</li>
                        <li><strong>深度分析</strong>: 15-30秒</li>
                        <li><strong>JARM指纹</strong>: 建议20秒以上</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h4><i class="fas fa-redo" style="color: var(--info-color);"></i> 重试策略</h4>
                    <ul>
                        <li><strong>Retries</strong>: 失败重试次数</li>
                        <li><strong>稳定网络</strong>: 1-2次重试</li>
                        <li><strong>不稳定网络</strong>: 3-5次重试</li>
                        <li><strong>生产环境</strong>: 建议3次重试</li>
                        <li><strong>测试环境</strong>: 可适当增加</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h4><i class="fas fa-list" style="color: var(--danger-color);"></i> 目标管理</h4>
                    <ul>
                        <li><strong>Inputs</strong>: 目标主机列表</li>
                        <li><strong>Ports</strong>: 扫描端口列表</li>
                        <li><strong>支持格式</strong>: 域名、IP、CIDR</li>
                        <li><strong>批量处理</strong>: 支持文件输入</li>
                    </ul>
                </div>
            </div>
        </section>

        <!-- 输出格式 -->
        <section id="output" class="section">
            <h2><i class="fas fa-file-export"></i> 输出格式详解</h2>

            <h3><i class="fas fa-code"></i> Response 响应结构</h3>
            <p>pkg/tlsx包的核心响应数据结构，包含完整的TLS扫描结果。</p>
            <div class="code-block" data-lang="go">
<pre><code class="language-go">// Response TLS扫描的完整响应结构
type Response struct {
    // === 基本连接信息 ===
    Timestamp *time.Time `json:"timestamp,omitempty"` // 扫描时间戳
    Host      string     `json:"host"`                // 目标主机名
    IP        string     `json:"ip,omitempty"`        // 目标IP地址
    Port      string     `json:"port"`                // 目标端口

    // === 连接状态 ===
    ProbeStatus bool   `json:"probe_status"`     // TLS探测是否成功
    Error       string `json:"error,omitempty"`  // 连接失败时的错误信息

    // === TLS协议信息 ===
    Version string `json:"tls_version,omitempty"` // 协商使用的TLS版本
    Cipher  string `json:"cipher,omitempty"`      // 协商使用的密码套件

    // === 证书信息 ===
    *CertificateResponse `json:",inline"` // 内联的叶证书详细信息

    // === 客户端信息 ===
    TLSConnection string `json:"tls_connection,omitempty"` // 使用的TLS客户端类型

    // === 证书链 ===
    Chain []*CertificateResponse `json:"chain,omitempty"` // 完整证书链

    // === 指纹哈希 ===
    JarmHash string `json:"jarm_hash,omitempty"` // JARM TLS服务器指纹
    Ja3Hash  string `json:"ja3_hash,omitempty"`  // JA3客户端指纹
    Ja3sHash string `json:"ja3s_hash,omitempty"` // JA3S服务器指纹

    // === 服务器名称指示 ===
    ServerName string `json:"sni,omitempty"` // 使用的SNI

    // === 枚举结果 ===
    VersionEnum []string      `json:"version_enum,omitempty"` // 支持的TLS版本列表
    TlsCiphers  []TlsCiphers  `json:"cipher_enum,omitempty"`  // 密码套件支持情况

    // === 协议详细信息（仅ztls模式） ===
    ClientHello *ztls.ClientHello `json:"client_hello,omitempty"` // 客户端Hello消息
    ServerHello *ztls.ServerHello `json:"servers_hello,omitempty"` // 服务器Hello消息
}</code></pre>
            </div>

            <h3><i class="fas fa-certificate"></i> CertificateResponse 证书结构</h3>
            <div class="code-block" data-lang="go">
<pre><code class="language-go">// CertificateResponse 包含X.509证书的完整解析信息
type CertificateResponse struct {
    // === 证书状态检查 ===
    Expired    bool `json:"expired,omitempty"`     // 证书是否已过期
    SelfSigned bool `json:"self_signed,omitempty"` // 证书是否为自签名
    MisMatched bool `json:"mismatched,omitempty"`  // 证书是否与目标主机名不匹配
    Revoked    bool `json:"revoked,omitempty"`     // 证书是否已被撤销
    Untrusted  bool `json:"untrusted,omitempty"`   // 证书是否不受信任

    // === 证书有效期 ===
    NotBefore time.Time `json:"not_before,omitempty"` // 证书生效时间
    NotAfter  time.Time `json:"not_after,omitempty"`  // 证书过期时间

    // === 证书主体信息 ===
    SubjectDN  string   `json:"subject_dn,omitempty"`  // 主体可分辨名称
    SubjectCN  string   `json:"subject_cn,omitempty"`  // 主体通用名称
    SubjectOrg []string `json:"subject_org,omitempty"` // 主体组织名称
    SubjectAN  []string `json:"subject_an,omitempty"`  // 主体备用名称(SAN)

    // === 域名信息 ===
    Domains []string `json:"domains,omitempty"` // 去重后的所有域名

    // === 证书标识 ===
    Serial string `json:"serial,omitempty"` // 证书序列号

    // === 颁发者信息 ===
    IssuerDN  string   `json:"issuer_dn,omitempty"`  // 颁发者可分辨名称
    IssuerCN  string   `json:"issuer_cn,omitempty"`  // 颁发者通用名称
    IssuerOrg []string `json:"issuer_org,omitempty"` // 颁发者组织名称

    // === 联系信息 ===
    Emails []string `json:"emails,omitempty"` // 证书中的电子邮件地址

    // === 证书指纹 ===
    FingerprintHash CertificateResponseFingerprintHash `json:"fingerprint_hash,omitempty"`

    // === 原始证书数据 ===
    Certificate string `json:"certificate,omitempty"` // PEM格式的完整证书

    // === 证书类型 ===
    WildCardCert bool `json:"wildcard_certificate,omitempty"` // 是否为通配符证书
}</code></pre>
            </div>

            <h3><i class="fas fa-file-code"></i> JSON输出示例</h3>
            <div class="code-block" data-lang="json">
<pre><code class="language-json">{
  "timestamp": "2025-07-01T10:30:00Z",
  "host": "www.baidu.com",
  "port": "443",
  "ip": "*************",
  "probe_status": true,
  "tls_version": "1.3",
  "cipher": "TLS_AES_256_GCM_SHA384",
  "sni": "www.baidu.com",
  "jarm_hash": "27d40d40d29d40d1dc42d43d00041d4689ee210389f4f6b4b5b1b93f92252d",
  "subject_cn": "baidu.com",
  "subject_an": [
    "baidu.com",
    "*.baidu.com",
    "*.91.com",
    "*.hao123.com"
  ],
  "subject_org": [
    "Beijing Baidu Netcom Science Technology Co., Ltd"
  ],
  "expired": false,
  "self_signed": false,
  "mismatched": false,
  "revoked": false,
  "untrusted": false,
  "wildcard_certificate": true,
  "serial": "0c819d9513b3d6dc5a9b7b8c7b8f8e8d",
  "fingerprint_hash": {
    "md5": "a1b2c3d4e5f6789012345678901234567890",
    "sha1": "1234567890abcdef1234567890abcdef12345678",
    "sha256": "abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890"
  },
  "version_enum": [
    "1.2",
    "1.3"
  ],
  "cipher_enum": [
    {
      "version": "1.3",
      "ciphers": {
        "secure": [
          "TLS_AES_256_GCM_SHA384",
          "TLS_CHACHA20_POLY1305_SHA256",
          "TLS_AES_128_GCM_SHA256"
        ]
      }
    }
  ]
}</code></pre>
            </div>

            <h3><i class="fas fa-terminal"></i> 标准文本输出示例</h3>
            <div class="code-block" data-lang="text">
<pre><code>www.baidu.com:443 [1.3] [TLS_AES_256_GCM_SHA384] [baidu.com] [Beijing Baidu Netcom Science Technology Co., Ltd]

详细信息:
  主机: www.baidu.com:443
  IP地址: *************
  TLS版本: 1.3
  密码套件: TLS_AES_256_GCM_SHA384
  服务器名称: www.baidu.com
  探测状态: ✓

指纹信息:
  JARM: 27d40d40d29d40d1dc42d43d00041d4689ee210389f4f6b4b5b1b93f92252d

证书信息:
  通用名称: baidu.com
  组织: Beijing Baidu Netcom Science Technology Co., Ltd
  主题备用名称: baidu.com, *.baidu.com, *.91.com, *.hao123.com
  序列号: 0c819d9513b3d6dc5a9b7b8c7b8f8e8d
  过期状态: ✓ 有效
  自签名: ✗
  主机名匹配: ✓
  通配符证书: ✓

证书指纹:
  MD5: a1b2c3d4e5f6789012345678901234567890
  SHA1: 1234567890abcdef1234567890abcdef12345678
  SHA256: abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890

支持的TLS版本: 1.2, 1.3

密码套件分析:
  TLS 1.3:
    ✓ 安全: TLS_AES_256_GCM_SHA384, TLS_CHACHA20_POLY1305_SHA256, TLS_AES_128_GCM_SHA256</code></pre>
            </div>

            <h3><i class="fas fa-cogs"></i> 输出控制选项</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4><i class="fas fa-file-code" style="color: var(--primary-color);"></i> 格式控制</h4>
                    <ul>
                        <li><strong>JSON</strong>: 结构化JSON输出</li>
                        <li><strong>NoColor</strong>: 禁用颜色输出</li>
                        <li><strong>Verbose</strong>: 详细输出模式</li>
                        <li><strong>Silent</strong>: 静默模式</li>
                        <li><strong>RespOnly</strong>: 仅响应内容</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h4><i class="fas fa-eye" style="color: var(--success-color);"></i> 显示控制</h4>
                    <ul>
                        <li><strong>SAN</strong>: 显示证书SAN</li>
                        <li><strong>CN</strong>: 显示证书CN</li>
                        <li><strong>TLSVersion</strong>: 显示TLS版本</li>
                        <li><strong>Cipher</strong>: 显示密码套件</li>
                        <li><strong>Serial</strong>: 显示证书序列号</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h4><i class="fas fa-file-export" style="color: var(--warning-color);"></i> 输出目标</h4>
                    <ul>
                        <li><strong>标准输出</strong>: 控制台输出</li>
                        <li><strong>文件输出</strong>: 指定OutputFile</li>
                        <li><strong>流式输出</strong>: 实时输出结果</li>
                        <li><strong>批量输出</strong>: 批处理模式</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h4><i class="fas fa-fingerprint" style="color: var(--danger-color);"></i> 指纹输出</h4>
                    <ul>
                        <li><strong>Hash</strong>: 证书指纹算法</li>
                        <li><strong>JARM</strong>: TLS服务器指纹</li>
                        <li><strong>JA3/JA3S</strong>: 客户端/服务器指纹</li>
                        <li><strong>自定义格式</strong>: 可扩展输出</li>
                    </ul>
                </div>
            </div>
        </section>





        <!-- 页脚 -->
        <footer class="footer">
            <p><i class="fas fa-code"></i> 由 <strong>yaml_scan</strong> 项目开发维护</p>
            <p><i class="fas fa-heart" style="color: #e74c3c;"></i> 专注于TLS安全扫描和协议分析</p>
            <p style="margin-top: 15px;">
                <strong>pkg/tlsx</strong> - yaml_scan项目的核心TLS扫描库，提供统一、高效、可扩展的TLS安全分析能力
            </p>
        </footer>
    </div>

    <script>
        // 初始化Mermaid
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            themeVariables: {
                primaryColor: '#2563eb',
                primaryTextColor: '#ffffff',
                primaryBorderColor: '#1d4ed8',
                lineColor: '#6b7280',
                secondaryColor: '#f3f4f6',
                tertiaryColor: '#ffffff'
            }
        });

        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // 代码高亮
        Prism.highlightAll();

        // 添加复制代码功能
        document.querySelectorAll('.code-block').forEach(block => {
            const button = document.createElement('button');
            button.innerHTML = '<i class="fas fa-copy"></i>';
            button.style.cssText = `
                position: absolute;
                top: 10px;
                left: 15px;
                background: var(--success-color);
                color: white;
                border: none;
                padding: 8px 12px;
                border-radius: 5px;
                cursor: pointer;
                font-size: 0.9rem;
                transition: background 0.3s;
            `;
            button.addEventListener('click', () => {
                const code = block.querySelector('code').textContent;
                navigator.clipboard.writeText(code).then(() => {
                    button.innerHTML = '<i class="fas fa-check"></i> 已复制';
                    setTimeout(() => {
                        button.innerHTML = '<i class="fas fa-copy"></i>';
                    }, 2000);
                });
            });
            block.style.position = 'relative';
            block.appendChild(button);
        });
    </script>
</body>
</html>
                        <td>多核提升并发性能</td>
                    </tr>
                    <tr>
                        <td>网络</td>
                        <td>100Mbps</td>
                        <td>1Gbps+</td>
                        <td>高带宽支持大规模扫描</td>
                    </tr>
                    <tr>
                        <td>存储</td>
                        <td>1GB</td>
                        <td>10GB+</td>
                        <td>存储扫描结果和日志</td>
                    </tr>
                </tbody>
            </table>

            <h3><i class="fas fa-docker"></i> Docker部署</h3>
            <div class="code-block" data-lang="dockerfile">
<pre><code class="language-dockerfile"># Dockerfile
FROM golang:1.24-alpine AS builder

# 安装依赖
RUN apk add --no-cache git ca-certificates

# 设置工作目录
WORKDIR /app

# 复制源码
COPY . .

# 构建应用
RUN go mod download
RUN CGO_ENABLED=0 GOOS=linux go build -o tlsx-scanner ./cmd/scanner

# 运行时镜像
FROM alpine:latest

# 安装运行时依赖
RUN apk add --no-cache ca-certificates openssl

# 创建非root用户
RUN adduser -D -s /bin/sh scanner

# 复制二进制文件
COPY --from=builder /app/tlsx-scanner /usr/local/bin/

# 切换到非root用户
USER scanner

# 设置入口点
ENTRYPOINT ["tlsx-scanner"]</code></pre>
            </div>

            <div class="code-block" data-lang="yaml">
<pre><code class="language-yaml"># docker-compose.yml
version: '3.8'

services:
  tlsx-scanner:
    build: .
    container_name: tlsx-scanner
    restart: unless-stopped

    # 资源限制
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '2'
        reservations:
          memory: 512M
          cpus: '1'

    # 环境变量
    environment:
      - SCAN_MODE=ctls
      - TIMEOUT=10
      - CONCURRENCY=5

    # 挂载卷
    volumes:
      - ./config:/app/config:ro
      - ./results:/app/results
      - ./logs:/app/logs

    # 网络配置
    networks:
      - scanner-network

networks:
  scanner-network:
    driver: bridge</code></pre>
            </div>

            <h3><i class="fas fa-cloud"></i> Kubernetes部署</h3>
            <div class="code-block" data-lang="yaml">
<pre><code class="language-yaml"># k8s-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: tlsx-scanner
  labels:
    app: tlsx-scanner
spec:
  replicas: 3
  selector:
    matchLabels:
      app: tlsx-scanner
  template:
    metadata:
      labels:
        app: tlsx-scanner
    spec:
      containers:
      - name: tlsx-scanner
        image: tlsx-scanner:latest
        ports:
        - containerPort: 8080

        # 资源配置
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"

        # 环境变量
        env:
        - name: SCAN_MODE
          value: "ctls"
        - name: CONCURRENCY
          value: "5"

        # 健康检查
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10

        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5

        # 挂载配置
        volumeMounts:
        - name: config-volume
          mountPath: /app/config
        - name: results-volume
          mountPath: /app/results

      volumes:
      - name: config-volume
        configMap:
          name: tlsx-config
      - name: results-volume
        persistentVolumeClaim:
          claimName: tlsx-results-pvc

---
apiVersion: v1
kind: Service
metadata:
  name: tlsx-scanner-service
spec:
  selector:
    app: tlsx-scanner
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8080
  type: LoadBalancer</code></pre>
            </div>

            <h3><i class="fas fa-chart-bar"></i> 监控和日志</h3>
            <div class="code-block" data-lang="go">
<pre><code class="language-go">// 集成Prometheus监控
import (
    "github.com/prometheus/client_golang/prometheus"
    "github.com/prometheus/client_golang/prometheus/promhttp"
)

// 定义监控指标
var (
    scanDuration = prometheus.NewHistogramVec(
        prometheus.HistogramOpts{
            Name: "tlsx_scan_duration_seconds",
            Help: "TLS扫描耗时分布",
        },
        []string{"scan_mode", "status"},
    )

    scanTotal = prometheus.NewCounterVec(
        prometheus.CounterOpts{
            Name: "tlsx_scan_total",
            Help: "TLS扫描总数",
        },
        []string{"scan_mode", "result"},
    )
)

// 注册指标
func init() {
    prometheus.MustRegister(scanDuration)
    prometheus.MustRegister(scanTotal)
}

// 在扫描中记录指标
func performScan(options *clients.Options) {
    start := time.Now()
    defer func() {
        duration := time.Since(start).Seconds()
        scanDuration.WithLabelValues(options.ScanMode, "completed").Observe(duration)
    }()

    // 执行扫描...
    result, err := scanner.Execute()

    if err != nil {
        scanTotal.WithLabelValues(options.ScanMode, "error").Inc()
    } else {
        scanTotal.WithLabelValues(options.ScanMode, "success").Inc()
    }
}</code></pre>
            </div>
        </section>

        <!-- 集成指南 -->
        <section id="integration" class="section">
            <h2><i class="fas fa-plug"></i> 集成指南</h2>

            <h3><i class="fas fa-code-branch"></i> CI/CD集成</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4><i class="fab fa-github" style="color: #333;"></i> GitHub Actions</h4>
                    <div class="code-block" data-lang="yaml">
<pre><code class="language-yaml"># .github/workflows/tls-scan.yml
name: TLS Security Scan
on:
  schedule:
    - cron: '0 2 * * *'  # 每天凌晨2点
  push:
    branches: [main]

jobs:
  tls-scan:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3

    - name: Setup Go
      uses: actions/setup-go@v3
      with:
        go-version: 1.24

    - name: Run TLS Scan
      run: |
        go run ./cmd/scanner \
          -mode ctls \
          -timeout 10 \
          -json \
          -output results.json \
          -targets production-hosts.txt

    - name: Upload Results
      uses: actions/upload-artifact@v3
      with:
        name: tls-scan-results
        path: results.json</code></pre>
                    </div>
                </div>

                <div class="feature-card">
                    <h4><i class="fab fa-gitlab" style="color: #fc6d26;"></i> GitLab CI</h4>
                    <div class="code-block" data-lang="yaml">
<pre><code class="language-yaml"># .gitlab-ci.yml
stages:
  - security-scan

tls-security-scan:
  stage: security-scan
  image: golang:1.24-alpine
  script:
    - apk add --no-cache git
    - go mod download
    - go run ./cmd/scanner \
        -mode auto \
        -concurrency 5 \
        -json \
        -output $CI_PROJECT_DIR/tls-results.json \
        -targets production.txt
  artifacts:
    reports:
      junit: tls-results.json
    expire_in: 1 week
  only:
    - schedules
    - main</code></pre>
                    </div>
                </div>
            </div>

            <h3><i class="fas fa-bell"></i> 告警集成</h3>
            <div class="code-block" data-lang="go">
<pre><code class="language-go">// 集成Slack告警
import (
    "bytes"
    "encoding/json"
    "net/http"
)

type SlackMessage struct {
    Text        string `json:"text"`
    Channel     string `json:"channel"`
    Username    string `json:"username"`
    IconEmoji   string `json:"icon_emoji"`
}

func sendSlackAlert(webhook string, message string) error {
    payload := SlackMessage{
        Text:      message,
        Channel:   "#security",
        Username:  "TLS Scanner",
        IconEmoji: ":warning:",
    }

    jsonPayload, _ := json.Marshal(payload)
    resp, err := http.Post(webhook, "application/json", bytes.NewBuffer(jsonPayload))
    if err != nil {
        return err
    }
    defer resp.Body.Close()
    return nil
}

// 检查证书过期并发送告警
func checkCertificateExpiry(response *clients.Response) {
    if response.CertificateResponse != nil && response.CertificateResponse.Expired {
        message := fmt.Sprintf("🚨 证书过期告警: %s:%s 的证书已过期",
            response.Host, response.Port)
        sendSlackAlert(os.Getenv("SLACK_WEBHOOK"), message)
    }
}</code></pre>
            </div>

            <h3><i class="fas fa-database"></i> 数据库集成</h3>
            <div class="code-block" data-lang="go">
<pre><code class="language-go">// 集成PostgreSQL存储扫描结果
import (
    "database/sql"
    "time"
    _ "github.com/lib/pq"
)

type ScanResult struct {
    ID          int       `db:"id"`
    Host        string    `db:"host"`
    Port        string    `db:"port"`
    TLSVersion  string    `db:"tls_version"`
    Cipher      string    `db:"cipher"`
    Expired     bool      `db:"expired"`
    SelfSigned  bool      `db:"self_signed"`
    ScanTime    time.Time `db:"scan_time"`
    JarmHash    string    `db:"jarm_hash"`
}

func saveScanResult(db *sql.DB, response *clients.Response) error {
    query := `
        INSERT INTO tls_scan_results
        (host, port, tls_version, cipher, expired, self_signed, scan_time, jarm_hash)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8)`

    _, err := db.Exec(query,
        response.Host,
        response.Port,
        response.Version,
        response.Cipher,
        response.CertificateResponse.Expired,
        response.CertificateResponse.SelfSigned,
        response.Timestamp,
        response.JarmHash,
    )
    return err
}

// 创建数据库表
const createTableSQL = `
CREATE TABLE IF NOT EXISTS tls_scan_results (
    id SERIAL PRIMARY KEY,
    host VARCHAR(255) NOT NULL,
    port VARCHAR(10) NOT NULL,
    tls_version VARCHAR(10),
    cipher VARCHAR(255),
    expired BOOLEAN DEFAULT FALSE,
    self_signed BOOLEAN DEFAULT FALSE,
    scan_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    jarm_hash VARCHAR(64),
    INDEX idx_host_port (host, port),
    INDEX idx_scan_time (scan_time)
);`</code></pre>
            </div>
        </section>

        <!-- 故障排除 -->
        <section id="troubleshooting" class="section">
            <h2><i class="fas fa-wrench"></i> 故障排除</h2>

            <h3><i class="fas fa-question-circle"></i> 常见问题FAQ</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4><i class="fas fa-clock" style="color: var(--warning-color);"></i> 扫描速度慢</h4>
                    <p><strong>问题:</strong> 扫描速度比预期慢</p>
                    <p><strong>解决方案:</strong></p>
                    <ul>
                        <li>增加并发数 (Concurrency)</li>
                        <li>减少超时时间 (Timeout)</li>
                        <li>使用更快的扫描模式 (ctls)</li>
                        <li>禁用不必要的功能 (JARM, 证书链)</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h4><i class="fas fa-memory" style="color: var(--danger-color);"></i> 内存使用过高</h4>
                    <p><strong>问题:</strong> 程序消耗大量内存</p>
                    <p><strong>解决方案:</strong></p>
                    <ul>
                        <li>减少并发数</li>
                        <li>分批处理大量目标</li>
                        <li>及时关闭连接</li>
                        <li>使用流式处理</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h4><i class="fas fa-network-wired" style="color: var(--info-color);"></i> 连接失败率高</h4>
                    <p><strong>问题:</strong> 大量连接失败</p>
                    <p><strong>解决方案:</strong></p>
                    <ul>
                        <li>增加超时时间</li>
                        <li>增加重试次数</li>
                        <li>检查网络连接</li>
                        <li>验证目标可达性</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h4><i class="fas fa-certificate" style="color: var(--success-color);"></i> 证书解析错误</h4>
                    <p><strong>问题:</strong> 无法解析某些证书</p>
                    <p><strong>解决方案:</strong></p>
                    <ul>
                        <li>使用ztls模式 (更宽松解析)</li>
                        <li>禁用证书验证</li>
                        <li>检查证书格式</li>
                        <li>更新CA证书库</li>
                    </ul>
                </div>
            </div>

            <h3><i class="fas fa-terminal"></i> 调试命令</h3>
            <div class="code-block" data-lang="bash">
<pre><code class="language-bash"># 测试单个目标的连接
go run ./cmd/scanner \
  -mode ctls \
  -timeout 30 \
  -verbose \
  -target example.com:443

# 调试DNS解析
go run ./cmd/scanner \
  -mode ctls \
  -verbose \
  -resolvers 8.8.8.8,1.1.1.1 \
  -target example.com:443

# 测试不同TLS版本
go run ./cmd/scanner \
  -mode ztls \
  -min-version 1.0 \
  -max-version 1.3 \
  -tls-versions-enum \
  -target example.com:443

# 调试证书问题
go run ./cmd/scanner \
  -mode ztls \
  -no-verify \
  -expired \
  -self-signed \
  -cert \
  -target example.com:443

# 性能测试
time go run ./cmd/scanner \
  -mode ctls \
  -concurrency 10 \
  -timeout 5 \
  -targets large-list.txt</code></pre>
            </div>

            <h3><i class="fas fa-chart-line"></i> 性能调优</h3>
            <table class="api-table">
                <thead>
                    <tr>
                        <th>场景</th>
                        <th>推荐配置</th>
                        <th>说明</th>
                        <th>注意事项</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>快速扫描</td>
                        <td>Concurrency: 20, Timeout: 5</td>
                        <td>最大化速度</td>
                        <td>可能增加失败率</td>
                    </tr>
                    <tr>
                        <td>准确扫描</td>
                        <td>Concurrency: 5, Timeout: 15</td>
                        <td>平衡速度和准确性</td>
                        <td>推荐的生产配置</td>
                    </tr>
                    <tr>
                        <td>深度分析</td>
                        <td>Concurrency: 1, Timeout: 30</td>
                        <td>最大化信息收集</td>
                        <td>速度较慢</td>
                    </tr>
                    <tr>
                        <td>大规模扫描</td>
                        <td>分批处理, 流式输出</td>
                        <td>处理大量目标</td>
                        <td>需要资源管理</td>
                    </tr>
                </tbody>
            </table>
        </section>

        <!-- 高级功能 -->
        <section id="advanced-features" class="section">
            <h2><i class="fas fa-rocket"></i> 高级功能</h2>

            <h3><i class="fas fa-filter"></i> 自定义过滤器</h3>
            <div class="code-block" data-lang="go">
<pre><code class="language-go">// 自定义结果过滤器
type ResultFilter interface {
    Filter(response *clients.Response) bool
}

// 过期证书过滤器
type ExpiredCertFilter struct{}

func (f *ExpiredCertFilter) Filter(response *clients.Response) bool {
    return response.CertificateResponse != nil &&
           response.CertificateResponse.Expired
}

// 弱密码套件过滤器
type WeakCipherFilter struct {
    WeakCiphers []string
}

func (f *WeakCipherFilter) Filter(response *clients.Response) bool {
    for _, weak := range f.WeakCiphers {
        if strings.Contains(response.Cipher, weak) {
            return true
        }
    }
    return false
}

// 使用过滤器
func filterResults(responses []*clients.Response, filters []ResultFilter) []*clients.Response {
    var filtered []*clients.Response
    for _, response := range responses {
        for _, filter := range filters {
            if filter.Filter(response) {
                filtered = append(filtered, response)
                break
            }
        }
    }
    return filtered
}

// 示例用法
filters := []ResultFilter{
    &ExpiredCertFilter{},
    &WeakCipherFilter{WeakCiphers: []string{"RC4", "DES", "MD5"}},
}
filteredResults := filterResults(scanResults, filters)</code></pre>
            </div>

            <h3><i class="fas fa-plug"></i> 插件系统</h3>
            <div class="code-block" data-lang="go">
<pre><code class="language-go">// 插件接口定义
type Plugin interface {
    Name() string
    Version() string
    Process(response *clients.Response) (*PluginResult, error)
}

// 插件结果
type PluginResult struct {
    PluginName string                 `json:"plugin_name"`
    Data       map[string]interface{} `json:"data"`
    Alerts     []Alert               `json:"alerts,omitempty"`
}

type Alert struct {
    Level   string `json:"level"`   // info, warning, critical
    Message string `json:"message"`
}

// 证书监控插件
type CertMonitorPlugin struct{}

func (p *CertMonitorPlugin) Name() string { return "cert-monitor" }
func (p *CertMonitorPlugin) Version() string { return "1.0.0" }

func (p *CertMonitorPlugin) Process(response *clients.Response) (*PluginResult, error) {
    result := &PluginResult{
        PluginName: p.Name(),
        Data:       make(map[string]interface{}),
        Alerts:     []Alert{},
    }

    if response.CertificateResponse == nil {
        return result, nil
    }

    cert := response.CertificateResponse

    // 检查证书过期
    if cert.Expired {
        result.Alerts = append(result.Alerts, Alert{
            Level:   "critical",
            Message: "证书已过期",
        })
    }

    // 检查即将过期的证书（30天内）
    // 这里需要解析证书的NotAfter字段
    result.Data["expired"] = cert.Expired
    result.Data["self_signed"] = cert.SelfSigned
    result.Data["wildcard"] = cert.WildCardCert

    return result, nil
}

// 插件管理器
type PluginManager struct {
    plugins []Plugin
}

func (pm *PluginManager) RegisterPlugin(plugin Plugin) {
    pm.plugins = append(pm.plugins, plugin)
}

func (pm *PluginManager) ProcessResponse(response *clients.Response) map[string]*PluginResult {
    results := make(map[string]*PluginResult)

    for _, plugin := range pm.plugins {
        result, err := plugin.Process(response)
        if err != nil {
            gologger.Error().Msgf("插件 %s 处理失败: %v", plugin.Name(), err)
            continue
        }
        results[plugin.Name()] = result
    }

    return results
}</code></pre>
            </div>

            <h3><i class="fas fa-chart-pie"></i> 统计分析</h3>
            <div class="code-block" data-lang="go">
<pre><code class="language-go">// 扫描统计分析
type ScanStatistics struct {
    TotalScanned     int                    `json:"total_scanned"`
    SuccessfulScans  int                    `json:"successful_scans"`
    FailedScans      int                    `json:"failed_scans"`
    TLSVersionStats  map[string]int         `json:"tls_version_stats"`
    CipherStats      map[string]int         `json:"cipher_stats"`
    CertIssueStats   map[string]int         `json:"cert_issue_stats"`
    PortStats        map[string]int         `json:"port_stats"`
    ScanDuration     time.Duration          `json:"scan_duration"`
    AvgResponseTime  time.Duration          `json:"avg_response_time"`
    TopJarmHashes    map[string]int         `json:"top_jarm_hashes"`
}

func AnalyzeScanResults(responses []*clients.Response, startTime time.Time) *ScanStatistics {
    stats := &ScanStatistics{
        TLSVersionStats: make(map[string]int),
        CipherStats:     make(map[string]int),
        CertIssueStats:  make(map[string]int),
        PortStats:       make(map[string]int),
        TopJarmHashes:   make(map[string]int),
        ScanDuration:    time.Since(startTime),
    }

    var totalResponseTime time.Duration

    for _, response := range responses {
        stats.TotalScanned++

        if response.ProbeStatus {
            stats.SuccessfulScans++

            // TLS版本统计
            if response.Version != "" {
                stats.TLSVersionStats[response.Version]++
            }

            // 密码套件统计
            if response.Cipher != "" {
                stats.CipherStats[response.Cipher]++
            }

            // 端口统计
            stats.PortStats[response.Port]++

            // JARM指纹统计
            if response.JarmHash != "" {
                stats.TopJarmHashes[response.JarmHash]++
            }

            // 证书问题统计
            if response.CertificateResponse != nil {
                cert := response.CertificateResponse
                if cert.Expired {
                    stats.CertIssueStats["expired"]++
                }
                if cert.SelfSigned {
                    stats.CertIssueStats["self_signed"]++
                }
                if cert.MisMatched {
                    stats.CertIssueStats["mismatched"]++
                }
                if cert.Revoked {
                    stats.CertIssueStats["revoked"]++
                }
            }

            // 响应时间统计（如果有的话）
            // totalResponseTime += response.ResponseTime
        } else {
            stats.FailedScans++
        }
    }

    if stats.SuccessfulScans > 0 {
        stats.AvgResponseTime = totalResponseTime / time.Duration(stats.SuccessfulScans)
    }

    return stats
}

// 生成统计报告
func GenerateReport(stats *ScanStatistics) string {
    var report strings.Builder

    report.WriteString("=== TLS扫描统计报告 ===\n\n")
    report.WriteString(fmt.Sprintf("总扫描数: %d\n", stats.TotalScanned))
    report.WriteString(fmt.Sprintf("成功扫描: %d (%.2f%%)\n",
        stats.SuccessfulScans,
        float64(stats.SuccessfulScans)/float64(stats.TotalScanned)*100))
    report.WriteString(fmt.Sprintf("失败扫描: %d (%.2f%%)\n",
        stats.FailedScans,
        float64(stats.FailedScans)/float64(stats.TotalScanned)*100))
    report.WriteString(fmt.Sprintf("扫描耗时: %v\n", stats.ScanDuration))
    report.WriteString(fmt.Sprintf("平均响应时间: %v\n\n", stats.AvgResponseTime))

    // TLS版本分布
    report.WriteString("TLS版本分布:\n")
    for version, count := range stats.TLSVersionStats {
        percentage := float64(count) / float64(stats.SuccessfulScans) * 100
        report.WriteString(fmt.Sprintf("  %s: %d (%.2f%%)\n", version, count, percentage))
    }

    // 证书问题统计
    if len(stats.CertIssueStats) > 0 {
        report.WriteString("\n证书问题统计:\n")
        for issue, count := range stats.CertIssueStats {
            report.WriteString(fmt.Sprintf("  %s: %d\n", issue, count))
        }
    }

    return report.String()
}</code></pre>
            </div>
        </section>

        <!-- 扩展和生态 -->
        <section id="ecosystem" class="section">
            <h2><i class="fas fa-puzzle-piece"></i> 扩展和生态</h2>

            <h3><i class="fas fa-tools"></i> 相关工具</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4><i class="fas fa-terminal" style="color: var(--primary-color);"></i> 命令行工具</h4>
                    <ul>
                        <li><strong>tlsx-cli</strong> - 命令行扫描器</li>
                        <li><strong>tlsx-monitor</strong> - 持续监控工具</li>
                        <li><strong>tlsx-report</strong> - 报告生成器</li>
                        <li><strong>tlsx-compare</strong> - 结果对比工具</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h4><i class="fas fa-globe" style="color: var(--success-color);"></i> Web界面</h4>
                    <ul>
                        <li><strong>tlsx-dashboard</strong> - Web管理界面</li>
                        <li><strong>tlsx-api</strong> - RESTful API服务</li>
                        <li><strong>tlsx-visualizer</strong> - 数据可视化</li>
                        <li><strong>tlsx-scheduler</strong> - 任务调度器</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h4><i class="fas fa-plug" style="color: var(--warning-color);"></i> 集成插件</h4>
                    <ul>
                        <li><strong>Nmap集成</strong> - 端口发现集成</li>
                        <li><strong>Shodan集成</strong> - 威胁情报集成</li>
                        <li><strong>SIEM集成</strong> - 安全信息集成</li>
                        <li><strong>Grafana插件</strong> - 监控面板</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h4><i class="fas fa-cloud" style="color: var(--info-color);"></i> 云服务</h4>
                    <ul>
                        <li><strong>AWS Lambda</strong> - 无服务器扫描</li>
                        <li><strong>Azure Functions</strong> - 云函数集成</li>
                        <li><strong>Google Cloud Run</strong> - 容器化部署</li>
                        <li><strong>Kubernetes Operator</strong> - K8s原生支持</li>
                    </ul>
                </div>
            </div>

            <h3><i class="fas fa-code"></i> 社区贡献</h3>
            <div class="code-block" data-lang="markdown">
<pre><code class="language-markdown"># 贡献指南

## 如何贡献

1. **Fork项目** - 在GitHub上fork项目仓库
2. **创建分支** - 为你的功能创建新分支
3. **编写代码** - 实现新功能或修复bug
4. **编写测试** - 确保代码质量
5. **提交PR** - 提交Pull Request

## 代码规范

- 遵循Go语言官方代码规范
- 使用有意义的变量和函数名
- 添加详细的中文注释
- 编写单元测试
- 更新相关文档

## 测试要求

```bash
# 运行所有测试
go test ./...

# 运行测试并生成覆盖率报告
go test -coverprofile=coverage.out ./...
go tool cover -html=coverage.out

# 运行基准测试
go test -bench=. ./...
```

## 文档更新

- 更新API文档
- 添加使用示例
- 更新README文件
- 翻译英文文档</code></pre>
            </div>

            <h3><i class="fas fa-road"></i> 发展路线图</h3>
            <div class="mermaid">
                gantt
                    title TLS扫描库发展路线图
                    dateFormat  YYYY-MM-DD
                    section 核心功能
                    基础TLS扫描           :done, basic, 2025-01-01, 2025-03-01
                    指纹识别功能          :done, fingerprint, 2025-02-01, 2025-04-01
                    批量扫描优化          :done, batch, 2025-03-01, 2025-05-01

                    section 高级特性
                    插件系统             :active, plugin, 2025-05-01, 2025-07-01
                    Web界面              :web, 2025-06-01, 2025-08-01
                    云原生支持           :cloud, 2025-07-01, 2025-09-01

                    section 企业功能
                    SIEM集成             :siem, 2025-08-01, 2025-10-01
                    合规性检查           :compliance, 2025-09-01, 2025-11-01
                    威胁情报集成         :threat, 2025-10-01, 2025-12-01

                    section 性能优化
                    分布式扫描           :distributed, 2025-11-01, 2026-01-01
                    机器学习集成         :ml, 2025-12-01, 2026-02-01
                    实时流处理           :stream, 2026-01-01, 2026-03-01
            </div>
        </section>

        <!-- 页脚 -->
        <footer class="footer">
            <p><i class="fas fa-code"></i> 由 <strong>yaml_scan</strong> 团队开发维护</p>
            <p><i class="fas fa-heart" style="color: #e74c3c;"></i> 专注于网络安全和TLS协议分析</p>
            <p style="margin-top: 15px;">
                <a href="https://github.com/yaml_scan" style="color: var(--primary-color); text-decoration: none;">
                    <i class="fab fa-github"></i> GitHub
                </a> |
                <a href="#" style="color: var(--primary-color); text-decoration: none;">
                    <i class="fas fa-book"></i> 文档
                </a> |
                <a href="#" style="color: var(--primary-color); text-decoration: none;">
                    <i class="fas fa-bug"></i> 问题反馈
                </a>
            </p>
        </footer>
    </div>

    <script>
        // 初始化Mermaid
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            themeVariables: {
                primaryColor: '#2563eb',
                primaryTextColor: '#ffffff',
                primaryBorderColor: '#1d4ed8',
                lineColor: '#6b7280',
                secondaryColor: '#f3f4f6',
                tertiaryColor: '#ffffff'
            }
        });

        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // 代码高亮
        Prism.highlightAll();

        // 添加复制代码功能
        document.querySelectorAll('.code-block').forEach(block => {
            const button = document.createElement('button');
            button.innerHTML = '<i class="fas fa-copy"></i>';
            button.style.cssText = `
                position: absolute;
                top: 10px;
                left: 15px;
                background: var(--success-color);
                color: white;
                border: none;
                padding: 8px 12px;
                border-radius: 5px;
                cursor: pointer;
                font-size: 0.9rem;
                transition: background 0.3s;
            `;
            button.addEventListener('click', () => {
                const code = block.querySelector('code').textContent;
                navigator.clipboard.writeText(code).then(() => {
                    button.innerHTML = '<i class="fas fa-check"></i> 已复制';
                    setTimeout(() => {
                        button.innerHTML = '<i class="fas fa-copy"></i>';
                    }, 2000);
                });
            });
            block.style.position = 'relative';
            block.appendChild(button);
        });
    </script>
</body>
</html>
