//
// Author: chenjb
// Version: V1.0
// Date: 2025-06-30
// FilePath: /yaml_scan/pkg/tlsx/tlsx/openssl/openssl_exec_test.go
// Description: openssl_exec.go的单元测试

package openssl

import (
	"context"
	"crypto/x509"
	"encoding/pem"
	"fmt"
	"strings"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
)

// TestCMDOUT 测试CMDOUT结构体
// 验证命令输出结构的基本功能
func TestCMDOUT(t *testing.T) {
	t.Run("CMDOUT结构体基本功能", func(t *testing.T) {
		cmdout := &CMDOUT{
			Command: "openssl version",
			Stdout:  "OpenSSL 1.1.1",
			Stderr:  "",
		}

		require.NotNil(t, cmdout, "CMDOUT结构体不应该为nil")
		require.Equal(t, "openssl version", cmdout.Command, "Command字段应该正确设置")
		require.Equal(t, "OpenSSL 1.1.1", cmdout.Stdout, "Stdout字段应该正确设置")
		require.Empty(t, cmdout.Stderr, "Stderr字段应该为空")
	})

	t.Run("CMDOUT字段类型验证", func(t *testing.T) {
		cmdout := &CMDOUT{}
		
		// 验证字段类型
		require.IsType(t, "", cmdout.Command, "Command应该是字符串类型")
		require.IsType(t, "", cmdout.Stdout, "Stdout应该是字符串类型")
		require.IsType(t, "", cmdout.Stderr, "Stderr应该是字符串类型")
	})
}

// TestExecOpenSSL 测试execOpenSSL函数
// 验证OpenSSL命令执行功能
func TestExecOpenSSL(t *testing.T) {
	if !IsAvailable() {
		t.Skip("OpenSSL不可用，跳过execOpenSSL测试")
	}

	t.Run("执行version命令", func(t *testing.T) {
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()

		result, err := execOpenSSL(ctx, []string{"version"})
		require.NoError(t, err, "执行version命令应该成功")
		require.NotNil(t, result, "结果不应该为nil")
		require.NotEmpty(t, result.Stdout, "标准输出不应该为空")
		require.Contains(t, strings.ToLower(result.Stdout), "openssl", "输出应该包含openssl")
		require.Contains(t, result.Command, "version", "命令字符串应该包含version")

		t.Logf("OpenSSL版本: %s", strings.TrimSpace(result.Stdout))
	})

	t.Run("执行无效命令", func(t *testing.T) {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		result, err := execOpenSSL(ctx, []string{"invalid_command"})
		// 可能返回错误或在stderr中包含错误信息
		if err != nil {
			t.Logf("执行无效命令返回错误: %v", err)
		} else {
			require.NotEmpty(t, result.Stderr, "无效命令应该在stderr中有错误信息")
		}
	})

	t.Run("上下文超时测试", func(t *testing.T) {
		// 创建一个很短的超时上下文
		ctx, cancel := context.WithTimeout(context.Background(), 1*time.Millisecond)
		defer cancel()

		// 等待上下文超时
		time.Sleep(2 * time.Millisecond)

		result, err := execOpenSSL(ctx, []string{"version"})
		// 应该因为上下文超时而失败
		if err != nil {
			require.Contains(t, err.Error(), "context", "错误应该与上下文相关")
		}
		require.NotNil(t, result, "即使失败也应该返回结果结构")
	})
}

// TestGetCiphers 测试getCiphers函数
// 验证密码套件获取功能
func TestGetCiphers(t *testing.T) {
	if !IsAvailable() {
		t.Skip("OpenSSL不可用，跳过getCiphers测试")
	}

	t.Run("获取密码套件列表", func(t *testing.T) {
		ciphers, err := getCiphers()
		require.NoError(t, err, "获取密码套件应该成功")
		require.NotEmpty(t, ciphers, "密码套件列表不应该为空")
		require.Greater(t, len(ciphers), 10, "应该有足够数量的密码套件")

		// 验证密码套件格式
		for i, cipher := range ciphers {
			require.NotEmpty(t, cipher, "密码套件[%d]不应该为空", i)
			// 大多数密码套件应该包含常见的关键词
			if i < 5 { // 只检查前几个，避免测试过于严格
				t.Logf("密码套件[%d]: %s", i, cipher)
			}
		}

		t.Logf("总共获取到 %d 个密码套件", len(ciphers))
	})

	t.Run("密码套件唯一性验证", func(t *testing.T) {
		ciphers, err := getCiphers()
		require.NoError(t, err, "获取密码套件应该成功")

		// 检查是否有重复的密码套件
		cipherSet := make(map[string]bool)
		duplicates := []string{}

		for _, cipher := range ciphers {
			if cipherSet[cipher] {
				duplicates = append(duplicates, cipher)
			} else {
				cipherSet[cipher] = true
			}
		}

		if len(duplicates) > 0 {
			t.Logf("发现重复的密码套件: %v", duplicates)
		}
	})
}

// TestReadSessionData 测试readSessionData函数
// 验证会话数据解析功能
func TestReadSessionData(t *testing.T) {
	t.Run("解析有效会话数据", func(t *testing.T) {
		// 模拟OpenSSL s_client的输出
		sessionData := `
SSL-Session:
    Protocol  : TLSv1.2
    Cipher    : ECDHE-RSA-AES256-GCM-SHA384
    Session-ID: 1234567890ABCDEF
    Master-Key: FEDCBA0987654321
    Timeout   : 300 (sec)
`

		session, err := readSessionData(sessionData)
		require.NoError(t, err, "解析会话数据应该成功")
		require.NotNil(t, session, "会话对象不应该为nil")
		require.Equal(t, "TLSv1.2", session.Protocol, "协议版本应该正确")
		require.Equal(t, "ECDHE-RSA-AES256-GCM-SHA384", session.Cipher, "密码套件应该正确")
		require.Equal(t, "FEDCBA0987654321", session.MasterKey, "主密钥应该正确")

		t.Logf("解析的会话信息: Protocol=%s, Cipher=%s", session.Protocol, session.Cipher)
	})

	t.Run("解析不完整会话数据", func(t *testing.T) {
		// 只包含部分会话信息
		sessionData := `
SSL-Session:
    Protocol  : TLSv1.2
    Timeout   : 300 (sec)
`

		session, err := readSessionData(sessionData)
		require.NoError(t, err, "解析不完整会话数据应该成功")
		require.NotNil(t, session, "会话对象不应该为nil")
		require.Equal(t, "TLSv1.2", session.Protocol, "协议版本应该正确")
		require.Empty(t, session.Cipher, "密码套件应该为空")
		require.Empty(t, session.MasterKey, "主密钥应该为空")
	})

	t.Run("解析无会话数据", func(t *testing.T) {
		// 不包含SSL-Session块的数据
		sessionData := `
CONNECTED(00000003)
depth=0 CN = example.com
verify error:num=18:self signed certificate
verify return:1
`

		session, err := readSessionData(sessionData)
		require.NoError(t, err, "解析无会话数据应该成功")
		require.NotNil(t, session, "会话对象不应该为nil")
		require.Empty(t, session.Protocol, "协议版本应该为空")
		require.Empty(t, session.Cipher, "密码套件应该为空")
	})

	t.Run("解析空数据", func(t *testing.T) {
		session, err := readSessionData("")
		require.NoError(t, err, "解析空数据应该成功")
		require.NotNil(t, session, "会话对象不应该为nil")
	})
}

// TestGetx509Certificate 测试getx509Certificate函数
// 验证X.509证书解析功能
func TestGetx509Certificate(t *testing.T) {
	t.Run("解析有效PEM证书", func(t *testing.T) {
		// 创建一个简单的测试证书PEM数据
		testCertPEM := `-----BEGIN CERTIFICATE-----
MIIBkTCB+wIJAMlyFqk69v+9MA0GCSqGSIb3DQEBCwUAMBQxEjAQBgNVBAMMCWxv
Y2FsaG9zdDAeFw0yMzA2MjQwMDAwMDBaFw0yNDA2MjQwMDAwMDBaMBQxEjAQBgNV
BAMMCWxvY2FsaG9zdDBcMA0GCSqGSIb3DQEBAQUAA0sAMEgCQQDTwqq/oBFqGqfr
cJoJdVla5SmVK2n5g+OqVGDuLiDXkTXlLDVA4+7QoroTJ4jhVB8qtDrBN3lUh/sY
OjNnfkbZAgMBAAEwDQYJKoZIhvcNAQELBQADQQBJlffJHybjDGxRMqaRmDhX0+6v
02q4w2eSWryyOMqAFSWdVOkUG+9i7cNMo9fWs4incm16NjNBtEt7aFtVqzD0
-----END CERTIFICATE-----`

		cert, err := getx509Certificate([]byte(testCertPEM))
		require.NoError(t, err, "解析有效PEM证书应该成功")
		require.NotNil(t, cert, "证书对象不应该为nil")
		require.IsType(t, &x509.Certificate{}, cert, "应该返回x509.Certificate类型")

		t.Logf("证书主题: %s", cert.Subject.String())
	})

	t.Run("解析空证书数据", func(t *testing.T) {
		cert, err := getx509Certificate([]byte{})
		require.Error(t, err, "解析空证书数据应该失败")
		require.Nil(t, cert, "证书对象应该为nil")
		require.Contains(t, err.Error(), "empty", "错误信息应该包含'empty'")
	})

	t.Run("解析无效PEM数据", func(t *testing.T) {
		invalidPEM := []byte("这不是有效的PEM数据")
		
		cert, err := getx509Certificate(invalidPEM)
		require.Error(t, err, "解析无效PEM数据应该失败")
		require.Nil(t, cert, "证书对象应该为nil")
		require.Contains(t, err.Error(), "pem", "错误信息应该包含'pem'")
	})

	t.Run("解析格式正确但内容无效的PEM", func(t *testing.T) {
		invalidCertPEM := `-----BEGIN CERTIFICATE-----
这是无效的证书内容
-----END CERTIFICATE-----`

		cert, err := getx509Certificate([]byte(invalidCertPEM))
		require.Error(t, err, "解析无效证书内容应该失败")
		require.Nil(t, cert, "证书对象应该为nil")
	})
}

// TestParseCertificates 测试parseCertificates函数
// 验证证书链解析功能
func TestParseCertificates(t *testing.T) {
	t.Run("解析单个证书", func(t *testing.T) {
		// 模拟包含单个证书的OpenSSL输出
		opensslOutput := `
CONNECTED(00000003)
depth=0 CN = example.com
-----BEGIN CERTIFICATE-----
MIIBkTCB+wIJAMlyFqk69v+9MA0GCSqGSIb3DQEBCwUAMBQxEjAQBgNVBAMMCWxv
Y2FsaG9zdDAeFw0yMzA2MjQwMDAwMDBaFw0yNDA2MjQwMDAwMDBaMBQxEjAQBgNV
BAMMCWxvY2FsaG9zdDBcMA0GCSqGSIb3DQEBAQUAA0sAMEgCQQDTwqq/oBFqGqfr
cJoJdVla5SmVK2n5g+OqVGDuLiDXkTXlLDVA4+7QoroTJ4jhVB8qtDrBN3lUh/sY
OjNnfkbZAgMBAAEwDQYJKoZIhvcNAQELBQADQQBJlffJHybjDGxRMqaRmDhX0+6v
02q4w2eSWryyOMqAFSWdVOkUG+9i7cNMo9fWs4incm16NjNBtEt7aFtVqzD0
-----END CERTIFICATE-----
verify return:1
`

		certs, err := parseCertificates(opensslOutput)
		require.NoError(t, err, "解析单个证书应该成功")
		require.Len(t, certs, 1, "应该解析出1个证书")
		require.NotNil(t, certs[0], "证书不应该为nil")

		t.Logf("解析出证书主题: %s", certs[0].Subject.String())
	})

	t.Run("解析无证书数据", func(t *testing.T) {
		opensslOutput := `
CONNECTED(00000003)
depth=0 CN = example.com
verify error:num=18:self signed certificate
verify return:1
`

		certs, err := parseCertificates(opensslOutput)
		require.NoError(t, err, "解析无证书数据应该成功")
		require.Empty(t, certs, "证书列表应该为空")
	})

	t.Run("解析包含无效证书的数据", func(t *testing.T) {
		opensslOutput := `
CONNECTED(00000003)
-----BEGIN CERTIFICATE-----
这是无效的证书内容
-----END CERTIFICATE-----
`

		certs, err := parseCertificates(opensslOutput)
		require.Error(t, err, "解析无效证书应该失败")
		require.Empty(t, certs, "证书列表应该为空")
	})
}
