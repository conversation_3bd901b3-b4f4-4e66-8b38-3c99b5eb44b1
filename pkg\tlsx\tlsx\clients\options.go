// Author: chenjb
// Version: V1.0
// Date: 2025-06-24 17:02:13
// FilePath: /yaml_scan/pkg/tlsx/tlsx/clients/options.go
// Description: TLS客户端配置选项和连接参数定义

// Package clients 提供TLS客户端的配置选项和连接参数
// 包含所有可配置的TLS扫描参数、输出选项和连接设置
package clients

import (
	"crypto/x509"                                // 标准X.509证书处理
	"github.com/cloudflare/cfssl/revoke"         // CloudFlare证书撤销检查
	zx509 "github.com/zmap/zcrypto/x509"         // ZMap X.509证书处理
	"yaml_scan/pkg/fastdialer"                  // 快速拨号器
	"yaml_scan/pkg/goflags"                     // 命令行标志处理
	"yaml_scan/pkg/gologger"                    // 日志记录器
)

// Options 包含tlsx客户端的所有配置选项
// 涵盖输入输出、连接参数、扫描模式、证书验证等各个方面
type Options struct {
	// === 输入输出配置 ===
	OutputFile string                // 输出文件路径，指定结果保存位置
	Inputs     goflags.StringSlice   // 要扫描的目标列表（主机名、IP等）
	InputList  string                // 包含目标列表的文件路径

	// === TLS连接配置 ===
	ServerName               goflags.StringSlice // TLS服务器名称指示（SNI）列表
	RandomForEmptyServerName bool                // 当SNI为空时是否使用随机值
	ReversePtrSNI            bool                // 是否执行反向PTR查询获取SNI

	// === 输出控制选项 ===
	Verbose    bool // 是否启用详细输出模式
	Version    bool // 是否显示程序版本信息
	JSON       bool // 是否使用JSON格式输出
	DisplayDns bool // 是否显示从SSL证书中提取的唯一主机名
	TLSChain   bool // 是否输出完整的TLS证书链信息

	// === 已弃用选项 ===
	// AllCiphers bool // 已弃用：为历史兼容性保留，不应使用

	// === 探测和响应选项 ===
	ProbeStatus bool // 是否在JSON输出中包含连接错误信息
	CertsOnly   bool // 是否仅获取证书信息（使用ztls提前终止SSL）
	RespOnly    bool // 是否在CLI输出中仅显示TLS响应数据
	Silent      bool // 是否启用静默模式（减少输出）
	NoColor     bool // 是否禁用CLI输出的彩色显示

	// === 连接控制参数 ===
	Retries     int    // TLS连接失败时的重试次数
	Timeout     int    // 连接超时时间（秒）
	Concurrency int    // 并发扫描的线程数量
	Delay       string // 每个线程中请求之间的延迟时间

	// === 网络配置 ===
	Ports     goflags.StringSlice // 要扫描的端口列表
	Resolvers goflags.StringSlice // 自定义DNS解析器列表

	// === TLS协议配置 ===
	Ciphers       goflags.StringSlice // 自定义密码套件列表
	CACertificate string              // 自定义CA证书路径
	MinVersion    string              // 可接受的最低TLS版本
	MaxVersion    string              // 可接受的最高TLS版本
	ScanMode      string              // TLS扫描模式（ztls/ctls/openssl/auto）

	// === 证书验证选项 ===
	VerifyServerCertificate bool   // 是否启用服务器证书验证
	OpenSSLBinary           string // OpenSSL二进制文件路径

	// === 证书信息显示选项 ===
	SAN        bool // 是否显示主题备用名称（Subject Alternative Names）
	CN         bool // 是否显示主题通用名称（Common Name）
	SO         bool // 是否显示主题组织名称（Subject Organization）
	TLSVersion bool // 是否显示使用的TLS版本
	Cipher     bool // 是否显示使用的密码套件
	Serial     bool // 是否显示证书序列号

	// === 证书状态检查选项 ===
	Expired    bool // 是否检查并显示证书过期状态
	SelfSigned bool // 是否检查并显示证书是否自签名
	Untrusted  bool // 是否检查并显示证书是否不受信任
	MisMatched bool // 是否检查并显示证书是否与主机名不匹配
	Revoked    bool // 是否检查并显示证书撤销状态
	HardFail   bool // 证书撤销检查失败时是否视为已撤销

	// === 指纹和哈希选项 ===
	Hash string // 要计算和显示的证书哈希类型（md5/sha1/sha256）
	Jarm bool   // 是否计算JARM TLS指纹
	Cert bool   // 是否以PEM格式显示完整证书
	Ja3  bool   // 是否显示JA3客户端指纹哈希
	Ja3s bool   // 是否显示JA3S服务器指纹哈希

	// === IP和网络选项 ===
	ScanAllIPs bool                  // 是否扫描域名解析到的所有IP地址
	IPVersion  goflags.StringSlice   // 使用的IP版本（4/6）

	// === 高级扫描选项 ===
	WildcardCertCheck bool     // 是否启用通配符证书检查
	TlsVersionsEnum   bool     // 是否枚举所有支持的TLS版本
	TlsCiphersEnum    bool     // 是否枚举每个TLS版本支持的密码套件
	TLsCipherLevel    []string // 密码套件安全级别过滤器（weak/secure/insecure/all）

	// === 协议详细信息选项（仅ztls模式） ===
	ClientHello bool // 是否包含客户端Hello消息详细信息
	ServerHello bool // 是否包含服务器Hello消息详细信息

	// === 系统和维护选项 ===
	HealthCheck        bool // 是否执行系统健康检查
	DisableUpdateCheck bool // 是否禁用自动更新检查
	CipherConcurrency  int  // 密码套件枚举时的并发数量

	// === 内部组件 ===
	Fastdialer *fastdialer.Dialer // 快速拨号器实例，用于高效网络连接
}

// IsTLSRevoked 检查标准X.509证书是否已被撤销
// 使用CloudFlare的证书撤销检查库进行验证
//
// 参数:
//   - options: 配置选项，包含HardFail设置
//   - cert: 要检查的X.509证书
//
// 返回值:
//   - bool: 如果证书已撤销或检查失败（当HardFail=true时）返回true
//
// 撤销检查结果说明:
//   - false, false: 检查撤销状态时遇到错误
//   - false, true:  证书检查成功，未被撤销
//   - true, true:   证书检查成功，已被撤销
//   - true, false:  撤销状态检查失败导致验证失败
func IsTLSRevoked(options *Options, cert *x509.Certificate) bool {
	// 如果证书为空，根据HardFail设置决定返回值
	if cert == nil {
		return options.HardFail
	}

	// 使用CloudFlare库检查证书撤销状态
	// 忽略第二个返回值（检查是否成功），只关注撤销状态
	revoked, _ := revoke.VerifyCertificate(cert)
	return revoked
}

// IsZTLSRevoked 检查ZMap X.509证书是否已被撤销
// 这是IsTLSRevoked的ZMap版本，处理ztls.Certificate类型
//
// 参数:
//   - options: 配置选项，包含HardFail设置
//   - cert: 要检查的ZMap X.509证书
//
// 返回值:
//   - bool: 如果证书已撤销或转换/检查失败（当HardFail=true时）返回true
//
// 处理流程:
//   - 将ZMap证书转换为标准X.509证书
//   - 调用IsTLSRevoked进行撤销检查
//   - 转换失败时根据HardFail设置决定返回值
func IsZTLSRevoked(options *Options, cert *zx509.Certificate) bool {
	// 将ZMap证书转换为标准X.509证书
	xcert, err := x509.ParseCertificate(cert.Raw)
	if err != nil {
		// 转换失败，记录调试日志
		gologger.Debug().Msgf("ztls: failed to convert zx509->x509 while checking revocation status: %v", err)
		// 根据HardFail设置决定返回值
		return options.HardFail
	}

	// 使用标准证书撤销检查函数
	return IsTLSRevoked(options, xcert)
}

// EnumMode 定义枚举模式的类型
// 用于指定TLS扫描时要枚举的内容类型
type EnumMode uint

const (
	None    EnumMode = iota // 无枚举模式，仅进行基本连接
	Version                 // 枚举TLS版本，测试服务器支持的所有TLS版本
	Cipher                  // 枚举密码套件，测试特定TLS版本下支持的密码套件
)

// ConnectOptions 包含单次TLS连接的具体选项
// 用于控制特定连接的行为和参数
type ConnectOptions struct {
	SNI         string            // TLS服务器名称指示，用于支持多域名的服务器
	VersionTLS  string            // 要使用的特定TLS版本（如"1.2", "1.3"）
	Ciphers     []string          // 要尝试的密码套件名称列表
	CipherLevel []CipherSecLevel  // 密码套件安全级别过滤器，仅在枚举模式下使用
	EnumMode    EnumMode          // 当前连接的枚举模式（无/版本/密码套件）
}
