//
// Author: chenjb
// Version: V1.0
// Date: 2025-06-26
// FilePath: /yaml_scan/pkg/tlsx/tlsx/clients/options_test.go
// Description: clients包options.go的单元测试

package clients

import (
	"crypto/rand"
	"crypto/rsa"
	"crypto/x509"
	"crypto/x509/pkix"
	"math/big"
	"testing"
	"time"

	zx509 "github.com/zmap/zcrypto/x509"
	"github.com/stretchr/testify/require"
)

// createTestCertificate 创建用于测试的X.509证书
// 生成一个简单的自签名证书用于测试
func createTestCertificate(t *testing.T) *x509.Certificate {
	// 生成私钥
	privateKey, err := rsa.GenerateKey(rand.Reader, 2048)
	require.NoError(t, err, "生成私钥不应该出错")

	// 创建证书模板
	template := x509.Certificate{
		SerialNumber: big.NewInt(1),
		Subject: pkix.Name{
			Organization:  []string{"Test Org"},
			Country:       []string{"US"},
			Province:      []string{""},
			Locality:      []string{"Test City"},
			StreetAddress: []string{""},
			PostalCode:    []string{""},
		},
		NotBefore:    time.Now(),
		NotAfter:     time.Now().Add(365 * 24 * time.Hour),
		KeyUsage:     x509.KeyUsageKeyEncipherment | x509.KeyUsageDigitalSignature,
		ExtKeyUsage:  []x509.ExtKeyUsage{x509.ExtKeyUsageServerAuth},
		IPAddresses:  nil,
	}

	// 创建自签名证书
	certDER, err := x509.CreateCertificate(rand.Reader, &template, &template, &privateKey.PublicKey, privateKey)
	require.NoError(t, err, "创建证书不应该出错")

	// 解析证书
	cert, err := x509.ParseCertificate(certDER)
	require.NoError(t, err, "解析证书不应该出错")

	return cert
}

// createTestZTLSCertificate 创建用于测试的ZMap X.509证书
// 生成一个简单的ZMap证书用于测试
func createTestZTLSCertificate(t *testing.T) *zx509.Certificate {
	// 先创建标准证书
	stdCert := createTestCertificate(t)
	
	// 转换为ZMap证书
	ztlsCert, err := zx509.ParseCertificate(stdCert.Raw)
	require.NoError(t, err, "转换为ZMap证书不应该出错")

	return ztlsCert
}

// TestIsTLSRevoked 测试标准X.509证书撤销检查功能
// 验证证书撤销状态检查的基本逻辑
func TestIsTLSRevoked(t *testing.T) {
	tests := []struct {
		name        string           // 测试用例名称
		options     *Options         // 配置选项
		cert        *x509.Certificate // 测试证书
		description string           // 测试描述
	}{
		{
			name: "HardFail为false时的nil证书",
			options: &Options{
				HardFail: false,
			},
			cert:        nil,
			description: "HardFail为false时，nil证书应该返回false",
		},
		{
			name: "HardFail为true时的nil证书",
			options: &Options{
				HardFail: true,
			},
			cert:        nil,
			description: "HardFail为true时，nil证书应该返回true",
		},
		{
			name: "有效证书的撤销检查",
			options: &Options{
				HardFail: false,
			},
			cert:        createTestCertificate(t),
			description: "有效的测试证书应该不被撤销",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsTLSRevoked(tt.options, tt.cert)
			
			if tt.cert == nil {
				// 对于nil证书，结果应该等于HardFail设置
				require.Equal(t, tt.options.HardFail, result, tt.description)
			} else {
				// 对于有效证书，我们主要验证函数不会崩溃
				// 实际的撤销状态取决于证书和网络连接
				t.Logf("证书撤销检查结果: %v", result)
			}
		})
	}
}

// TestIsZTLSRevoked 测试ZMap X.509证书撤销检查功能
// 验证ZMap证书撤销状态检查的基本逻辑
func TestIsZTLSRevoked(t *testing.T) {
	tests := []struct {
		name        string            // 测试用例名称
		options     *Options          // 配置选项
		cert        *zx509.Certificate // 测试证书
		description string            // 测试描述
	}{
		{
			name: "HardFail为false时的nil证书",
			options: &Options{
				HardFail: false,
			},
			cert:        nil,
			description: "HardFail为false时，nil证书应该返回false",
		},
		{
			name: "HardFail为true时的nil证书",
			options: &Options{
				HardFail: true,
			},
			cert:        nil,
			description: "HardFail为true时，nil证书应该返回true",
		},
		{
			name: "有效ZMap证书的撤销检查",
			options: &Options{
				HardFail: false,
			},
			cert:        createTestZTLSCertificate(t),
			description: "有效的ZMap测试证书应该不被撤销",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsZTLSRevoked(tt.options, tt.cert)
			
			if tt.cert == nil {
				// 对于nil证书，结果应该等于HardFail设置
				require.Equal(t, tt.options.HardFail, result, tt.description)
			} else {
				// 对于有效证书，我们主要验证函数不会崩溃
				// 实际的撤销状态取决于证书和网络连接
				t.Logf("ZMap证书撤销检查结果: %v", result)
			}
		})
	}
}

// TestEnumMode 测试枚举模式常量
// 验证枚举模式的定义和值
func TestEnumMode(t *testing.T) {
	// 验证枚举模式的值
	require.Equal(t, EnumMode(0), None, "None模式应该是0")
	require.Equal(t, EnumMode(1), Version, "Version模式应该是1")
	require.Equal(t, EnumMode(2), Cipher, "Cipher模式应该是2")

	// 验证枚举模式的类型
	var mode EnumMode
	mode = None
	require.Equal(t, None, mode, "应该能够赋值None模式")
	
	mode = Version
	require.Equal(t, Version, mode, "应该能够赋值Version模式")
	
	mode = Cipher
	require.Equal(t, Cipher, mode, "应该能够赋值Cipher模式")
}

// TestConnectOptions 测试连接选项结构
// 验证ConnectOptions结构的基本功能
func TestConnectOptions(t *testing.T) {
	// 创建连接选项
	options := ConnectOptions{
		SNI:        "www.example.com",
		VersionTLS: "1.2",
		Ciphers:    []string{"TLS_RSA_WITH_AES_256_GCM_SHA384"},
		CipherLevel: []CipherSecLevel{Secure, Weak},
		EnumMode:   Version,
	}

	// 验证字段设置
	require.Equal(t, "www.example.com", options.SNI, "SNI应该正确设置")
	require.Equal(t, "1.2", options.VersionTLS, "TLS版本应该正确设置")
	require.Len(t, options.Ciphers, 1, "密码套件列表应该有一个元素")
	require.Equal(t, "TLS_RSA_WITH_AES_256_GCM_SHA384", options.Ciphers[0], "密码套件应该正确设置")
	require.Len(t, options.CipherLevel, 2, "密码套件级别应该有两个元素")
	require.Equal(t, Version, options.EnumMode, "枚举模式应该正确设置")
}

// TestOptions 测试主要配置选项结构
// 验证Options结构的基本功能和默认值
func TestOptions(t *testing.T) {
	// 创建配置选项
	options := &Options{
		OutputFile:   "/tmp/output.json",
		Verbose:      true,
		JSON:         true,
		Retries:      3,
		Timeout:      10,
		Concurrency:  5,
		ScanMode:     "ctls",
		ProbeStatus:  true,
		Jarm:         true,
		TlsVersionsEnum: true,
		TlsCiphersEnum:  true,
		TLsCipherLevel:  []string{"secure", "weak"},
	}

	// 验证字段设置
	require.Equal(t, "/tmp/output.json", options.OutputFile, "输出文件路径应该正确设置")
	require.True(t, options.Verbose, "详细模式应该启用")
	require.True(t, options.JSON, "JSON模式应该启用")
	require.Equal(t, 3, options.Retries, "重试次数应该正确设置")
	require.Equal(t, 10, options.Timeout, "超时时间应该正确设置")
	require.Equal(t, 5, options.Concurrency, "并发数应该正确设置")
	require.Equal(t, "ctls", options.ScanMode, "扫描模式应该正确设置")
	require.True(t, options.ProbeStatus, "探测状态应该启用")
	require.True(t, options.Jarm, "JARM应该启用")
	require.True(t, options.TlsVersionsEnum, "TLS版本枚举应该启用")
	require.True(t, options.TlsCiphersEnum, "TLS密码套件枚举应该启用")
	require.Len(t, options.TLsCipherLevel, 2, "密码套件级别应该有两个元素")
	require.Equal(t, "secure", options.TLsCipherLevel[0], "第一个密码套件级别应该是secure")
	require.Equal(t, "weak", options.TLsCipherLevel[1], "第二个密码套件级别应该是weak")
}

// TestOptionsDefaults 测试配置选项的默认值
// 验证Options结构的零值行为
func TestOptionsDefaults(t *testing.T) {
	// 创建空的配置选项
	options := &Options{}

	// 验证默认值
	require.Empty(t, options.OutputFile, "输出文件路径默认应该为空")
	require.False(t, options.Verbose, "详细模式默认应该关闭")
	require.False(t, options.JSON, "JSON模式默认应该关闭")
	require.Equal(t, 0, options.Retries, "重试次数默认应该为0")
	require.Equal(t, 0, options.Timeout, "超时时间默认应该为0")
	require.Equal(t, 0, options.Concurrency, "并发数默认应该为0")
	require.Empty(t, options.ScanMode, "扫描模式默认应该为空")
	require.False(t, options.ProbeStatus, "探测状态默认应该关闭")
	require.False(t, options.Jarm, "JARM默认应该关闭")
	require.False(t, options.TlsVersionsEnum, "TLS版本枚举默认应该关闭")
	require.False(t, options.TlsCiphersEnum, "TLS密码套件枚举默认应该关闭")
	require.Nil(t, options.TLsCipherLevel, "密码套件级别默认应该为nil")
	require.Nil(t, options.Fastdialer, "快速拨号器默认应该为nil")
}
