<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TLS详细架构解析</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.7;
            color: #333;
            background: #ffffff;
            margin: 0;
            padding: 0;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background: #ffffff;
        }
        
        h1 {
            color: #2c3e50;
            margin: 40px 0 30px 0;
            font-size: 2.2em;
            font-weight: 700;
            line-height: 1.2;
        }

        h2 {
            color: #2c3e50;
            margin: 40px 0 20px 0;
            font-size: 1.6em;
            font-weight: 600;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 10px;
        }

        h3 {
            color: #495057;
            margin: 30px 0 15px 0;
            font-size: 1.3em;
            font-weight: 600;
        }

        h4 {
            color: #6c757d;
            margin: 20px 0 10px 0;
            font-size: 1.1em;
            font-weight: 600;
        }

        p {
            margin: 16px 0;
            color: #495057;
        }

        ul, ol {
            margin: 16px 0;
            padding-left: 24px;
        }

        li {
            margin: 8px 0;
            color: #495057;
        }

        strong {
            color: #2c3e50;
        }

        code {
            background: #f8f9fa;
            color: #e83e8c;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
            font-size: 0.9em;
        }
        
        .architecture-overview {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            color: white;
            padding: 25px;
            border-radius: 10px;
            margin: 20px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .layer-card {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        
        .protocol-stack {
            margin: 30px 0;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            overflow: hidden;
            background: #ffffff;
        }

        .stack-layer {
            padding: 20px;
            border-bottom: 1px solid #dee2e6;
            position: relative;
        }

        .stack-layer:last-child {
            border-bottom: none;
        }

        .application-layer {
            background: #f8f9fa;
            border-left: 4px solid #007bff;
        }

        .tls-layer {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
        }

        .transport-layer {
            background: #d1ecf1;
            border-left: 4px solid #17a2b8;
        }

        .network-layer {
            background: #d4edda;
            border-left: 4px solid #28a745;
        }

        .layer-title {
            font-size: 1.2em;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .layer-protocols {
            font-size: 0.95em;
            color: #6c757d;
            margin-bottom: 8px;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, monospace;
        }

        .layer-description {
            font-size: 0.9em;
            color: #6c757d;
            line-height: 1.5;
        }
        
        .component-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .component-card {
            background: white;
            border: 2px solid #ddd;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .component-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }
        
        .handshake-protocol {
            border-left: 4px solid #e74c3c;
        }
        
        .record-protocol {
            border-left: 4px solid #3498db;
        }
        
        .alert-protocol {
            border-left: 4px solid #f39c12;
        }
        
        .change-cipher-protocol {
            border-left: 4px solid #27ae60;
        }
        
        .data-flow {
            background: #e8f5e8;
            border: 2px solid #27ae60;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .flow-diagram {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 24px 0;
            flex-wrap: wrap;
            gap: 12px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }

        .flow-step {
            background: #ffffff;
            color: #495057;
            padding: 12px 16px;
            border-radius: 6px;
            font-weight: 500;
            min-width: 100px;
            text-align: center;
            border: 1px solid #dee2e6;
            font-size: 0.9em;
        }

        .arrow {
            font-size: 1.2em;
            color: #6c757d;
            margin: 0 4px;
        }

        .vertical-flow {
            flex-direction: column;
            align-items: center;
        }

        .vertical-flow .arrow {
            transform: rotate(90deg);
            margin: 8px 0;
        }

        .process-step {
            background: #ffffff;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 16px;
            margin: 8px 0;
            text-align: center;
            min-width: 140px;
        }

        .step-number {
            background: #007bff;
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8em;
            font-weight: 600;
            margin-right: 8px;
        }

        .diagram-container {
            background: #ffffff;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 24px;
            margin: 24px 0;
        }

        .diagram-title {
            font-size: 1.1em;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 16px;
            text-align: center;
        }
        
        .detailed-breakdown {
            background: #fff5f5;
            border: 2px solid #e74c3c;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .security-layer {
            background: #f0f8ff;
            border: 2px solid #4169e1;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        
        th {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 15px;
            text-align: left;
        }
        
        td {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
        }
        
        tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            overflow-x: auto;
            font-family: 'Consolas', monospace;
        }
        
        .highlight {
            background: linear-gradient(45deg, #ffeaa7, #fdcb6e);
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            border-left: 4px solid #e17055;
        }
        
        .state-machine {
            background: #f8f9fa;
            border: 2px solid #6c757d;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .state-box {
            display: inline-block;
            background: linear-gradient(135deg, #a29bfe, #6c5ce7);
            color: white;
            padding: 12px 24px;
            margin: 8px;
            border-radius: 25px;
            font-weight: bold;
            box-shadow: 0 4px 12px rgba(162, 155, 254, 0.3);
            transition: all 0.3s ease;
            border: 2px solid rgba(255,255,255,0.2);
        }

        .state-box:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(162, 155, 254, 0.4);
        }

        .handshake-flow {
            display: grid;
            grid-template-columns: 1fr auto 1fr;
            gap: 20px;
            align-items: center;
            margin: 20px 0;
            padding: 25px;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 15px;
            border: 2px solid #dee2e6;
        }

        .client-side {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            color: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(116, 185, 255, 0.3);
        }

        .server-side {
            background: linear-gradient(135deg, #00b894, #00a085);
            color: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0, 184, 148, 0.3);
        }

        .message-arrow {
            font-size: 3em;
            color: #e74c3c;
            text-align: center;
            animation: slideArrow 3s infinite;
        }

        @keyframes slideArrow {
            0%, 100% { transform: translateX(0); }
            50% { transform: translateX(10px); }
        }

        .step-number {
            background: #e74c3c;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏗️ TLS详细架构解析</h1>
        
        <div class="architecture-overview">
            <h3 style="color: white; border: none; margin-top: 0;">🎯 架构概述</h3>
            <p>TLS (Transport Layer Security) 是一个分层的安全协议，位于应用层和传输层之间，为上层应用提供透明的安全服务。TLS架构采用模块化设计，由多个子协议组成，每个子协议负责特定的安全功能。</p>
        </div>
        
        <section id="protocol-stack">
            <h2>📚 协议栈结构</h2>
            
            <div class="layer-card">
                <h3>完整协议栈视图</h3>
                <div class="protocol-stack">
                    <div class="stack-layer application-layer">
                        <div class="layer-title">应用层 (Application Layer)</div>
                        <div class="layer-protocols">HTTP, SMTP, FTP, IMAP, POP3, LDAP</div>
                        <div class="layer-description">应用程序通过TLS获得透明的安全传输服务</div>
                    </div>

                    <div class="stack-layer tls-layer">
                        <div class="layer-title">TLS安全层 (TLS Security Layer)</div>
                        <div class="layer-protocols">握手协议, 记录协议, 警报协议, 密码变更协议</div>
                        <div class="layer-description">提供端到端的加密、身份认证、数据完整性保护</div>
                    </div>

                    <div class="stack-layer transport-layer">
                        <div class="layer-title">传输层 (Transport Layer)</div>
                        <div class="layer-protocols">TCP (传输控制协议)</div>
                        <div class="layer-description">提供可靠的、面向连接的字节流传输服务</div>
                    </div>

                    <div class="stack-layer network-layer">
                        <div class="layer-title">网络层 (Network Layer)</div>
                        <div class="layer-protocols">IP (Internet Protocol)</div>
                        <div class="layer-description">提供端到端的数据包路由和传输服务</div>
                    </div>
                </div>
            </div>
            
            <div class="highlight">
                <h4>🔍 TLS在协议栈中的位置</h4>
                <ul>
                    <li><strong>位置</strong>: 介于应用层和传输层之间</li>
                    <li><strong>透明性</strong>: 对应用层透明，应用无需修改即可获得安全性</li>
                    <li><strong>依赖性</strong>: 依赖TCP提供可靠传输</li>
                    <li><strong>服务性</strong>: 为上层应用提供安全传输服务</li>
                </ul>
            </div>
        </section>
        
        <section id="tls-components">
            <h2>🧩 TLS协议层架构</h2>

            <div class="diagram-container">
                <div class="diagram-title">TLS协议分层架构</div>
                <p style="text-align: center; color: #6c757d; margin-bottom: 24px;">
                    TLS协议采用分层设计，底层是TLS记录协议，上层包含四个子协议
                </p>

                <!-- 应用层 -->
                <div style="background: #e3f2fd; border: 2px solid #2196f3; border-radius: 8px; padding: 16px; margin-bottom: 16px; text-align: center;">
                    <strong style="color: #1976d2;">应用层协议</strong><br>
                    <small style="color: #424242;">HTTP, SMTP, FTP, IMAP等应用协议</small>
                </div>

                <!-- TLS上层协议 -->
                <div style="border: 2px solid #ff9800; border-radius: 8px; margin-bottom: 16px; overflow: hidden;">
                    <div style="background: #fff3e0; padding: 12px; text-align: center; border-bottom: 1px solid #ff9800;">
                        <strong style="color: #f57c00;">TLS上层协议</strong>
                    </div>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1px; background: #ff9800;">
                        <div style="background: #ffffff; padding: 16px; text-align: center;">
                            <div style="color: #1976d2; font-size: 1.5em; margin-bottom: 8px;">🤝</div>
                            <strong style="color: #1976d2;">握手协议</strong><br>
                            <small style="color: #666;">Handshake Protocol</small><br>
                            <small style="color: #666;">建立安全连接</small>
                        </div>

                        <div style="background: #ffffff; padding: 16px; text-align: center;">
                            <div style="color: #d32f2f; font-size: 1.5em; margin-bottom: 8px;">⚠️</div>
                            <strong style="color: #d32f2f;">警报协议</strong><br>
                            <small style="color: #666;">Alert Protocol</small><br>
                            <small style="color: #666;">错误和状态通知</small>
                        </div>

                        <div style="background: #ffffff; padding: 16px; text-align: center;">
                            <div style="color: #388e3c; font-size: 1.5em; margin-bottom: 8px;">🔄</div>
                            <strong style="color: #388e3c;">密码规格变更协议</strong><br>
                            <small style="color: #666;">Change Cipher Spec</small><br>
                            <small style="color: #666;">切换加密参数</small>
                        </div>

                        <div style="background: #ffffff; padding: 16px; text-align: center;">
                            <div style="color: #7b1fa2; font-size: 1.5em; margin-bottom: 8px;">📊</div>
                            <strong style="color: #7b1fa2;">应用数据协议</strong><br>
                            <small style="color: #666;">Application Data</small><br>
                            <small style="color: #666;">传输应用数据</small>
                        </div>
                    </div>
                </div>

                <!-- TLS记录协议 (底层) -->
                <div style="background: #e8f5e8; border: 2px solid #4caf50; border-radius: 8px; padding: 20px; margin-bottom: 16px;">
                    <div style="text-align: center; margin-bottom: 16px;">
                        <div style="color: #2e7d32; font-size: 2em; margin-bottom: 8px;">📝</div>
                        <strong style="color: #2e7d32; font-size: 1.2em;">TLS记录协议 (底层)</strong><br>
                        <small style="color: #424242;">负责使用对称密码对消息进行加密</small>
                    </div>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: 12px;">
                        <div style="background: #ffffff; border: 1px solid #4caf50; border-radius: 6px; padding: 12px; text-align: center;">
                            <div style="color: #2e7d32; margin-bottom: 4px;">✂️</div>
                            <strong style="font-size: 0.9em;">分片</strong><br>
                            <small style="color: #666;">≤16KB</small>
                        </div>

                        <div style="background: #ffffff; border: 1px solid #4caf50; border-radius: 6px; padding: 12px; text-align: center;">
                            <div style="color: #2e7d32; margin-bottom: 4px;">🗜️</div>
                            <strong style="font-size: 0.9em;">压缩</strong><br>
                            <small style="color: #666;">可选</small>
                        </div>

                        <div style="background: #ffffff; border: 1px solid #4caf50; border-radius: 6px; padding: 12px; text-align: center;">
                            <div style="color: #2e7d32; margin-bottom: 4px;">🔐</div>
                            <strong style="font-size: 0.9em;">加密</strong><br>
                            <small style="color: #666;">对称密码</small>
                        </div>

                        <div style="background: #ffffff; border: 1px solid #4caf50; border-radius: 6px; padding: 12px; text-align: center;">
                            <div style="color: #2e7d32; margin-bottom: 4px;">✅</div>
                            <strong style="font-size: 0.9em;">完整性</strong><br>
                            <small style="color: #666;">MAC验证</small>
                        </div>

                        <div style="background: #ffffff; border: 1px solid #4caf50; border-radius: 6px; padding: 12px; text-align: center;">
                            <div style="color: #2e7d32; margin-bottom: 4px;">📋</div>
                            <strong style="font-size: 0.9em;">封装</strong><br>
                            <small style="color: #666;">添加头部</small>
                        </div>
                    </div>
                </div>

                <!-- 传输层 -->
                <div style="background: #f3e5f5; border: 2px solid #9c27b0; border-radius: 8px; padding: 16px; text-align: center;">
                    <strong style="color: #7b1fa2;">传输层 (TCP)</strong><br>
                    <small style="color: #424242;">提供可靠的字节流传输服务</small>
                </div>
            </div>
        </section>

        <section id="tls-layer-detail">
            <h2>📋 TLS协议层详细解析</h2>

            <div class="diagram-container">
                <div class="diagram-title">TLS记录协议 - 底层核心</div>
                <p style="color: #6c757d; margin-bottom: 20px;">
                    TLS记录协议是TLS的底层协议，负责使用对称密码对消息进行加密，为上层协议提供安全的传输通道。
                </p>

                <div style="background: #e8f5e8; border: 2px solid #4caf50; border-radius: 8px; padding: 20px; margin: 20px 0;">
                    <h4 style="color: #2e7d32; margin-bottom: 16px;">🔧 记录协议核心功能</h4>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 16px;">
                        <div style="background: #ffffff; border: 1px solid #4caf50; border-radius: 6px; padding: 16px;">
                            <h5 style="color: #2e7d32; margin-bottom: 8px;">🔐 对称加密</h5>
                            <ul style="margin: 0; color: #424242; font-size: 0.9em;">
                                <li>使用协商的对称密钥</li>
                                <li>AES、ChaCha20等算法</li>
                                <li>保证数据机密性</li>
                            </ul>
                        </div>

                        <div style="background: #ffffff; border: 1px solid #4caf50; border-radius: 6px; padding: 16px;">
                            <h5 style="color: #2e7d32; margin-bottom: 8px;">✅ 完整性保护</h5>
                            <ul style="margin: 0; color: #424242; font-size: 0.9em;">
                                <li>MAC或AEAD验证</li>
                                <li>防止数据篡改</li>
                                <li>检测传输错误</li>
                            </ul>
                        </div>

                        <div style="background: #ffffff; border: 1px solid #4caf50; border-radius: 6px; padding: 16px;">
                            <h5 style="color: #2e7d32; margin-bottom: 8px;">📦 数据封装</h5>
                            <ul style="margin: 0; color: #424242; font-size: 0.9em;">
                                <li>添加TLS记录头部</li>
                                <li>分片大数据块</li>
                                <li>序列号防重放</li>
                            </ul>
                        </div>

                        <div style="background: #ffffff; border: 1px solid #4caf50; border-radius: 6px; padding: 16px;">
                            <h5 style="color: #2e7d32; margin-bottom: 8px;">🔄 透明传输</h5>
                            <ul style="margin: 0; color: #424242; font-size: 0.9em;">
                                <li>对上层协议透明</li>
                                <li>统一的安全接口</li>
                                <li>可靠的数据传输</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <div class="diagram-container">
                <div class="diagram-title">TLS上层协议详解</div>
                <p style="color: #6c757d; margin-bottom: 20px;">
                    TLS上层包含四个子协议，每个协议负责特定的功能，共同构成完整的TLS安全框架。
                </p>

                <!-- 握手协议 -->
                <div style="background: #e3f2fd; border: 2px solid #2196f3; border-radius: 8px; padding: 20px; margin: 20px 0;">
                    <h4 style="color: #1976d2; margin-bottom: 16px;">🤝 握手协议 (Handshake Protocol)</h4>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                        <div>
                            <h5 style="color: #1976d2; margin-bottom: 12px;">主要功能</h5>
                            <ul style="color: #424242; margin: 0;">
                                <li><strong>协议版本协商</strong>: 确定使用的TLS版本</li>
                                <li><strong>密码套件选择</strong>: 协商加密算法组合</li>
                                <li><strong>密钥交换</strong>: 安全地交换密钥材料</li>
                                <li><strong>身份认证</strong>: 验证通信双方身份</li>
                                <li><strong>会话建立</strong>: 建立安全通信会话</li>
                            </ul>
                        </div>

                        <div>
                            <h5 style="color: #1976d2; margin-bottom: 12px;">消息类型</h5>
                            <div style="background: #ffffff; border: 1px solid #2196f3; border-radius: 6px; padding: 12px;">
                                <div style="font-family: monospace; font-size: 0.9em; color: #424242;">
                                    • ClientHello<br>
                                    • ServerHello<br>
                                    • Certificate<br>
                                    • ServerKeyExchange<br>
                                    • CertificateRequest<br>
                                    • ServerHelloDone<br>
                                    • ClientKeyExchange<br>
                                    • CertificateVerify<br>
                                    • Finished
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 警报协议 -->
                <div style="background: #fff3e0; border: 2px solid #ff9800; border-radius: 8px; padding: 20px; margin: 20px 0;">
                    <h4 style="color: #f57c00; margin-bottom: 16px;">⚠️ 警报协议 (Alert Protocol)</h4>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                        <div>
                            <h5 style="color: #f57c00; margin-bottom: 12px;">协议功能</h5>
                            <ul style="color: #424242; margin: 0;">
                                <li><strong>错误通知</strong>: 报告协议错误和异常</li>
                                <li><strong>警告信息</strong>: 发送非致命警告</li>
                                <li><strong>连接关闭</strong>: 正常关闭TLS连接</li>
                                <li><strong>状态通知</strong>: 通知连接状态变化</li>
                            </ul>
                        </div>

                        <div>
                            <h5 style="color: #f57c00; margin-bottom: 12px;">警报级别</h5>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px;">
                                <div style="background: #fff8e1; border: 1px solid #ffb74d; border-radius: 6px; padding: 12px; text-align: center;">
                                    <strong style="color: #f57c00;">Warning (1)</strong><br>
                                    <small style="color: #666;">警告级别<br>连接可继续</small>
                                </div>
                                <div style="background: #ffebee; border: 1px solid #e57373; border-radius: 6px; padding: 12px; text-align: center;">
                                    <strong style="color: #d32f2f;">Fatal (2)</strong><br>
                                    <small style="color: #666;">致命错误<br>必须关闭连接</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 密码规格变更协议 -->
                <div style="background: #e8f5e8; border: 2px solid #4caf50; border-radius: 8px; padding: 20px; margin: 20px 0;">
                    <h4 style="color: #388e3c; margin-bottom: 16px;">🔄 密码规格变更协议 (Change Cipher Spec Protocol)</h4>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                        <div>
                            <h5 style="color: #388e3c; margin-bottom: 12px;">协议作用</h5>
                            <ul style="color: #424242; margin: 0;">
                                <li><strong>激活新参数</strong>: 启用握手协商的安全参数</li>
                                <li><strong>切换加密状态</strong>: 从明文切换到加密通信</li>
                                <li><strong>同步状态</strong>: 确保双方状态一致</li>
                                <li><strong>简单协议</strong>: 只有一个字节的消息</li>
                            </ul>
                        </div>

                        <div>
                            <h5 style="color: #388e3c; margin-bottom: 12px;">使用时机</h5>
                            <div style="background: #ffffff; border: 1px solid #4caf50; border-radius: 6px; padding: 12px;">
                                <div style="color: #424242; font-size: 0.9em;">
                                    <strong>1. 初始握手</strong><br>
                                    握手完成后激活加密<br><br>
                                    <strong>2. 重新协商</strong><br>
                                    更新安全参数时<br><br>
                                    <strong>3. 会话恢复</strong><br>
                                    恢复之前的会话
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 应用数据协议 -->
                <div style="background: #f3e5f5; border: 2px solid #9c27b0; border-radius: 8px; padding: 20px; margin: 20px 0;">
                    <h4 style="color: #7b1fa2; margin-bottom: 16px;">📊 应用数据协议 (Application Data Protocol)</h4>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                        <div>
                            <h5 style="color: #7b1fa2; margin-bottom: 12px;">协议特点</h5>
                            <ul style="color: #424242; margin: 0;">
                                <li><strong>透明传输</strong>: 对应用层完全透明</li>
                                <li><strong>安全保护</strong>: 自动加密和完整性保护</li>
                                <li><strong>高效传输</strong>: 最小化协议开销</li>
                                <li><strong>流式处理</strong>: 支持流式数据传输</li>
                            </ul>
                        </div>

                        <div>
                            <h5 style="color: #7b1fa2; margin-bottom: 12px;">数据流向</h5>
                            <div style="background: #ffffff; border: 1px solid #9c27b0; border-radius: 6px; padding: 12px;">
                                <div style="text-align: center; color: #424242; font-size: 0.9em;">
                                    <div style="background: #e1bee7; padding: 8px; border-radius: 4px; margin: 4px 0;">应用层数据</div>
                                    <div style="color: #7b1fa2;">⬇</div>
                                    <div style="background: #e1bee7; padding: 8px; border-radius: 4px; margin: 4px 0;">TLS记录协议</div>
                                    <div style="color: #7b1fa2;">⬇</div>
                                    <div style="background: #e1bee7; padding: 8px; border-radius: 4px; margin: 4px 0;">TCP传输</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="record-protocol-detail">
            <h2>📋 TLS记录协议设计架构详解</h2>

            <div class="diagram-container">
                <div class="diagram-title">TLS记录协议设计理念</div>
                <p style="color: #6c757d; margin-bottom: 20px;">
                    TLS记录协议是TLS架构的核心基础，采用<strong>分层抽象</strong>的设计理念，为上层协议提供统一的安全传输服务。
                    它将复杂的加密操作封装在底层，使上层协议可以专注于各自的业务逻辑。
                </p>

                <div style="background: #e8f5e8; border: 2px solid #4caf50; border-radius: 8px; padding: 20px; margin: 20px 0;">
                    <h4 style="color: #2e7d32; margin-bottom: 16px;">🏗️ 记录协议设计原则</h4>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 16px;">
                        <div style="background: #ffffff; border: 1px solid #4caf50; border-radius: 6px; padding: 16px;">
                            <h5 style="color: #2e7d32; margin-bottom: 8px;">🎯 单一职责原则</h5>
                            <ul style="margin: 0; color: #424242; font-size: 0.9em;">
                                <li>专注于数据的安全传输</li>
                                <li>不涉及上层协议的业务逻辑</li>
                                <li>提供统一的加密抽象</li>
                            </ul>
                        </div>

                        <div style="background: #ffffff; border: 1px solid #4caf50; border-radius: 6px; padding: 16px;">
                            <h5 style="color: #2e7d32; margin-bottom: 8px;">🔄 透明性原则</h5>
                            <ul style="margin: 0; color: #424242; font-size: 0.9em;">
                                <li>对上层协议完全透明</li>
                                <li>隐藏加密实现细节</li>
                                <li>提供简洁的API接口</li>
                            </ul>
                        </div>

                        <div style="background: #ffffff; border: 1px solid #4caf50; border-radius: 6px; padding: 16px;">
                            <h5 style="color: #2e7d32; margin-bottom: 8px;">🛡️ 安全性原则</h5>
                            <ul style="margin: 0; color: #424242; font-size: 0.9em;">
                                <li>强制的完整性保护</li>
                                <li>防重放攻击机制</li>
                                <li>安全的状态管理</li>
                            </ul>
                        </div>

                        <div style="background: #ffffff; border: 1px solid #4caf50; border-radius: 6px; padding: 16px;">
                            <h5 style="color: #2e7d32; margin-bottom: 8px;">⚡ 效率原则</h5>
                            <ul style="margin: 0; color: #424242; font-size: 0.9em;">
                                <li>最小化协议开销</li>
                                <li>优化的数据处理流程</li>
                                <li>高效的内存管理</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <div class="diagram-container">
                <div class="diagram-title">记录协议内部架构设计</div>

                <div style="background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 20px; margin: 20px 0;">
                    <h4 style="color: #495057; margin-bottom: 16px;">🔧 核心组件架构</h4>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                        <!-- 左侧：状态管理 -->
                        <div style="background: #ffffff; border: 1px solid #dee2e6; border-radius: 6px; padding: 16px;">
                            <h5 style="color: #1976d2; margin-bottom: 12px;">📊 状态管理器 (State Manager)</h5>
                            <div style="background: #e3f2fd; border-radius: 4px; padding: 12px; margin-bottom: 12px;">
                                <strong>当前状态 (Current State)</strong>
                                <ul style="margin: 8px 0 0 0; font-size: 0.9em;">
                                    <li>当前使用的加密参数</li>
                                    <li>读写序列号</li>
                                    <li>连接状态标识</li>
                                </ul>
                            </div>
                            <div style="background: #fff3e0; border-radius: 4px; padding: 12px;">
                                <strong>待定状态 (Pending State)</strong>
                                <ul style="margin: 8px 0 0 0; font-size: 0.9em;">
                                    <li>握手协商的新参数</li>
                                    <li>等待激活的密钥</li>
                                    <li>新的安全配置</li>
                                </ul>
                            </div>
                        </div>

                        <!-- 右侧：数据处理器 -->
                        <div style="background: #ffffff; border: 1px solid #dee2e6; border-radius: 6px; padding: 16px;">
                            <h5 style="color: #388e3c; margin-bottom: 12px;">⚙️ 数据处理器 (Data Processor)</h5>
                            <div style="background: #e8f5e8; border-radius: 4px; padding: 12px; margin-bottom: 12px;">
                                <strong>输入处理</strong>
                                <ul style="margin: 8px 0 0 0; font-size: 0.9em;">
                                    <li>接收上层协议数据</li>
                                    <li>数据分片和组装</li>
                                    <li>压缩处理(可选)</li>
                                </ul>
                            </div>
                            <div style="background: #f3e5f5; border-radius: 4px; padding: 12px;">
                                <strong>输出处理</strong>
                                <ul style="margin: 8px 0 0 0; font-size: 0.9em;">
                                    <li>加密和MAC计算</li>
                                    <li>添加记录头部</li>
                                    <li>发送到传输层</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="diagram-container">
                <div class="diagram-title">记录协议状态机设计</div>

                <div style="background: #fff3e0; border: 2px solid #ff9800; border-radius: 8px; padding: 20px; margin: 20px 0;">
                    <h4 style="color: #f57c00; margin-bottom: 16px;">🔄 连接状态机</h4>

                    <div style="display: flex; justify-content: center; align-items: center; margin: 20px 0;">
                        <div style="display: grid; grid-template-columns: repeat(5, 1fr); gap: 16px; align-items: center;">
                            <div style="background: #4caf50; color: white; padding: 12px; border-radius: 6px; text-align: center; font-size: 0.9em;">
                                <strong>INIT</strong><br>
                                <small>初始状态</small>
                            </div>

                            <div style="color: #f57c00; font-size: 1.5em; text-align: center;">→</div>

                            <div style="background: #2196f3; color: white; padding: 12px; border-radius: 6px; text-align: center; font-size: 0.9em;">
                                <strong>HANDSHAKE</strong><br>
                                <small>握手进行中</small>
                            </div>

                            <div style="color: #f57c00; font-size: 1.5em; text-align: center;">→</div>

                            <div style="background: #ff9800; color: white; padding: 12px; border-radius: 6px; text-align: center; font-size: 0.9em;">
                                <strong>ESTABLISHED</strong><br>
                                <small>连接建立</small>
                            </div>
                        </div>
                    </div>

                    <div style="margin-top: 20px;">
                        <h5 style="color: #f57c00; margin-bottom: 12px;">状态转换条件</h5>
                        <table style="width: 100%; border-collapse: collapse; border: 1px solid #dee2e6;">
                            <thead>
                                <tr style="background: #fff8e1;">
                                    <th style="padding: 8px; border: 1px solid #dee2e6; text-align: left;">当前状态</th>
                                    <th style="padding: 8px; border: 1px solid #dee2e6; text-align: left;">触发事件</th>
                                    <th style="padding: 8px; border: 1px solid #dee2e6; text-align: left;">目标状态</th>
                                    <th style="padding: 8px; border: 1px solid #dee2e6; text-align: left;">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td style="padding: 6px; border: 1px solid #dee2e6;">INIT</td>
                                    <td style="padding: 6px; border: 1px solid #dee2e6;">收到ClientHello</td>
                                    <td style="padding: 6px; border: 1px solid #dee2e6;">HANDSHAKE</td>
                                    <td style="padding: 6px; border: 1px solid #dee2e6;">开始握手处理</td>
                                </tr>
                                <tr>
                                    <td style="padding: 6px; border: 1px solid #dee2e6;">HANDSHAKE</td>
                                    <td style="padding: 6px; border: 1px solid #dee2e6;">收到ChangeCipherSpec</td>
                                    <td style="padding: 6px; border: 1px solid #dee2e6;">ESTABLISHED</td>
                                    <td style="padding: 6px; border: 1px solid #dee2e6;">激活加密状态</td>
                                </tr>
                                <tr>
                                    <td style="padding: 6px; border: 1px solid #dee2e6;">ESTABLISHED</td>
                                    <td style="padding: 6px; border: 1px solid #dee2e6;">收到Alert(close)</td>
                                    <td style="padding: 6px; border: 1px solid #dee2e6;">CLOSED</td>
                                    <td style="padding: 6px; border: 1px solid #dee2e6;">关闭连接</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <div class="diagram-container">
                <div class="diagram-title">记录协议数据结构设计</div>

                <div style="background: #e3f2fd; border: 2px solid #2196f3; border-radius: 8px; padding: 20px; margin: 20px 0;">
                    <h4 style="color: #1976d2; margin-bottom: 16px;">📋 TLS记录格式详细设计</h4>

                    <!-- 记录头部结构 -->
                    <div style="background: #ffffff; border: 1px solid #2196f3; border-radius: 6px; padding: 16px; margin-bottom: 16px;">
                        <h5 style="color: #1976d2; margin-bottom: 12px;">记录头部 (5字节)</h5>
                        <div style="display: grid; grid-template-columns: repeat(5, 1fr); gap: 2px; border: 2px solid #1976d2; border-radius: 4px; overflow: hidden;">
                            <div style="background: #ff5722; color: white; padding: 12px; text-align: center; font-size: 0.8em;">
                                <strong>Content Type</strong><br>
                                1 byte<br>
                                <small>协议类型标识</small>
                            </div>
                            <div style="background: #ff9800; color: white; padding: 12px; text-align: center; font-size: 0.8em;">
                                <strong>Version Major</strong><br>
                                1 byte<br>
                                <small>主版本号</small>
                            </div>
                            <div style="background: #ffc107; color: #212529; padding: 12px; text-align: center; font-size: 0.8em;">
                                <strong>Version Minor</strong><br>
                                1 byte<br>
                                <small>次版本号</small>
                            </div>
                            <div style="background: #4caf50; color: white; padding: 12px; text-align: center; font-size: 0.8em;">
                                <strong>Length High</strong><br>
                                1 byte<br>
                                <small>长度高字节</small>
                            </div>
                            <div style="background: #2196f3; color: white; padding: 12px; text-align: center; font-size: 0.8em;">
                                <strong>Length Low</strong><br>
                                1 byte<br>
                                <small>长度低字节</small>
                            </div>
                        </div>
                    </div>

                    <!-- 字段详细说明 -->
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
                        <div style="background: #ffffff; border: 1px solid #2196f3; border-radius: 6px; padding: 16px;">
                            <h5 style="color: #1976d2; margin-bottom: 12px;">Content Type 字段设计</h5>
                            <table style="width: 100%; border-collapse: collapse; font-size: 0.9em;">
                                <thead>
                                    <tr style="background: #e3f2fd;">
                                        <th style="padding: 6px; border: 1px solid #bbdefb; text-align: left;">值</th>
                                        <th style="padding: 6px; border: 1px solid #bbdefb; text-align: left;">协议</th>
                                        <th style="padding: 6px; border: 1px solid #bbdefb; text-align: left;">用途</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td style="padding: 4px; border: 1px solid #bbdefb;"><code>20</code></td>
                                        <td style="padding: 4px; border: 1px solid #bbdefb;">ChangeCipherSpec</td>
                                        <td style="padding: 4px; border: 1px solid #bbdefb;">状态切换</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 4px; border: 1px solid #bbdefb;"><code>21</code></td>
                                        <td style="padding: 4px; border: 1px solid #bbdefb;">Alert</td>
                                        <td style="padding: 4px; border: 1px solid #bbdefb;">错误通知</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 4px; border: 1px solid #bbdefb;"><code>22</code></td>
                                        <td style="padding: 4px; border: 1px solid #bbdefb;">Handshake</td>
                                        <td style="padding: 4px; border: 1px solid #bbdefb;">握手消息</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 4px; border: 1px solid #bbdefb;"><code>23</code></td>
                                        <td style="padding: 4px; border: 1px solid #bbdefb;">ApplicationData</td>
                                        <td style="padding: 4px; border: 1px solid #bbdefb;">应用数据</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div style="background: #ffffff; border: 1px solid #2196f3; border-radius: 6px; padding: 16px;">
                            <h5 style="color: #1976d2; margin-bottom: 12px;">版本和长度字段</h5>
                            <div style="margin-bottom: 12px;">
                                <strong>版本字段 (2字节)</strong>
                                <ul style="margin: 8px 0 0 0; font-size: 0.9em;">
                                    <li>TLS 1.0: 0x0301</li>
                                    <li>TLS 1.1: 0x0302</li>
                                    <li>TLS 1.2: 0x0303</li>
                                    <li>TLS 1.3: 0x0303 (兼容性)</li>
                                </ul>
                            </div>
                            <div>
                                <strong>长度字段 (2字节)</strong>
                                <ul style="margin: 8px 0 0 0; font-size: 0.9em;">
                                    <li>网络字节序 (大端)</li>
                                    <li>最大值: 2^14 = 16384字节</li>
                                    <li>不包括记录头部长度</li>
                                    <li>0长度记录用于心跳</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="diagram-container">
                <div class="diagram-title">记录协议安全参数设计</div>

                <div style="background: #f3e5f5; border: 2px solid #9c27b0; border-radius: 8px; padding: 20px; margin: 20px 0;">
                    <h4 style="color: #7b1fa2; margin-bottom: 16px;">🔐 安全参数结构</h4>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
                        <!-- 连接状态 -->
                        <div style="background: #ffffff; border: 1px solid #9c27b0; border-radius: 6px; padding: 16px;">
                            <h5 style="color: #7b1fa2; margin-bottom: 12px;">连接状态 (Connection State)</h5>
                            <div style="background: #fce4ec; border-radius: 4px; padding: 12px; font-family: monospace; font-size: 0.8em;">
                                <strong>struct {</strong><br>
                                &nbsp;&nbsp;CompressionMethod compression;<br>
                                &nbsp;&nbsp;CipherSpec cipher_spec;<br>
                                &nbsp;&nbsp;opaque MAC_secret[hash_size];<br>
                                &nbsp;&nbsp;opaque key[key_material_length];<br>
                                &nbsp;&nbsp;opaque IV[IV_size];<br>
                                &nbsp;&nbsp;uint64 sequence_number;<br>
                                <strong>} ConnectionState;</strong>
                            </div>
                        </div>

                        <!-- 安全参数 -->
                        <div style="background: #ffffff; border: 1px solid #9c27b0; border-radius: 6px; padding: 16px;">
                            <h5 style="color: #7b1fa2; margin-bottom: 12px;">安全参数 (Security Parameters)</h5>
                            <div style="background: #fce4ec; border-radius: 4px; padding: 12px; font-family: monospace; font-size: 0.8em;">
                                <strong>struct {</strong><br>
                                &nbsp;&nbsp;ConnectionEnd entity;<br>
                                &nbsp;&nbsp;PRFAlgorithm prf_algorithm;<br>
                                &nbsp;&nbsp;BulkCipherAlgorithm bulk_cipher;<br>
                                &nbsp;&nbsp;CipherType cipher_type;<br>
                                &nbsp;&nbsp;uint8 enc_key_length;<br>
                                &nbsp;&nbsp;uint8 block_length;<br>
                                &nbsp;&nbsp;uint8 fixed_iv_length;<br>
                                &nbsp;&nbsp;uint8 record_iv_length;<br>
                                &nbsp;&nbsp;MACAlgorithm mac_algorithm;<br>
                                &nbsp;&nbsp;uint8 mac_length;<br>
                                &nbsp;&nbsp;uint8 mac_key_length;<br>
                                &nbsp;&nbsp;CompressionMethod compression;<br>
                                &nbsp;&nbsp;opaque master_secret[48];<br>
                                &nbsp;&nbsp;opaque client_random[32];<br>
                                &nbsp;&nbsp;opaque server_random[32];<br>
                                <strong>} SecurityParameters;</strong>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="diagram-container">
                <div class="diagram-title">记录协议处理流程设计</div>

                <div style="background: #e8f5e8; border: 2px solid #4caf50; border-radius: 8px; padding: 20px; margin: 20px 0;">
                    <h4 style="color: #2e7d32; margin-bottom: 16px;">⚙️ 数据处理管道 (Processing Pipeline)</h4>

                    <!-- 发送流程 -->
                    <div style="background: #ffffff; border: 1px solid #4caf50; border-radius: 6px; padding: 16px; margin-bottom: 16px;">
                        <h5 style="color: #2e7d32; margin-bottom: 12px;">📤 发送流程 (Send Pipeline)</h5>
                        <div style="display: flex; flex-direction: column; gap: 12px;">
                            <div style="display: flex; align-items: center; gap: 12px;">
                                <div style="background: #4caf50; color: white; padding: 8px; border-radius: 4px; min-width: 40px; text-align: center; font-weight: bold;">1</div>
                                <div style="flex: 1; background: #e8f5e8; padding: 12px; border-radius: 4px;">
                                    <strong>分片 (Fragmentation)</strong><br>
                                    <small>将大于16KB的数据分割成多个记录</small>
                                </div>
                            </div>

                            <div style="display: flex; align-items: center; gap: 12px;">
                                <div style="background: #4caf50; color: white; padding: 8px; border-radius: 4px; min-width: 40px; text-align: center; font-weight: bold;">2</div>
                                <div style="flex: 1; background: #e8f5e8; padding: 12px; border-radius: 4px;">
                                    <strong>压缩 (Compression)</strong><br>
                                    <small>可选步骤，TLS 1.3已移除</small>
                                </div>
                            </div>

                            <div style="display: flex; align-items: center; gap: 12px;">
                                <div style="background: #4caf50; color: white; padding: 8px; border-radius: 4px; min-width: 40px; text-align: center; font-weight: bold;">3</div>
                                <div style="flex: 1; background: #e8f5e8; padding: 12px; border-radius: 4px;">
                                    <strong>MAC计算 (MAC Computation)</strong><br>
                                    <small>HMAC(MAC_write_key, seq_num + TLSCompressed)</small>
                                </div>
                            </div>

                            <div style="display: flex; align-items: center; gap: 12px;">
                                <div style="background: #4caf50; color: white; padding: 8px; border-radius: 4px; min-width: 40px; text-align: center; font-weight: bold;">4</div>
                                <div style="flex: 1; background: #e8f5e8; padding: 12px; border-radius: 4px;">
                                    <strong>加密 (Encryption)</strong><br>
                                    <small>使用协商的对称密钥加密数据和MAC</small>
                                </div>
                            </div>

                            <div style="display: flex; align-items: center; gap: 12px;">
                                <div style="background: #4caf50; color: white; padding: 8px; border-radius: 4px; min-width: 40px; text-align: center; font-weight: bold;">5</div>
                                <div style="flex: 1; background: #e8f5e8; padding: 12px; border-radius: 4px;">
                                    <strong>封装 (Encapsulation)</strong><br>
                                    <small>添加5字节TLS记录头部</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 接收流程 -->
                    <div style="background: #ffffff; border: 1px solid #4caf50; border-radius: 6px; padding: 16px;">
                        <h5 style="color: #2e7d32; margin-bottom: 12px;">📥 接收流程 (Receive Pipeline)</h5>
                        <div style="display: flex; flex-direction: column; gap: 12px;">
                            <div style="display: flex; align-items: center; gap: 12px;">
                                <div style="background: #ff9800; color: white; padding: 8px; border-radius: 4px; min-width: 40px; text-align: center; font-weight: bold;">1</div>
                                <div style="flex: 1; background: #fff3e0; padding: 12px; border-radius: 4px;">
                                    <strong>解析头部 (Header Parsing)</strong><br>
                                    <small>解析5字节记录头部，验证格式</small>
                                </div>
                            </div>

                            <div style="display: flex; align-items: center; gap: 12px;">
                                <div style="background: #ff9800; color: white; padding: 8px; border-radius: 4px; min-width: 40px; text-align: center; font-weight: bold;">2</div>
                                <div style="flex: 1; background: #fff3e0; padding: 12px; border-radius: 4px;">
                                    <strong>解密 (Decryption)</strong><br>
                                    <small>使用当前连接状态的密钥解密载荷</small>
                                </div>
                            </div>

                            <div style="display: flex; align-items: center; gap: 12px;">
                                <div style="background: #ff9800; color: white; padding: 8px; border-radius: 4px; min-width: 40px; text-align: center; font-weight: bold;">3</div>
                                <div style="flex: 1; background: #fff3e0; padding: 12px; border-radius: 4px;">
                                    <strong>MAC验证 (MAC Verification)</strong><br>
                                    <small>验证消息完整性，检测篡改</small>
                                </div>
                            </div>

                            <div style="display: flex; align-items: center; gap: 12px;">
                                <div style="background: #ff9800; color: white; padding: 8px; border-radius: 4px; min-width: 40px; text-align: center; font-weight: bold;">4</div>
                                <div style="flex: 1; background: #fff3e0; padding: 12px; border-radius: 4px;">
                                    <strong>解压缩 (Decompression)</strong><br>
                                    <small>如果启用压缩，则解压缩数据</small>
                                </div>
                            </div>

                            <div style="display: flex; align-items: center; gap: 12px;">
                                <div style="background: #ff9800; color: white; padding: 8px; border-radius: 4px; min-width: 40px; text-align: center; font-weight: bold;">5</div>
                                <div style="flex: 1; background: #fff3e0; padding: 12px; border-radius: 4px;">
                                    <strong>重组 (Reassembly)</strong><br>
                                    <small>重组分片的数据，交付给上层协议</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="diagram-container">
                <div class="diagram-title">记录协议错误处理机制</div>

                <div style="background: #ffebee; border: 2px solid #f44336; border-radius: 8px; padding: 20px; margin: 20px 0;">
                    <h4 style="color: #d32f2f; margin-bottom: 16px;">🚨 错误处理和恢复机制</h4>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
                        <!-- 错误检测 -->
                        <div style="background: #ffffff; border: 1px solid #f44336; border-radius: 6px; padding: 16px;">
                            <h5 style="color: #d32f2f; margin-bottom: 12px;">🔍 错误检测</h5>
                            <ul style="margin: 0; color: #424242; font-size: 0.9em;">
                                <li><strong>格式错误</strong>: 记录头部格式不正确</li>
                                <li><strong>长度错误</strong>: 记录长度超出限制</li>
                                <li><strong>版本错误</strong>: 不支持的协议版本</li>
                                <li><strong>MAC验证失败</strong>: 数据完整性被破坏</li>
                                <li><strong>解密失败</strong>: 密钥或算法错误</li>
                                <li><strong>序列号错误</strong>: 重放攻击检测</li>
                            </ul>
                        </div>

                        <!-- 错误处理 -->
                        <div style="background: #ffffff; border: 1px solid #f44336; border-radius: 6px; padding: 16px;">
                            <h5 style="color: #d32f2f; margin-bottom: 12px;">⚡ 错误处理</h5>
                            <ul style="margin: 0; color: #424242; font-size: 0.9em;">
                                <li><strong>发送Alert</strong>: 通知对方发生错误</li>
                                <li><strong>记录日志</strong>: 记录错误详情用于调试</li>
                                <li><strong>状态回滚</strong>: 恢复到安全状态</li>
                                <li><strong>连接关闭</strong>: 致命错误时关闭连接</li>
                                <li><strong>重试机制</strong>: 某些错误允许重试</li>
                                <li><strong>降级处理</strong>: 协商更安全的参数</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

                <div style="background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 20px; margin: 20px 0;">
                    <h4 style="color: #495057; margin-bottom: 16px;">🔄 上层协议与记录协议的交互</h4>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 16px;">
                        <div style="background: #ffffff; border: 1px solid #dee2e6; border-radius: 6px; padding: 16px;">
                            <h5 style="color: #1976d2; margin-bottom: 12px;">🤝 握手协议 → 记录协议</h5>
                            <ul style="margin: 0; color: #424242; font-size: 0.9em;">
                                <li>发送握手消息到记录协议</li>
                                <li>记录协议添加Content Type: 0x16</li>
                                <li>在握手完成前可能不加密</li>
                                <li>握手完成后切换到加密模式</li>
                            </ul>
                        </div>

                        <div style="background: #ffffff; border: 1px solid #dee2e6; border-radius: 6px; padding: 16px;">
                            <h5 style="color: #f57c00; margin-bottom: 12px;">⚠️ 警报协议 → 记录协议</h5>
                            <ul style="margin: 0; color: #424242; font-size: 0.9em;">
                                <li>发送警报消息到记录协议</li>
                                <li>记录协议添加Content Type: 0x15</li>
                                <li>根据当前状态决定是否加密</li>
                                <li>致命警报后立即关闭连接</li>
                            </ul>
                        </div>

                        <div style="background: #ffffff; border: 1px solid #dee2e6; border-radius: 6px; padding: 16px;">
                            <h5 style="color: #388e3c; margin-bottom: 12px;">🔄 密码规格变更 → 记录协议</h5>
                            <ul style="margin: 0; color: #424242; font-size: 0.9em;">
                                <li>发送变更消息到记录协议</li>
                                <li>记录协议添加Content Type: 0x14</li>
                                <li>触发记录协议状态切换</li>
                                <li>激活新的加密参数</li>
                            </ul>
                        </div>

                        <div style="background: #ffffff; border: 1px solid #dee2e6; border-radius: 6px; padding: 16px;">
                            <h5 style="color: #7b1fa2; margin-bottom: 12px;">📊 应用数据 → 记录协议</h5>
                            <ul style="margin: 0; color: #424242; font-size: 0.9em;">
                                <li>发送应用数据到记录协议</li>
                                <li>记录协议添加Content Type: 0x17</li>
                                <li>必须在加密状态下传输</li>
                                <li>提供完全透明的安全传输</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="diagram-container">
                    <div class="diagram-title">TLS记录协议数据处理流程</div>

                    <div class="flow-diagram">
                        <div class="flow-step">
                            <div class="step-number">1</div>
                            应用数据
                        </div>
                        <div class="arrow">→</div>
                        <div class="flow-step">
                            <div class="step-number">2</div>
                            分片
                        </div>
                        <div class="arrow">→</div>
                        <div class="flow-step">
                            <div class="step-number">3</div>
                            压缩
                        </div>
                        <div class="arrow">→</div>
                        <div class="flow-step">
                            <div class="step-number">4</div>
                            MAC计算
                        </div>
                        <div class="arrow">→</div>
                        <div class="flow-step">
                            <div class="step-number">5</div>
                            加密
                        </div>
                        <div class="arrow">→</div>
                        <div class="flow-step">
                            <div class="step-number">6</div>
                            添加头部
                        </div>
                        <div class="arrow">→</div>
                        <div class="flow-step">
                            <div class="step-number">7</div>
                            TCP传输
                        </div>
                    </div>

                    <div style="margin-top: 20px; padding: 16px; background: #f8f9fa; border-radius: 6px;">
                        <h5 style="margin-bottom: 12px; color: #495057;">处理步骤说明</h5>
                        <ul style="margin: 0; color: #6c757d; font-size: 0.9em;">
                            <li><strong>分片</strong>: 将数据分割为最大16KB的片段</li>
                            <li><strong>压缩</strong>: 可选步骤，TLS 1.3已移除</li>
                            <li><strong>MAC计算</strong>: 使用HMAC或AEAD模式验证完整性</li>
                            <li><strong>加密</strong>: 使用AES-GCM、ChaCha20-Poly1305等算法</li>
                            <li><strong>添加头部</strong>: 5字节TLS记录头(类型+版本+长度)</li>
                        </ul>
                    </div>
                </div>

                <table>
                    <thead>
                        <tr>
                            <th>处理步骤</th>
                            <th>功能描述</th>
                            <th>技术细节</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>1. 分片 (Fragmentation)</strong></td>
                            <td>将大数据块分割为小片段</td>
                            <td>最大片段长度: 2^14 字节 (16KB)</td>
                        </tr>
                        <tr>
                            <td><strong>2. 压缩 (Compression)</strong></td>
                            <td>减少数据大小</td>
                            <td>TLS 1.3已移除，TLS 1.2可选</td>
                        </tr>
                        <tr>
                            <td><strong>3. MAC计算</strong></td>
                            <td>计算消息认证码</td>
                            <td>HMAC-SHA256/384, 或AEAD模式</td>
                        </tr>
                        <tr>
                            <td><strong>4. 加密 (Encryption)</strong></td>
                            <td>对数据进行加密保护</td>
                            <td>AES-GCM, ChaCha20-Poly1305等</td>
                        </tr>
                        <tr>
                            <td><strong>5. 添加头部</strong></td>
                            <td>添加TLS记录头</td>
                            <td>5字节: 类型(1) + 版本(2) + 长度(2)</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="detailed-breakdown">
                <h3>TLS记录格式</h3>
                <div class="diagram-container">
                    <div class="diagram-title">TLS记录格式结构</div>

                    <!-- TLS记录结构图 -->
                    <div style="border: 2px solid #495057; border-radius: 8px; overflow: hidden; margin: 20px 0;">
                        <!-- 记录头部 -->
                        <div style="display: grid; grid-template-columns: 1fr 2fr 2fr; border-bottom: 2px solid #495057;">
                            <div style="background: #007bff; color: white; padding: 16px; text-align: center; font-weight: 600; border-right: 1px solid #495057;">
                                Content Type<br>
                                <small>(1 byte)</small>
                            </div>
                            <div style="background: #17a2b8; color: white; padding: 16px; text-align: center; font-weight: 600; border-right: 1px solid #495057;">
                                Version<br>
                                <small>(2 bytes)</small>
                            </div>
                            <div style="background: #ffc107; color: #212529; padding: 16px; text-align: center; font-weight: 600;">
                                Length<br>
                                <small>(2 bytes)</small>
                            </div>
                        </div>

                        <!-- 加密载荷 -->
                        <div style="background: #6c757d; color: white; padding: 24px; text-align: center; font-weight: 600;">
                            Encrypted Payload<br>
                            <small>Variable length, max 2^14 bytes (16KB)</small>
                        </div>
                    </div>

                    <!-- Content Type说明 -->
                    <div style="margin-top: 24px;">
                        <h5 style="margin-bottom: 16px; color: #495057;">Content Type 字段值</h5>
                        <table style="width: 100%; border-collapse: collapse; border: 1px solid #dee2e6;">
                            <thead>
                                <tr style="background: #f8f9fa;">
                                    <th style="padding: 12px; border: 1px solid #dee2e6; text-align: left;">十六进制</th>
                                    <th style="padding: 12px; border: 1px solid #dee2e6; text-align: left;">十进制</th>
                                    <th style="padding: 12px; border: 1px solid #dee2e6; text-align: left;">协议类型</th>
                                    <th style="padding: 12px; border: 1px solid #dee2e6; text-align: left;">用途</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td style="padding: 10px; border: 1px solid #dee2e6;"><code>0x14</code></td>
                                    <td style="padding: 10px; border: 1px solid #dee2e6;">20</td>
                                    <td style="padding: 10px; border: 1px solid #dee2e6;">Change Cipher Spec</td>
                                    <td style="padding: 10px; border: 1px solid #dee2e6;">切换加密参数</td>
                                </tr>
                                <tr>
                                    <td style="padding: 10px; border: 1px solid #dee2e6;"><code>0x15</code></td>
                                    <td style="padding: 10px; border: 1px solid #dee2e6;">21</td>
                                    <td style="padding: 10px; border: 1px solid #dee2e6;">Alert</td>
                                    <td style="padding: 10px; border: 1px solid #dee2e6;">警报和错误通知</td>
                                </tr>
                                <tr>
                                    <td style="padding: 10px; border: 1px solid #dee2e6;"><code>0x16</code></td>
                                    <td style="padding: 10px; border: 1px solid #dee2e6;">22</td>
                                    <td style="padding: 10px; border: 1px solid #dee2e6;">Handshake</td>
                                    <td style="padding: 10px; border: 1px solid #dee2e6;">握手协议消息</td>
                                </tr>
                                <tr>
                                    <td style="padding: 10px; border: 1px solid #dee2e6;"><code>0x17</code></td>
                                    <td style="padding: 10px; border: 1px solid #dee2e6;">23</td>
                                    <td style="padding: 10px; border: 1px solid #dee2e6;">Application Data</td>
                                    <td style="padding: 10px; border: 1px solid #dee2e6;">应用程序数据</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <h4>🔍 记录处理状态</h4>
                <div class="state-machine">
                    <p><strong>连接状态</strong>: TLS连接维护两个状态</p>
                    <ul>
                        <li><span class="state-box">当前状态</span> - 正在使用的加密参数</li>
                        <li><span class="state-box">待定状态</span> - 握手协商的新参数</li>
                    </ul>
                    <p>Change Cipher Spec消息触发状态切换</p>
                </div>
            </div>
        </section>

        <section id="handshake-detail">
            <h2>🤝 握手协议详细架构</h2>

            <div class="layer-card">
                <h3>握手协议状态机</h3>
                <p>握手协议通过状态机管理连接建立过程，确保安全参数的正确协商。</p>

                <div class="diagram-container">
                    <div class="diagram-title">TLS握手状态机流程</div>

                    <!-- 客户端状态流程 -->
                    <div style="margin-bottom: 24px;">
                        <h5 style="color: #495057; margin-bottom: 12px;">客户端状态转换</h5>
                        <div class="flow-diagram">
                            <div style="background: #28a745; color: white; padding: 8px 16px; border-radius: 4px; font-size: 0.9em;">START</div>
                            <div class="arrow">→</div>
                            <div style="background: #007bff; color: white; padding: 8px 16px; border-radius: 4px; font-size: 0.9em;">WAIT_SERVER_HELLO</div>
                            <div class="arrow">→</div>
                            <div style="background: #17a2b8; color: white; padding: 8px 16px; border-radius: 4px; font-size: 0.9em;">WAIT_CERT</div>
                            <div class="arrow">→</div>
                            <div style="background: #6f42c1; color: white; padding: 8px 16px; border-radius: 4px; font-size: 0.9em;">WAIT_SERVER_HELLO_DONE</div>
                            <div class="arrow">→</div>
                            <div style="background: #dc3545; color: white; padding: 8px 16px; border-radius: 4px; font-size: 0.9em;">WAIT_FINISHED</div>
                            <div class="arrow">→</div>
                            <div style="background: #28a745; color: white; padding: 8px 16px; border-radius: 4px; font-size: 0.9em;">CONNECTED</div>
                        </div>
                    </div>

                    <!-- 服务器状态流程 -->
                    <div>
                        <h5 style="color: #495057; margin-bottom: 12px;">服务器状态转换</h5>
                        <div class="flow-diagram">
                            <div style="background: #28a745; color: white; padding: 8px 16px; border-radius: 4px; font-size: 0.9em;">START</div>
                            <div class="arrow">→</div>
                            <div style="background: #007bff; color: white; padding: 8px 16px; border-radius: 4px; font-size: 0.9em;">WAIT_CLIENT_HELLO</div>
                            <div class="arrow">→</div>
                            <div style="background: #ffc107; color: #212529; padding: 8px 16px; border-radius: 4px; font-size: 0.9em;">WAIT_CLIENT_KEY_EXCHANGE</div>
                            <div class="arrow">→</div>
                            <div style="background: #dc3545; color: white; padding: 8px 16px; border-radius: 4px; font-size: 0.9em;">WAIT_FINISHED</div>
                            <div class="arrow">→</div>
                            <div style="background: #28a745; color: white; padding: 8px 16px; border-radius: 4px; font-size: 0.9em;">CONNECTED</div>
                        </div>
                    </div>
                </div>

                <table>
                    <thead>
                        <tr>
                            <th>握手消息</th>
                            <th>发送方</th>
                            <th>主要内容</th>
                            <th>目的</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>ClientHello</strong></td>
                            <td>客户端</td>
                            <td>协议版本、随机数、密码套件列表</td>
                            <td>发起握手，提供客户端能力</td>
                        </tr>
                        <tr>
                            <td><strong>ServerHello</strong></td>
                            <td>服务器</td>
                            <td>选择的协议版本、随机数、密码套件</td>
                            <td>响应握手，确定协商参数</td>
                        </tr>
                        <tr>
                            <td><strong>Certificate</strong></td>
                            <td>服务器</td>
                            <td>服务器证书链</td>
                            <td>提供身份证明和公钥</td>
                        </tr>
                        <tr>
                            <td><strong>ServerKeyExchange</strong></td>
                            <td>服务器</td>
                            <td>密钥交换参数</td>
                            <td>提供额外的密钥交换信息</td>
                        </tr>
                        <tr>
                            <td><strong>CertificateRequest</strong></td>
                            <td>服务器</td>
                            <td>客户端证书要求</td>
                            <td>请求客户端身份验证</td>
                        </tr>
                        <tr>
                            <td><strong>ServerHelloDone</strong></td>
                            <td>服务器</td>
                            <td>空消息</td>
                            <td>表示服务器握手消息发送完毕</td>
                        </tr>
                        <tr>
                            <td><strong>ClientKeyExchange</strong></td>
                            <td>客户端</td>
                            <td>预主密钥或密钥交换参数</td>
                            <td>提供密钥材料</td>
                        </tr>
                        <tr>
                            <td><strong>CertificateVerify</strong></td>
                            <td>客户端</td>
                            <td>客户端私钥签名</td>
                            <td>证明客户端拥有私钥</td>
                        </tr>
                        <tr>
                            <td><strong>Finished</strong></td>
                            <td>双方</td>
                            <td>握手消息的哈希值</td>
                            <td>验证握手完整性</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </section>

        <section id="security-architecture">
            <h2>🛡️ 安全架构设计</h2>

            <div class="layer-card">
                <h3>多层安全防护</h3>
                <p>TLS采用多层安全架构，每一层都提供特定的安全保护，形成完整的安全体系。</p>

                <div class="component-grid">
                    <div class="security-layer">
                        <h4>🔐 加密层 (Encryption Layer)</h4>
                        <ul>
                            <li><strong>对称加密</strong>: AES, ChaCha20</li>
                            <li><strong>非对称加密</strong>: RSA, ECDH</li>
                            <li><strong>密钥管理</strong>: 密钥派生、更新</li>
                            <li><strong>加密模式</strong>: GCM, CCM, Poly1305</li>
                        </ul>
                    </div>

                    <div class="security-layer">
                        <h4>🔍 认证层 (Authentication Layer)</h4>
                        <ul>
                            <li><strong>数字证书</strong>: X.509证书验证</li>
                            <li><strong>数字签名</strong>: RSA, ECDSA, EdDSA</li>
                            <li><strong>身份验证</strong>: 服务器/客户端认证</li>
                            <li><strong>证书链</strong>: 信任链验证</li>
                        </ul>
                    </div>

                    <div class="security-layer">
                        <h4>✅ 完整性层 (Integrity Layer)</h4>
                        <ul>
                            <li><strong>消息认证码</strong>: HMAC</li>
                            <li><strong>认证加密</strong>: AEAD模式</li>
                            <li><strong>序列号</strong>: 防重放攻击</li>
                            <li><strong>哈希函数</strong>: SHA-256, SHA-384</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="detailed-breakdown">
                <h3>密钥管理架构</h3>
                <div class="data-flow">
                    <h4>🔑 TLS密钥派生过程详解</h4>
                    <div class="flow-diagram vertical-flow">
                        <div class="flow-step" style="background: linear-gradient(135deg, #e74c3c, #c0392b); min-width: 200px;">
                            <div style="font-size: 1.5em; margin-bottom: 5px;">🎲</div>
                            <strong>预主密钥</strong><br>
                            <small>Pre-Master Secret</small><br>
                            <small>48字节随机数</small>
                        </div>
                        <div class="arrow">⬇️</div>
                        <div style="background: #f8f9fa; border: 2px dashed #6c757d; padding: 15px; border-radius: 10px; text-align: center; min-width: 300px;">
                            <strong>PRF函数 + 随机数</strong><br>
                            <small>ClientHello.random + ServerHello.random</small>
                        </div>
                        <div class="arrow">⬇️</div>
                        <div class="flow-step" style="background: linear-gradient(135deg, #3498db, #2980b9); min-width: 200px;">
                            <div style="font-size: 1.5em; margin-bottom: 5px;">🔐</div>
                            <strong>主密钥</strong><br>
                            <small>Master Secret</small><br>
                            <small>48字节</small>
                        </div>
                        <div class="arrow">⬇️</div>
                        <div style="background: #f8f9fa; border: 2px dashed #6c757d; padding: 15px; border-radius: 10px; text-align: center; min-width: 300px;">
                            <strong>密钥扩展函数</strong><br>
                            <small>PRF(master_secret, "key expansion", random)</small>
                        </div>
                        <div class="arrow">⬇️</div>
                        <div class="flow-step" style="background: linear-gradient(135deg, #f39c12, #e67e22); min-width: 200px;">
                            <div style="font-size: 1.5em; margin-bottom: 5px;">📦</div>
                            <strong>密钥块</strong><br>
                            <small>Key Block</small><br>
                            <small>包含所有会话密钥</small>
                        </div>
                        <div class="arrow">⬇️</div>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px; width: 100%;">
                            <div style="background: linear-gradient(135deg, #27ae60, #229954); color: white; padding: 15px; border-radius: 8px; text-align: center;">
                                <div style="font-size: 1.2em;">🔑</div>
                                <strong>Client MAC Key</strong>
                            </div>
                            <div style="background: linear-gradient(135deg, #8e44ad, #7d3c98); color: white; padding: 15px; border-radius: 8px; text-align: center;">
                                <div style="font-size: 1.2em;">🔑</div>
                                <strong>Server MAC Key</strong>
                            </div>
                            <div style="background: linear-gradient(135deg, #e67e22, #d35400); color: white; padding: 15px; border-radius: 8px; text-align: center;">
                                <div style="font-size: 1.2em;">🔐</div>
                                <strong>Client Write Key</strong>
                            </div>
                            <div style="background: linear-gradient(135deg, #2c3e50, #34495e); color: white; padding: 15px; border-radius: 8px; text-align: center;">
                                <div style="font-size: 1.2em;">🔐</div>
                                <strong>Server Write Key</strong>
                            </div>
                            <div style="background: linear-gradient(135deg, #16a085, #138d75); color: white; padding: 15px; border-radius: 8px; text-align: center;">
                                <div style="font-size: 1.2em;">🎯</div>
                                <strong>Client IV</strong>
                            </div>
                            <div style="background: linear-gradient(135deg, #c0392b, #a93226); color: white; padding: 15px; border-radius: 8px; text-align: center;">
                                <div style="font-size: 1.2em;">🎯</div>
                                <strong>Server IV</strong>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="code-block">
密钥派生函数 (PRF - Pseudo Random Function):

TLS 1.2:
master_secret = PRF(pre_master_secret, "master secret",
                   ClientHello.random + ServerHello.random)[0..47]

key_block = PRF(master_secret, "key expansion",
               ServerHello.random + ClientHello.random)

派生的密钥包括:
- client_write_MAC_key
- server_write_MAC_key
- client_write_key
- server_write_key
- client_write_IV
- server_write_IV
                </div>
            </div>
        </section>

        <section id="tls13-architecture">
            <h2>🚀 TLS 1.3 架构创新</h2>

            <div class="layer-card">
                <h3>TLS 1.3 vs TLS 1.2 架构对比</h3>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div class="detailed-breakdown">
                        <h4>TLS 1.2 架构</h4>
                        <ul>
                            <li>复杂的密码套件协商</li>
                            <li>2-RTT握手过程</li>
                            <li>支持不安全的算法</li>
                            <li>可选的前向保密</li>
                            <li>支持压缩</li>
                            <li>支持重协商</li>
                        </ul>
                    </div>

                    <div class="data-flow">
                        <h4>TLS 1.3 架构</h4>
                        <ul>
                            <li>简化的密码套件设计</li>
                            <li>1-RTT握手过程</li>
                            <li>移除不安全算法</li>
                            <li>强制前向保密</li>
                            <li>移除压缩支持</li>
                            <li>移除重协商</li>
                        </ul>
                    </div>
                </div>

                <h3>TLS 1.3 握手架构</h3>
                <div class="diagram-container">
                    <div class="diagram-title">TLS 1.3 简化握手流程 (1-RTT)</div>

                    <div style="display: grid; grid-template-columns: 1fr auto 1fr auto 1fr; gap: 16px; align-items: center; margin: 20px 0;">
                        <!-- 客户端第一步 -->
                        <div style="background: #007bff; color: white; padding: 16px; border-radius: 6px; text-align: center; font-size: 0.9em;">
                            <strong>ClientHello</strong><br>
                            + Key Share<br>
                            + Supported Groups<br>
                            + Extensions
                        </div>

                        <!-- 箭头1 -->
                        <div style="text-align: center; color: #6c757d;">
                            →<br>
                            <small>Flight 1</small>
                        </div>

                        <!-- 服务器响应 -->
                        <div style="background: #28a745; color: white; padding: 16px; border-radius: 6px; text-align: center; font-size: 0.9em;">
                            <strong>ServerHello</strong><br>
                            + Key Share<br>
                            + Certificate<br>
                            + CertificateVerify<br>
                            + Finished
                        </div>

                        <!-- 箭头2 -->
                        <div style="text-align: center; color: #6c757d;">
                            ←<br>
                            <small>Flight 2</small>
                        </div>

                        <!-- 客户端完成 -->
                        <div style="background: #ffc107; color: #212529; padding: 16px; border-radius: 6px; text-align: center; font-size: 0.9em;">
                            <strong>Finished</strong><br>
                            握手完成<br>
                            开始应用数据传输
                        </div>
                    </div>

                    <div style="background: #d4edda; border: 1px solid #c3e6cb; border-radius: 6px; padding: 12px; margin: 20px 0; text-align: center; color: #155724;">
                        <strong>性能优势：仅需 1-RTT 完成握手，相比TLS 1.2性能提升约50%</strong>
                    </div>

                    <div style="margin-top: 24px;">
                        <h5 style="margin-bottom: 12px; color: #495057;">TLS 1.2 vs TLS 1.3 对比</h5>
                        <table style="width: 100%; border-collapse: collapse; border: 1px solid #dee2e6;">
                            <thead>
                                <tr style="background: #f8f9fa;">
                                    <th style="padding: 10px; border: 1px solid #dee2e6; text-align: left;">特性</th>
                                    <th style="padding: 10px; border: 1px solid #dee2e6; text-align: left;">TLS 1.2</th>
                                    <th style="padding: 10px; border: 1px solid #dee2e6; text-align: left;">TLS 1.3</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td style="padding: 8px; border: 1px solid #dee2e6;">握手轮次</td>
                                    <td style="padding: 8px; border: 1px solid #dee2e6;">2-RTT</td>
                                    <td style="padding: 8px; border: 1px solid #dee2e6;">1-RTT</td>
                                </tr>
                                <tr>
                                    <td style="padding: 8px; border: 1px solid #dee2e6;">前向保密</td>
                                    <td style="padding: 8px; border: 1px solid #dee2e6;">可选</td>
                                    <td style="padding: 8px; border: 1px solid #dee2e6;">强制</td>
                                </tr>
                                <tr>
                                    <td style="padding: 8px; border: 1px solid #dee2e6;">0-RTT支持</td>
                                    <td style="padding: 8px; border: 1px solid #dee2e6;">不支持</td>
                                    <td style="padding: 8px; border: 1px solid #dee2e6;">支持</td>
                                </tr>
                                <tr>
                                    <td style="padding: 8px; border: 1px solid #dee2e6;">密码套件</td>
                                    <td style="padding: 8px; border: 1px solid #dee2e6;">复杂组合</td>
                                    <td style="padding: 8px; border: 1px solid #dee2e6;">简化设计</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <h3>TLS 1.3 密钥调度</h3>
                <div class="code-block">
TLS 1.3 密钥派生架构:

Early Secret = HKDF-Extract(0, PSK)
    ↓
Handshake Secret = HKDF-Extract(Derived-Secret, (EC)DHE)
    ↓
Master Secret = HKDF-Extract(Derived-Secret, 0)

每个阶段派生不同用途的密钥:
- Early Data Keys (0-RTT)
- Handshake Keys (握手保护)
- Application Keys (应用数据)
- Resumption Keys (会话恢复)
                </div>
            </div>
        </section>

        <section id="alert-architecture">
            <h2>⚠️ 警报协议架构</h2>

            <div class="layer-card">
                <h3>警报系统设计</h3>
                <p>警报协议负责错误处理和状态通知，是TLS架构中的重要组成部分。</p>

                <table>
                    <thead>
                        <tr>
                            <th>警报级别</th>
                            <th>描述</th>
                            <th>处理方式</th>
                            <th>常见警报</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>Warning (1)</strong></td>
                            <td>警告级别，连接可继续</td>
                            <td>记录日志，继续处理</td>
                            <td>close_notify, user_canceled</td>
                        </tr>
                        <tr>
                            <td><strong>Fatal (2)</strong></td>
                            <td>致命错误，必须关闭连接</td>
                            <td>立即终止连接</td>
                            <td>handshake_failure, certificate_expired</td>
                        </tr>
                    </tbody>
                </table>

                <div class="component-grid">
                    <div class="security-layer">
                        <h4>🔴 致命错误类型</h4>
                        <ul>
                            <li><strong>unexpected_message</strong>: 意外消息</li>
                            <li><strong>bad_record_mac</strong>: MAC验证失败</li>
                            <li><strong>handshake_failure</strong>: 握手失败</li>
                            <li><strong>certificate_expired</strong>: 证书过期</li>
                            <li><strong>certificate_unknown</strong>: 未知证书</li>
                            <li><strong>illegal_parameter</strong>: 非法参数</li>
                        </ul>
                    </div>

                    <div class="security-layer">
                        <h4>🟡 警告类型</h4>
                        <ul>
                            <li><strong>close_notify</strong>: 正常关闭通知</li>
                            <li><strong>user_canceled</strong>: 用户取消</li>
                            <li><strong>no_renegotiation</strong>: 不支持重协商</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <section id="performance-architecture">
            <h2>⚡ 性能优化架构</h2>

            <div class="layer-card">
                <h3>TLS性能优化机制</h3>
                <p>TLS架构包含多种性能优化机制，以减少延迟和提高吞吐量。</p>

                <div class="component-grid">
                    <div class="data-flow">
                        <h4>🔄 会话恢复 (Session Resumption)</h4>
                        <ul>
                            <li><strong>Session ID</strong>: 服务器存储会话状态</li>
                            <li><strong>Session Ticket</strong>: 客户端存储加密会话状态</li>
                            <li><strong>PSK模式</strong>: TLS 1.3预共享密钥</li>
                            <li><strong>0-RTT</strong>: TLS 1.3零往返时间</li>
                        </ul>

                        <div class="code-block">
会话恢复流程:
Client → Server: ClientHello + Session ID/Ticket
Server → Client: ServerHello + 恢复确认
Client ↔ Server: 直接开始加密通信
                        </div>
                    </div>

                    <div class="security-layer">
                        <h4>🚀 0-RTT 数据传输</h4>
                        <p>TLS 1.3引入的0-RTT机制允许客户端在握手完成前发送应用数据</p>

                        <div class="highlight">
                            <strong>优势</strong>:
                            <ul>
                                <li>减少连接建立延迟</li>
                                <li>提高用户体验</li>
                                <li>适合重复连接场景</li>
                            </ul>

                            <strong>限制</strong>:
                            <ul>
                                <li>可能存在重放攻击风险</li>
                                <li>需要应用层配合</li>
                                <li>仅适用于幂等操作</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="extension-architecture">
            <h2>🔧 扩展机制架构</h2>

            <div class="layer-card">
                <h3>TLS扩展系统</h3>
                <p>TLS通过扩展机制提供额外功能，保持协议的可扩展性和向后兼容性。</p>

                <table>
                    <thead>
                        <tr>
                            <th>扩展名称</th>
                            <th>功能</th>
                            <th>使用场景</th>
                            <th>TLS版本</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>SNI</strong></td>
                            <td>服务器名称指示</td>
                            <td>虚拟主机、CDN</td>
                            <td>TLS 1.0+</td>
                        </tr>
                        <tr>
                            <td><strong>ALPN</strong></td>
                            <td>应用层协议协商</td>
                            <td>HTTP/2, HTTP/3选择</td>
                            <td>TLS 1.2+</td>
                        </tr>
                        <tr>
                            <td><strong>OCSP Stapling</strong></td>
                            <td>证书状态检查</td>
                            <td>证书撤销验证</td>
                            <td>TLS 1.0+</td>
                        </tr>
                        <tr>
                            <td><strong>Supported Groups</strong></td>
                            <td>支持的椭圆曲线</td>
                            <td>密钥交换协商</td>
                            <td>TLS 1.2+</td>
                        </tr>
                        <tr>
                            <td><strong>Key Share</strong></td>
                            <td>密钥共享</td>
                            <td>TLS 1.3快速握手</td>
                            <td>TLS 1.3</td>
                        </tr>
                        <tr>
                            <td><strong>PSK Key Exchange Modes</strong></td>
                            <td>PSK密钥交换模式</td>
                            <td>会话恢复、0-RTT</td>
                            <td>TLS 1.3</td>
                        </tr>
                    </tbody>
                </table>

                <div class="detailed-breakdown">
                    <h3>扩展处理架构</h3>
                    <div class="flow-diagram">
                        <div class="flow-step">客户端扩展</div>
                        <div class="arrow">→</div>
                        <div class="flow-step">服务器处理</div>
                        <div class="arrow">→</div>
                        <div class="flow-step">服务器响应</div>
                        <div class="arrow">→</div>
                        <div class="flow-step">功能激活</div>
                    </div>

                    <div class="code-block">
扩展格式:
struct {
    ExtensionType extension_type;
    opaque extension_data<0..2^16-1>;
} Extension;

处理原则:
1. 未知扩展必须忽略
2. 关键扩展必须理解
3. 扩展顺序不重要
4. 服务器只能响应客户端发送的扩展
                    </div>
                </div>
            </div>
        </section>

        <section id="http-tls-integration">
            <h2>🌐 HTTP与TLS集成架构</h2>

            <div class="diagram-container">
                <div class="diagram-title">HTTP与TLS协议栈集成</div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin: 20px 0;">
                    <div>
                        <h5 style="text-align: center; color: #dc3545; margin-bottom: 16px;">HTTP (不安全)</h5>
                        <div class="protocol-stack">
                            <div class="stack-layer application-layer">
                                <div class="layer-title">HTTP应用层</div>
                                <div class="layer-protocols">GET, POST, PUT, DELETE</div>
                                <div class="layer-description">明文HTTP请求和响应</div>
                            </div>

                            <div class="stack-layer transport-layer">
                                <div class="layer-title">TCP传输层</div>
                                <div class="layer-protocols">端口 80</div>
                                <div class="layer-description">直接TCP连接，无加密</div>
                            </div>
                        </div>
                    </div>

                    <div>
                        <h5 style="text-align: center; color: #28a745; margin-bottom: 16px;">HTTPS (安全)</h5>
                        <div class="protocol-stack">
                            <div class="stack-layer application-layer">
                                <div class="layer-title">HTTP应用层</div>
                                <div class="layer-protocols">GET, POST, PUT, DELETE</div>
                                <div class="layer-description">HTTP请求和响应</div>
                            </div>

                            <div class="stack-layer tls-layer">
                                <div class="layer-title">TLS安全层</div>
                                <div class="layer-protocols">TLS 1.2/1.3</div>
                                <div class="layer-description">加密、认证、完整性保护</div>
                            </div>

                            <div class="stack-layer transport-layer">
                                <div class="layer-title">TCP传输层</div>
                                <div class="layer-protocols">端口 443</div>
                                <div class="layer-description">加密的TCP连接</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="diagram-container">
                <div class="diagram-title">HTTPS连接建立时序图</div>

                <div style="display: grid; grid-template-columns: 1fr auto 1fr; gap: 20px; align-items: start; margin: 20px 0;">
                    <div style="text-align: center;">
                        <div style="background: #007bff; color: white; padding: 12px; border-radius: 6px; margin-bottom: 20px;">
                            <strong>客户端 (浏览器)</strong>
                        </div>
                    </div>

                    <div style="width: 2px; background: #dee2e6; height: 400px; position: relative;">
                        <!-- 时序线 -->
                    </div>

                    <div style="text-align: center;">
                        <div style="background: #28a745; color: white; padding: 12px; border-radius: 6px; margin-bottom: 20px;">
                            <strong>服务器</strong>
                        </div>
                    </div>
                </div>

                <div style="margin: 20px 0;">
                    <div style="display: flex; align-items: center; margin: 10px 0;">
                        <div style="background: #007bff; color: white; padding: 6px 12px; border-radius: 4px; margin-right: 10px; font-size: 0.8em;">1</div>
                        <span>TCP SYN (端口443)</span>
                        <div style="flex: 1; border-bottom: 1px dashed #6c757d; margin: 0 10px;"></div>
                        <span>→</span>
                    </div>

                    <div style="display: flex; align-items: center; margin: 10px 0;">
                        <div style="background: #28a745; color: white; padding: 6px 12px; border-radius: 4px; margin-right: 10px; font-size: 0.8em;">2</div>
                        <span>←</span>
                        <div style="flex: 1; border-bottom: 1px dashed #6c757d; margin: 0 10px;"></div>
                        <span>TCP SYN-ACK</span>
                    </div>

                    <div style="display: flex; align-items: center; margin: 10px 0;">
                        <div style="background: #007bff; color: white; padding: 6px 12px; border-radius: 4px; margin-right: 10px; font-size: 0.8em;">3</div>
                        <span>TCP ACK</span>
                        <div style="flex: 1; border-bottom: 1px dashed #6c757d; margin: 0 10px;"></div>
                        <span>→</span>
                    </div>

                    <div style="display: flex; align-items: center; margin: 10px 0;">
                        <div style="background: #ffc107; color: #212529; padding: 6px 12px; border-radius: 4px; margin-right: 10px; font-size: 0.8em;">4</div>
                        <span>TLS ClientHello</span>
                        <div style="flex: 1; border-bottom: 1px dashed #6c757d; margin: 0 10px;"></div>
                        <span>→</span>
                    </div>

                    <div style="display: flex; align-items: center; margin: 10px 0;">
                        <div style="background: #ffc107; color: #212529; padding: 6px 12px; border-radius: 4px; margin-right: 10px; font-size: 0.8em;">5</div>
                        <span>←</span>
                        <div style="flex: 1; border-bottom: 1px dashed #6c757d; margin: 0 10px;"></div>
                        <span>TLS ServerHello + Certificate</span>
                    </div>

                    <div style="display: flex; align-items: center; margin: 10px 0;">
                        <div style="background: #ffc107; color: #212529; padding: 6px 12px; border-radius: 4px; margin-right: 10px; font-size: 0.8em;">6</div>
                        <span>TLS Key Exchange + Finished</span>
                        <div style="flex: 1; border-bottom: 1px dashed #6c757d; margin: 0 10px;"></div>
                        <span>→</span>
                    </div>

                    <div style="display: flex; align-items: center; margin: 10px 0;">
                        <div style="background: #17a2b8; color: white; padding: 6px 12px; border-radius: 4px; margin-right: 10px; font-size: 0.8em;">7</div>
                        <span>HTTP Request (加密)</span>
                        <div style="flex: 1; border-bottom: 1px dashed #6c757d; margin: 0 10px;"></div>
                        <span>→</span>
                    </div>

                    <div style="display: flex; align-items: center; margin: 10px 0;">
                        <div style="background: #17a2b8; color: white; padding: 6px 12px; border-radius: 4px; margin-right: 10px; font-size: 0.8em;">8</div>
                        <span>←</span>
                        <div style="flex: 1; border-bottom: 1px dashed #6c757d; margin: 0 10px;"></div>
                        <span>HTTP Response (加密)</span>
                    </div>
                </div>
            </div>

            <div class="diagram-container">
                <div class="diagram-title">HTTP消息在TLS中的封装</div>

                <div style="margin: 20px 0;">
                    <h5 style="margin-bottom: 16px; color: #495057;">HTTP请求的TLS封装过程</h5>

                    <div class="flow-diagram vertical-flow">
                        <div class="process-step" style="background: #e3f2fd; border: 2px solid #2196f3;">
                            <strong>原始HTTP请求</strong><br>
                            <code style="font-size: 0.8em;">GET /api/users HTTP/1.1<br>Host: example.com</code>
                        </div>

                        <div class="arrow">⬇</div>

                        <div class="process-step" style="background: #fff3e0; border: 2px solid #ff9800;">
                            <strong>TLS记录封装</strong><br>
                            <small>添加TLS记录头部 + 加密HTTP数据</small>
                        </div>

                        <div class="arrow">⬇</div>

                        <div class="process-step" style="background: #e8f5e8; border: 2px solid #4caf50;">
                            <strong>TCP段传输</strong><br>
                            <small>通过TCP连接发送加密数据</small>
                        </div>
                    </div>
                </div>

                <div style="background: #f8f9fa; border-radius: 6px; padding: 16px; margin: 20px 0;">
                    <h5 style="margin-bottom: 12px; color: #495057;">TLS记录格式中的HTTP数据</h5>
                    <div style="font-family: monospace; font-size: 0.9em; background: #ffffff; border: 1px solid #dee2e6; border-radius: 4px; padding: 12px;">
                        <div style="color: #007bff;">TLS Record Header (5 bytes):</div>
                        <div style="margin-left: 20px;">Content Type: 0x17 (Application Data)</div>
                        <div style="margin-left: 20px;">Version: 0x0303 (TLS 1.2)</div>
                        <div style="margin-left: 20px;">Length: HTTP数据长度</div>
                        <br>
                        <div style="color: #28a745;">Encrypted HTTP Data:</div>
                        <div style="margin-left: 20px; color: #6c757d;">[加密的HTTP请求/响应数据]</div>
                    </div>
                </div>
            </div>
        </section>

        <section id="implementation-architecture">
            <h2>💻 实现架构考虑</h2>

            <div class="layer-card">
                <h3>TLS实现架构模式</h3>

                <div class="component-grid">
                    <div class="security-layer">
                        <h4>🏗️ 分层实现</h4>
                        <ul>
                            <li><strong>API层</strong>: 应用程序接口</li>
                            <li><strong>协议层</strong>: TLS协议逻辑</li>
                            <li><strong>密码层</strong>: 加密算法实现</li>
                            <li><strong>传输层</strong>: 网络I/O处理</li>
                        </ul>
                    </div>

                    <div class="security-layer">
                        <h4>🔄 状态管理</h4>
                        <ul>
                            <li><strong>连接状态</strong>: 每个连接的状态信息</li>
                            <li><strong>会话状态</strong>: 可复用的会话信息</li>
                            <li><strong>密钥状态</strong>: 加密密钥和参数</li>
                            <li><strong>缓冲管理</strong>: 数据缓冲和处理</li>
                        </ul>
                    </div>

                    <div class="security-layer">
                        <h4>🛡️ 安全实现</h4>
                        <ul>
                            <li><strong>密钥保护</strong>: 内存中密钥安全</li>
                            <li><strong>随机数生成</strong>: 安全随机数源</li>
                            <li><strong>时序攻击防护</strong>: 常数时间算法</li>
                            <li><strong>侧信道防护</strong>: 防止信息泄露</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 15px; margin: 40px 0; text-align: center;">
            <h2 style="color: white; border: none; margin-bottom: 20px;">🎯 TLS架构总结</h2>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 30px 0;">
                <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px;">
                    <h4>🏗️ 模块化设计</h4>
                    <p>分层架构，职责清晰，易于维护和扩展</p>
                </div>

                <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px;">
                    <h4>🛡️ 多重安全</h4>
                    <p>加密、认证、完整性多层防护</p>
                </div>

                <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px;">
                    <h4>⚡ 性能优化</h4>
                    <p>会话恢复、0-RTT等机制提升性能</p>
                </div>

                <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px;">
                    <h4>🔧 可扩展性</h4>
                    <p>扩展机制支持新功能和协议演进</p>
                </div>
            </div>

            <div style="margin-top: 30px; padding: 20px; background: rgba(255,255,255,0.1); border-radius: 10px;">
                <h3>🔍 架构核心特点</h3>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; text-align: left; margin-top: 15px;">
                    <div>
                        <h4>设计原则</h4>
                        <ul>
                            <li>安全第一</li>
                            <li>向后兼容</li>
                            <li>性能优化</li>
                            <li>可扩展性</li>
                        </ul>
                    </div>
                    <div>
                        <h4>架构优势</h4>
                        <ul>
                            <li>模块化组件</li>
                            <li>清晰的接口</li>
                            <li>灵活的配置</li>
                            <li>强大的安全性</li>
                        </ul>
                    </div>
                </div>
            </div>

            <p style="margin-top: 30px; font-style: italic; font-size: 1.1em;">
                "TLS架构的精妙之处在于其平衡了安全性、性能和可扩展性，<br>
                为现代网络通信提供了坚实的安全基础。"
            </p>
        </div>

        <footer style="text-align: center; padding: 20px; color: #666; border-top: 2px solid #eee; margin-top: 40px;">
            <p>📐 本文档详细解析了TLS协议的完整架构设计</p>
            <p>🔬 从协议栈到组件实现，从安全机制到性能优化</p>
            <p style="margin-top: 20px; font-size: 0.9em;">
                © 2024 TLS详细架构解析 | 深入理解，精通应用 🚀
            </p>
        </footer>
    </div>
</body>
</html>
