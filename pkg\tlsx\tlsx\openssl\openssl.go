//
// Author: chenjb
// Version: V1.0
// Date: 2025-06-23 19:55:14
// FilePath: /yaml_scan/pkg/tlsx/tlsx/openssl/openssl.go
// Description: OpenSSL命令行工具的TLS客户端实现

// Package openssl 提供基于OpenSSL命令行工具的TLS客户端实现
// 通过调用系统中的OpenSSL二进制文件执行TLS连接和证书分析
// 支持最广泛的TLS协议版本和密码套件，提供最佳兼容性
package openssl

import (
	"context"                           // 上下文控制
	"crypto/x509"                       // X.509证书处理
	"net"                               // 网络操作
	"time"                              // 时间处理
	"yaml_scan/pkg/fastdialer"          // 快速拨号器
	"yaml_scan/pkg/gologger"            // 日志记录器
	"yaml_scan/pkg/tlsx/tlsx/clients"   // TLS客户端接口
	"yaml_scan/pkg/tlsx/output/stats"   // 统计信息收集
	stringsutil "yaml_scan/utils/strings" // 字符串工具
	errorutils "yaml_scan/utils/errors"  // 错误处理工具
	iputil "yaml_scan/utils/ip"          // IP地址工具
)

// Client 是基于OpenSSL命令行工具的TLS客户端实现
// 通过调用外部OpenSSL进程执行TLS连接和证书分析
type Client struct {
	dialer  *fastdialer.Dialer  // 用于网络连接的快速拨号器
	options *clients.Options    // TLS连接配置选项
}

// New 创建一个新的基于OpenSSL的TLS客户端
// 检查OpenSSL是否可用，并初始化客户端结构
//
// 参数:
//   - options: TLS连接配置选项，包含超时、重试等参数
//
// 返回值:
//   - *Client: OpenSSL TLS客户端实例
//   - error: 如果OpenSSL不可用则返回错误
//
// 注意:
//   - 如果系统中未安装OpenSSL或无法找到可执行文件，将返回ErrNotAvailable错误
//   - 客户端使用传入的fastdialer进行网络连接
func New(options *clients.Options) (*Client, error) {
	// 检查OpenSSL是否可用
	if !IsAvailable() {
		return nil, ErrNotAvailable
	}

	// 创建并初始化客户端实例
	c := &Client{
		dialer:  options.Fastdialer, // 使用传入的快速拨号器
		options: options,            // 保存配置选项
	}
	return c, nil
}

// ConnectWithOptions 使用指定选项连接到目标主机并获取TLS响应数据
// 通过调用OpenSSL命令行工具执行TLS握手和证书分析
//
// 参数:
//   - hostname: 目标主机名或域名
//   - ip: 目标IP地址
//   - port: 目标端口号
//   - options: 连接选项，包含SNI、TLS版本等配置
//
// 返回值:
//   - *clients.Response: TLS连接响应，包含证书信息和连接详情
//   - error: 连接过程中的错误，成功时为nil
//
// 连接流程:
//   - 生成OpenSSL命令行参数
//   - 验证网络连接可达性
//   - 调用OpenSSL执行TLS握手
//   - 解析OpenSSL输出并构建响应结构
func (c *Client) ConnectWithOptions(hostname, ip, port string, options clients.ConnectOptions) (*clients.Response, error) {
	// 生成OpenSSL命令行选项和参数
	// 该过程包括以下步骤：
	// 1. 确定TLS协议版本（优先级：options.VersionTLS > c.options.MinVersion > c.options.MaxVersion > 默认tls12）
	// 2. 将协议版本字符串转换为OpenSSL命令行参数格式（如"tls12" -> "-tls1_2"）
	// 3. 构建连接地址（优先使用IP地址，否则使用hostname）
	// 4. 设置SNI服务器名称（Server Name Indication）
	// 5. 验证并转换密码套件列表为OpenSSL支持的格式
	// 6. 配置CA证书文件路径（如果指定）
	// 7. 验证必要参数的完整性（hostname/ip和port不能为空）
	// 最终生成的Options结构将用于构建完整的OpenSSL s_client命令参数
	opensslOpts, errx := c.getOpenSSLopts(hostname, ip, port, options)
	if errx != nil {
		return nil, errx.Msgf("failed to generate openssl options")
	}

	// 确保超时时间不为零（Windows系统建议至少3秒）
	// 此超时时间将用于os.exec的上下文控制
	if c.options.Timeout < 3 {
		c.options.Timeout = 3
	}

	// 验证拨号器是否可用，如果没有则创建默认拨号器
	if c.dialer == nil {
		var err error
		c.dialer, err = fastdialer.NewDialer(fastdialer.DefaultOptions)
		if err != nil {
			return nil, errorutils.NewWithErr(err).WithTag(PkgTag, "fastdialer").Msgf("failed to create new fastdialer")
		}
	}

	// 预先测试网络连接以确保目标可达
	// 注意：拨号器使用的IP可能与OpenSSL使用的IP不同
	// 这里主要用于避免不一致性和提前发现网络问题
	rawConn, err := c.dialer.Dial(context.TODO(), "tcp", opensslOpts.Address)
	if err != nil || rawConn == nil {
		return nil, errorutils.NewWithErr(err).WithTag(PkgTag, "fastdialer").Msgf("could not dial address:%v", opensslOpts.Address)
	}
	defer rawConn.Close() // 确保连接在函数结束时关闭

	// 获取实际解析到的IP地址
	resolvedIP, _, err := net.SplitHostPort(rawConn.RemoteAddr().String())
	if err != nil {
		return nil, err
	}

	// 创建带超时的上下文，用于控制OpenSSL命令执行时间
	ctx, cancel := context.WithTimeout(context.TODO(), time.Duration(c.options.Timeout)*time.Second)
	defer cancel()

	// 调用OpenSSL执行TLS握手并获取响应
	// 注意：这里忽略的错误包含握手错误和OpenSSL返回的其他错误
	resp, errx := getResponse(ctx, opensslOpts)
	if errx != nil {
		return nil, errx.Msgf("failed to response from openssl").WithTag(PkgTag)
	}

	// 记录响应时间戳
	now := time.Now()

	// 构建TLS连接响应结构
	response := &clients.Response{
		Timestamp:           &now,                                                                          // 响应生成时间戳
		Host:                hostname,                                                                      // 目标主机名
		IP:                  resolvedIP,                                                                   // 实际连接的IP地址
		ProbeStatus:         true,                                                                         // 探测状态：成功
		Port:                port,                                                                         // 目标端口号
		Version:             resp.Session.getTLSVersion(),                                                 // 协商的TLS版本
		CertificateResponse: clients.Convertx509toResponse(c.options, hostname, resp.AllCerts[0], c.options.Cert), // 叶证书信息
		Cipher:              resp.Session.Cipher,                                                          // 使用的密码套件
		TLSConnection:       "openssl",                                                                    // 标识使用OpenSSL实现
		ServerName:          opensslOpts.ServerName,                                                       // 使用的SNI
		ClientCertRequired:  &resp.ClientCertRequired,                                                     // 是否需要客户端证书
	}

	// 获取完整的证书链用于信任状态检查
	certs := getCertChain(ctx, opensslOpts)
	response.Untrusted = clients.IsUntrustedCA(certs) // 检查是否存在不受信任的CA

	// 注意：当请求证书链时，OpenSSL s_client不会返回服务器证书
	// 因此需要单独获取证书链信息
	if c.options.TLSChain {
		responses := []*clients.CertificateResponse{} // 初始化证书链响应列表

		// 遍历证书链中的每个证书并转换为响应格式
		for _, v := range certs {
			responses = append(responses, clients.Convertx509toResponse(c.options, hostname, v, c.options.Cert))
		}
		response.Chain = responses // 设置证书链信息
	}
	return response, nil
}

// EnumerateCiphers 枚举目标服务器支持的所有密码套件
// 通过逐个尝试不同的密码套件与目标服务器建立TLS连接来确定支持的密码套件
//
// 参数:
//   - hostname: 目标主机名或域名
//   - ip: 目标IP地址
//   - port: 目标端口号
//   - options: 连接选项，包含TLS版本、安全级别等配置
//
// 返回值:
//   - []string: 目标服务器支持的密码套件名称列表
//   - error: 枚举过程中的错误，成功时为nil
//
// 枚举流程:
//   - 根据指定的安全级别筛选要测试的密码套件
//   - 为每个密码套件单独建立TLS连接
//   - 记录握手成功的密码套件
//   - 跳过证书解析以提高枚举效率
func (c *Client) EnumerateCiphers(hostname, ip, port string, options clients.ConnectOptions) ([]string, error) {
	// 根据给定的安全级别筛选密码套件
	// 支持的级别包括：All（全部）、Secure（安全）、Weak（弱）、Insecure（不安全）
	toEnumerate := clients.GetCiphersWithLevel(AllCiphersNames, options.CipherLevel...)

	enumeratedCiphers := []string{} // 存储枚举成功的密码套件

	// 生成OpenSSL命令行选项
	// 复用ConnectWithOptions中的选项生成逻辑
	opensslOpts, err := c.getOpenSSLopts(hostname, ip, port, options)
	if err != nil {
		return nil, err.Msgf("failed to generate openssl options")
	}
	opensslOpts.SkipCertParse = true // 跳过证书解析以提高枚举效率
	gologger.Debug().Label(PkgTag).Msgf("Starting cipher enumeration with %v ciphers in %v", len(toEnumerate), options.VersionTLS)

	// 逐个测试每个密码套件
	for _, v := range toEnumerate {
		opensslOpts.Cipher = []string{v}        // 设置当前要测试的密码套件
		stats.IncrementOpensslTLSConnections()  // 增加连接统计计数

		// 为每次连接创建带超时的上下文
		ctx, cancel := context.WithTimeout(context.TODO(), time.Duration(c.options.Timeout)*time.Second)
		defer cancel()

		// 尝试使用当前密码套件建立TLS连接
		if resp, errx := getResponse(ctx, opensslOpts); errx == nil && resp.Session.Cipher != "0000" {
			// "0000"表示握手失败，只有非"0000"的响应才表示密码套件被支持
			enumeratedCiphers = append(enumeratedCiphers, resp.Session.Cipher)
		}
	}
	return enumeratedCiphers, nil
}

// SupportedTLSVersions 返回OpenSSL客户端支持的TLS协议版本列表
// 该方法实现了clients.Implementation接口的要求
//
// 返回值:
//   - []string: OpenSSL支持的TLS版本字符串列表（如["tls10", "tls11", "tls12"]）
//   - error: 始终为nil，因为版本列表是静态预定义的
//
// 注意:
//   - 返回的版本列表在编译时确定，不会动态检测系统OpenSSL的实际支持情况
//   - 实际可用的版本可能因系统OpenSSL版本和配置而异
func (c *Client) SupportedTLSVersions() ([]string, error) {
	return SupportedTLSVersions, nil
}

// SupportedTLSCiphers 返回OpenSSL客户端支持的密码套件列表
// 该方法实现了clients.Implementation接口的要求
//
// 返回值:
//   - []string: OpenSSL支持的密码套件名称列表
//   - error: 始终为nil，因为密码套件列表在初始化时已确定
//
// 注意:
//   - 返回的密码套件列表在包初始化时通过调用"openssl ciphers"命令动态获取
//   - 列表内容取决于系统中安装的OpenSSL版本和编译配置
//   - 如果OpenSSL不可用，列表将为空
func (c *Client) SupportedTLSCiphers() ([]string, error) {
	return AllCiphersNames, nil
}

// getOpenSSLopts 根据连接参数生成OpenSSL命令行选项结构
// 该方法是OpenSSL客户端的核心配置生成器，负责将高级连接选项转换为OpenSSL可理解的参数
//
// 参数:
//   - hostname: 目标主机名或域名，用于SNI和连接地址
//   - ip: 目标IP地址，如果提供则优先使用
//   - port: 目标端口号
//   - options: 连接选项，包含TLS版本、密码套件、SNI等配置
//
// 返回值:
//   - *Options: 生成的OpenSSL选项结构，包含所有必要的命令行参数
//   - errorutils.Error: 配置生成过程中的错误
//
// 配置生成逻辑:
//   - TLS版本选择：按优先级选择协议版本
//   - 地址构建：智能选择IP或hostname作为连接目标
//   - 密码套件验证：确保指定的密码套件被OpenSSL支持
//   - SNI配置：处理服务器名称指示设置
//   - 枚举模式适配：根据不同的枚举模式调整参数
func (c *Client) getOpenSSLopts(hostname, ip, port string, options clients.ConnectOptions) (*Options, errorutils.Error) {
	// 确定要使用的TLS协议版本
	// 优先级：连接选项中的版本 > 客户端最小版本 > 客户端最大版本 > 默认TLS 1.2
	var protocolVersion string
	switch {
	case options.VersionTLS != "":
		protocolVersion = options.VersionTLS    // 使用连接选项中指定的版本
	case c.options.MinVersion != "":
		protocolVersion = c.options.MinVersion  // 使用客户端配置的最小版本
	case c.options.MaxVersion != "":
		protocolVersion = c.options.MaxVersion  // 使用客户端配置的最大版本
	default:
		protocolVersion = "tls12"               // 默认使用TLS 1.2
	}

	// 将协议版本字符串转换为OpenSSL内部协议枚举
	protocol, err := getProtocol(protocolVersion)
	if err != nil {
		return nil, errorutils.NewWithTag("openssl", "%s", err.Error())
	}

	// 创建基础OpenSSL选项结构
	// 注意：空值的CLI选项在生成命令时会被忽略
	opensslOptions := &Options{
		ServerName: options.SNI,                // 设置SNI（服务器名称指示）
		Protocol:   protocol,                   // 设置TLS协议版本
		CAFile:     c.options.CACertificate,    // 设置CA证书文件路径
	}

	// 智能选择连接地址：优先使用IP地址，否则使用hostname
	// 在以下情况下使用IP地址：
	// 1. 提供了有效的IP地址
	// 2. 启用了扫描所有IP选项
	// 3. 指定了IP版本限制
	if (ip != "" && iputil.IsIP(ip)) || c.options.ScanAllIPs || len(c.options.IPVersion) > 0 {
		opensslOptions.Address = net.JoinHostPort(ip, port)
	} else {
		opensslOptions.Address = net.JoinHostPort(hostname, port)
	}

	// 验证必要参数的完整性
	if (hostname == "" && ip == "") || port == "" {
		return nil, errorutils.NewWithTag("openssl", "client requires valid address got port=%v,hostname=%v,ip=%v", port, hostname, ip)
	}

	// 处理版本枚举模式的特殊情况
	// 在版本枚举模式下，如果指定的TLS版本不被支持，直接返回错误
	if options.EnumMode == clients.Version && (options.VersionTLS == "" || !stringsutil.EqualFoldAny(options.VersionTLS, SupportedTLSVersions...)) {
		return nil, errorutils.NewWithTag("openssl", "tlsversion `%v` not supported in openssl", options.VersionTLS)
	}

	// 处理密码套件配置（非密码套件枚举模式）
	if options.EnumMode != clients.Cipher {
		// 验证并转换用户指定的密码套件为OpenSSL支持的格式
		ciphers, err := toOpenSSLCiphers(options.Ciphers...)
		if err != nil {
			return nil, errorutils.NewWithErr(err).WithTag("openssl")
		}
		opensslOptions.Cipher = ciphers

		// 如果没有指定SNI，使用hostname作为默认SNI
		// 这对于多虚拟主机的服务器是必要的（如projectdiscovery.io）
		if opensslOptions.ServerName == "" {
			opensslOptions.ServerName = hostname
		}
	} else {
			// 密码套件枚举模式：验证TLS版本是否支持密码套件枚举
		if !stringsutil.EqualFoldAny(options.VersionTLS, SupportedTLSVersions...) {
			return nil, errorutils.NewWithTag(PkgTag, "cipher enum with version %v not implemented", options.VersionTLS)
		}
	}
	return opensslOptions, nil
}

// getCertChain 获取完整的TLS证书链
// 由于OpenSSL s_client的特殊行为，需要单独调用来获取证书链信息
//
// 参数:
//   - ctx: 上下文，用于控制命令执行超时
//   - opts: OpenSSL选项，将被修改以启用证书链输出
//
// 返回值:
//   - []*x509.Certificate: 解析后的证书链，按从叶证书到根证书的顺序排列
//
// OpenSSL行为说明:
//   - 默认情况下，OpenSSL s_client不会输出完整的证书链
//   - 当使用-showcerts参数时，会输出证书链但不包含服务器证书本身
//   - 因此需要单独调用来获取证书链，然后与服务器证书合并
//
// 注意:
//   - 该函数会修改传入的opts参数，设置CertChain=true
//   - 如果OpenSSL执行失败或证书解析失败，返回空切片
//   - 证书链的顺序和完整性取决于服务器的配置
func getCertChain(ctx context.Context, opts *Options) []*x509.Certificate {
	responses := []*x509.Certificate{} // 初始化空的证书列表

	// 启用证书链输出选项
	// 这会在OpenSSL命令中添加-showcerts参数
	opts.CertChain = true

	// 根据修改后的选项生成OpenSSL命令参数
	args, _ := opts.Args()

	// 执行OpenSSL命令获取证书链
	result, er := execOpenSSL(ctx, args)
	if er != nil {
		// OpenSSL执行失败，返回空列表
		return responses
	}

	// 解析OpenSSL输出中的证书数据
	// parseCertificates函数会从PEM格式的输出中提取所有证书
	certs, err := parseCertificates(result.Stdout)
	if err != nil {
		// 证书解析失败，返回空列表
		return responses
	}

	return certs
}