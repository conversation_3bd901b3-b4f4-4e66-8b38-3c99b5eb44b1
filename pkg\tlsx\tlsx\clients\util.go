// Author: chenjb
// Version: V1.0
// Date: 2025-06-24 11:45:05
// FilePath: /yaml_scan/pkg/tlsx/tlsx/clients/util.go
// Description: TLS客户端工具函数集合，提供各种辅助功能

// Package clients 提供TLS客户端的工具函数集合
// 包含证书转换、网络连接、数据处理等各种辅助功能
package clients

import (
	"context"                           // 上下文控制
	"crypto/x509"                       // X.509证书处理
	"encoding/hex"                      // 十六进制编码
	"errors"                            // 错误处理
	"math/big"                          // 大整数运算
	"net"                               // 网络操作
	"strings"                           // 字符串操作
	"time"                              // 时间处理
	errorutil "yaml_scan/utils/errors"  // 自定义错误工具
	iputil "yaml_scan/utils/ip"         // IP地址工具
	mapsutil "yaml_scan/utils/maps"     // 映射操作工具
)

// Convertx509toResponse 将标准X.509证书转换为tlsx的证书响应结构
// 执行完整的证书解析和状态检查，生成包含所有相关信息的响应对象
//
// 参数:
//   - options: TLS连接配置选项，包含撤销检查等设置
//   - hostname: 目标主机名，用于证书域名匹配验证
//   - cert: 要转换的X.509证书对象
//   - showcert: 是否在响应中包含完整的PEM格式证书内容
//
// 返回值:
//   - *CertificateResponse: 包含证书详细信息和状态检查结果的响应结构
//
// 功能说明:
//   - 提取证书的基本信息（主体、颁发者、有效期等）
//   - 执行安全状态检查（过期、自签名、撤销等）
//   - 计算多种哈希指纹（MD5、SHA1、SHA256）
//   - 解析可分辨名称（使用ZMap库获得更详细信息）
//   - 可选包含PEM格式的完整证书内容
func Convertx509toResponse(options *Options, hostname string, cert *x509.Certificate, showcert bool) *CertificateResponse {
	// 收集证书中的所有域名信息（主题通用名称 + 主题备用名称）
	domainNames := []string{cert.Subject.CommonName}
	domainNames = append(domainNames, cert.DNSNames...)

	// 创建并填充证书响应结构，包含所有基本信息和状态检查
	response := &CertificateResponse{
		// === 证书扩展信息 ===
		SubjectAN: cert.DNSNames,      // 主题备用名称列表（SAN）
		Emails:    cert.EmailAddresses, // 证书中包含的电子邮件地址列表

		// === 证书有效期信息 ===
		NotBefore: cert.NotBefore, // 证书生效时间
		NotAfter:  cert.NotAfter,  // 证书过期时间

		// === 证书状态检查 ===
		Expired:      IsExpired(cert.NotAfter),                             // 检查证书是否已过期
		SelfSigned:   IsSelfSigned(cert.AuthorityKeyId, cert.SubjectKeyId), // 检查是否为自签名证书
		MisMatched:   IsMisMatchedCert(hostname, domainNames),              // 检查证书域名是否与目标主机匹配
		Revoked:      IsTLSRevoked(options, cert),                          // 检查证书是否已被撤销
		WildCardCert: IsWildCardCert(domainNames),                          // 检查是否为通配符证书

		// === 颁发者信息 ===
		IssuerCN:  cert.Issuer.CommonName,   // 颁发者通用名称（CA名称）
		IssuerOrg: cert.Issuer.Organization, // 颁发者组织名称列表

		// === 主题信息 ===
		SubjectCN:  cert.Subject.CommonName,   // 主题通用名称（通常是域名）
		SubjectOrg: cert.Subject.Organization, // 主题组织名称列表

		// === 证书指纹哈希 ===
		FingerprintHash: CertificateResponseFingerprintHash{
			MD5:    MD5Fingerprint(cert.Raw),    // MD5指纹（128位）
			SHA1:   SHA1Fingerprint(cert.Raw),   // SHA1指纹（160位）
			SHA256: SHA256Fingerprint(cert.Raw), // SHA256指纹（256位，推荐）
		},

		// === 证书标识 ===
		Serial: FormatToSerialNumber(cert.SerialNumber), // 格式化的证书序列号
	}

	// 使用ZMap库解析可分辨名称，获得比标准库更详细的信息
	response.IssuerDN = ParseASN1DNSequenceWithZpkixOrDefault(cert.RawIssuer, cert.Issuer.String())
	response.SubjectDN = ParseASN1DNSequenceWithZpkixOrDefault(cert.RawSubject, cert.Subject.String())

	// 如果请求包含完整证书内容，添加PEM格式的证书
	if showcert {
		response.Certificate = PemEncode(cert.Raw)
	}

	// 如果启用了DNS域名显示，提取并去重所有域名
	if options.DisplayDns {
		response.Domains = GetUniqueDomainsFromCert(response)
	}

	return response
}

// GetUniqueDomainsFromCert 从证书响应中提取所有唯一的域名
// 合并主题通用名称和主题备用名称，去除重复项和通配符前缀
//
// 参数:
//   - resp: 包含证书信息的响应结构
//
// 返回值:
//   - []string: 去重后的域名列表，已移除通配符前缀
//
// 处理逻辑:
//   - 收集主题备用名称（SAN）中的所有域名
//   - 收集主题通用名称（CN）
//   - 移除通配符前缀（如将"*.example.com"转换为"example.com"）
//   - 使用map进行去重处理
func GetUniqueDomainsFromCert(resp *CertificateResponse) []string {
	// 使用map进行域名去重，struct{}作为值类型节省内存
	domains := map[string]struct{}{}

	// 处理主题备用名称列表中的每个域名
	for _, domain := range resp.SubjectAN {
		// 移除通配符前缀并添加到去重集合中
		domains[trimWildcardPrefix(domain)] = struct{}{}
	}

	// 处理主题通用名称（如果存在）
	if resp.SubjectCN != "" {
		// 移除通配符前缀并添加到去重集合中
		domains[trimWildcardPrefix(resp.SubjectCN)] = struct{}{}
	}

	// 从map中提取所有唯一的域名键
	return mapsutil.GetKeys(domains)
}

// trimWildcardPrefix 移除域名字符串开头的通配符前缀
// 将通配符域名转换为基础域名，便于域名匹配和去重
//
// 参数:
//   - hostname: 可能包含通配符前缀的域名字符串
//
// 返回值:
//   - string: 移除"*."前缀后的域名
//
// 示例:
//   - "*.example.com" -> "example.com"
//   - "example.com" -> "example.com" (无变化)
func trimWildcardPrefix(hostname string) string {
	return strings.TrimPrefix(hostname, "*.")
}

// IntersectStringSlices 计算两个字符串切片的交集
// 使用哈希表优化算法，时间复杂度为O(m+n)
//
// 参数:
//   - s1: 第一个字符串切片
//   - s2: 第二个字符串切片
//
// 返回值:
//   - []string: 包含两个切片共同元素的新切片
//
// 算法优化:
//   - 选择较小的切片构建哈希表，减少内存使用
//   - 遍历较大的切片进行查找，提高效率
//   - 适用于密码套件列表的交集计算等场景
func IntersectStringSlices(s1 []string, s2 []string) []string {
	res := []string{}                    // 存储交集结果
	slicemap := map[string]struct{}{}    // 用于快速查找的哈希表
	var rangeslice []string              // 用于遍历的切片

	// 选择较小的切片构建哈希表，较大的切片用于遍历
	// 这样可以减少哈希表的大小，提高内存效率
	if len(s1) < len(s2) {
		// s1较小，用s1构建哈希表，遍历s2
		for _, v := range s1 {
			slicemap[v] = struct{}{}
		}
		rangeslice = s2
	} else {
		// s2较小或相等，用s2构建哈希表，遍历s1
		for _, v := range s2 {
			slicemap[v] = struct{}{}
		}
		rangeslice = s1
	}

	// 遍历较大的切片，查找在哈希表中存在的元素
	for _, v := range rangeslice {
		if _, ok := slicemap[v]; ok {
			res = append(res, v) // 找到交集元素，添加到结果中
		}
	}

	return res
}

// GetConn 根据用户输入建立网络连接
// 支持IP地址和主机名连接，自动处理地址格式和超时设置
//
// 参数:
//   - ctx: 上下文对象，用于控制连接超时和取消操作
//   - hostname: 目标主机名或域名
//   - ip: 目标IP地址
//   - port: 目标端口号（字符串格式）
//   - inputOpts: TLS连接配置选项
//
// 返回值:
//   - net.Conn: 建立的TCP网络连接
//   - error: 连接过程中的错误，成功时为nil
//
// 连接策略:
//   - 优先使用IP地址连接（当启用ScanAllIPs或指定IPVersion时）
//   - 否则使用主机名连接（依赖DNS解析）
//   - 使用FastDialer提供高效的连接建立
//   - 自动设置连接超时（默认5秒）
func GetConn(ctx context.Context, hostname, ip, port string, inputOpts *Options) (net.Conn, error) {
	var address string

	// 根据配置决定使用IP地址还是主机名进行连接
	if iputil.IsIP(ip) && (inputOpts.ScanAllIPs || len(inputOpts.IPVersion) > 0) {
		// 使用IP地址连接：当IP有效且启用了IP扫描选项时
		address = net.JoinHostPort(ip, port)
	} else {
		// 使用主机名连接：默认情况或IP无效时
		address = net.JoinHostPort(hostname, port)
	}

	// 验证输入参数的有效性
	if (hostname == "" && ip == "") || port == "" {
		return nil, errorutil.New("client requires valid address got port=%v,hostname=%v,ip=%v", port, hostname, ip)
	}

	// 使用FastDialer建立TCP连接，提供更好的性能和控制
	rawConn, err := inputOpts.Fastdialer.Dial(ctx, "tcp", address)
	if err != nil {
		return nil, errorutil.New("could not dial address").Wrap(err)
	}

	// 检查连接是否成功建立
	if rawConn == nil {
		return nil, errorutil.New("could not connect to %s", address)
	}

	// 设置默认超时时间（如果未配置）
	if inputOpts.Timeout == 0 {
		inputOpts.Timeout = 5 // 默认5秒超时
	}

	// 为连接设置读写超时，防止连接挂起
	err = rawConn.SetDeadline(time.Now().Add(time.Duration(inputOpts.Timeout) * time.Second))
	return rawConn, err
}

// FormatToSerialNumber 将证书序列号从大整数格式转换为标准的十六进制字符串格式
// 生成冒号分隔的大写十六进制字符串，符合证书序列号的标准显示格式
//
// 参数:
//   - serialNumber: 证书的序列号（大整数格式）
//
// 返回值:
//   - string: 格式化的序列号字符串，如"0C:D0:A8:BE:C6:32:CF:E6:45:EC:A0:A9:B0:84:FB:1C"
//
// 格式说明:
//   - 将大整数转换为字节数组
//   - 每个字节转换为两位十六进制数
//   - 字节之间用冒号分隔
//   - 最终结果为大写格式
//
// 示例:
//   - 输入: 17034156255497985825694118641198758684
//   - 输出: "0C:D0:A8:BE:C6:32:CF:E6:45:EC:A0:A9:B0:84:FB:1C"
func FormatToSerialNumber(serialNumber *big.Int) string {
	// 检查序列号是否为空或零值
	if serialNumber == nil || serialNumber.Cmp(big.NewInt(0)) == 0 {
		return "" // 无效序列号返回空字符串
	}

	// 将大整数转换为字节数组
	b := serialNumber.Bytes()
	if len(b) == 0 {
		return "" // 空字节数组返回空字符串
	}

	// 创建足够大的缓冲区：每个字节需要2个十六进制字符+1个冒号
	buf := make([]byte, 0, 3*len(b))
	// 为十六进制编码预分配空间
	x := buf[1*len(b) : 3*len(b)]
	// 将字节数组编码为十六进制字符串
	hex.Encode(x, b)

	// 构建冒号分隔的格式：每两个十六进制字符后添加一个冒号
	for i := 0; i < len(x); i += 2 {
		buf = append(buf, x[i], x[i+1], ':')
	}

	// 转换为大写格式并移除最后多余的冒号
	return strings.ToUpper(string(buf[:len(buf)-1]))
}

// IsClientCertRequiredError 检查TLS错误是否由于服务器要求客户端证书认证而导致
// 用于识别需要客户端证书的TLS服务器，帮助确定服务器的认证要求
//
// 参数:
//   - err: 要分析的错误对象
//
// 返回值:
//   - bool: 如果错误是由于缺少客户端证书导致返回true，否则返回false
//
// 检查的错误类型:
//   - "bad certificate": 客户端提供的证书无效
//   - "certificate required": 服务器要求客户端提供证书但未提供
//
// 使用场景:
//   - 识别需要双向TLS认证的服务器
//   - 在扫描报告中标记客户端证书要求
//   - 帮助用户了解目标服务器的认证策略
func IsClientCertRequiredError(err error) bool {
	nerr := &net.OpError{} // 网络操作错误类型

	// 检查是否为网络操作错误，且操作类型为"remote error"（远程错误）
	if errors.As(err, &nerr) && nerr.Op == "remote error" {
		// 获取远程错误的详细信息
		rErr := nerr.Err.Error()
		// 移除TLS错误前缀，获取核心错误消息
		rErr = strings.TrimPrefix(rErr, "tls: ")

		// 检查特定的TLS客户端证书相关错误消息
		switch rErr {
		case "bad certificate":
			// 客户端证书无效或格式错误
			return true
		case "certificate required":
			// 服务器要求客户端证书但客户端未提供
			return true
		}
	}

	// 不是客户端证书相关的错误
	return false
}
