//
// Author: chenjb
// Version: V1.0
// Date: 2025-06-30
// FilePath: /yaml_scan/pkg/tlsx/tlsx/openssl/options_test.go
// Description: options.go的单元测试

package openssl

import (
	"crypto/x509"
	"strings"
	"testing"

	"github.com/stretchr/testify/require"
)

// TestSupportedTLSVersions 测试支持的TLS版本列表
// 验证SupportedTLSVersions常量的正确性
func TestSupportedTLSVersions(t *testing.T) {
	t.Run("版本列表内容验证", func(t *testing.T) {
		require.NotEmpty(t, SupportedTLSVersions, "支持的TLS版本列表不应该为空")
		
		expectedVersions := []string{"tls10", "tls11", "tls12"}
		require.Equal(t, expectedVersions, SupportedTLSVersions, "支持的TLS版本应该匹配")
		
		t.Logf("支持的TLS版本: %v", SupportedTLSVersions)
	})

	t.Run("版本格式验证", func(t *testing.T) {
		for _, version := range SupportedTLSVersions {
			require.NotEmpty(t, version, "版本字符串不应该为空")
			require.True(t, strings.HasPrefix(version, "tls"), "版本应该以'tls'开头")
			require.Len(t, version, 5, "版本字符串长度应该为5")
		}
	})

	t.Run("版本唯一性验证", func(t *testing.T) {
		versionSet := make(map[string]bool)
		for _, version := range SupportedTLSVersions {
			require.False(t, versionSet[version], "版本不应该重复: %s", version)
			versionSet[version] = true
		}
	})
}

// TestProtocols 测试协议枚举类型
// 验证Protocols枚举的定义和方法
func TestProtocols(t *testing.T) {
	t.Run("协议枚举值验证", func(t *testing.T) {
		require.Equal(t, Protocols(0), TLSv1, "TLSv1应该是0")
		require.Equal(t, Protocols(1), TLSv1_1, "TLSv1_1应该是1")
		require.Equal(t, Protocols(2), TLSv1_2, "TLSv1_2应该是2")
		require.Equal(t, Protocols(3), TLSv1_3, "TLSv1_3应该是3")
		require.Equal(t, Protocols(4), DTLSv1, "DTLSv1应该是4")
		require.Equal(t, Protocols(5), DTLSv1_2, "DTLSv1_2应该是5")
		require.Equal(t, Protocols(6), TLSUnsupported, "TLSUnsupported应该是6")
	})

	t.Run("String方法测试", func(t *testing.T) {
		testCases := []struct {
			protocol Protocols
			expected string
		}{
			{TLSv1, "tls1"},
			{TLSv1_1, "tls1_1"},
			{TLSv1_2, "tls1_2"},
			{DTLSv1, "dtls1"},
			{DTLSv1_2, "dtls1_2"},
			{TLSUnsupported, ""},
			{Protocols(999), ""}, // 未知协议
		}

		for _, tc := range testCases {
			result := tc.protocol.String()
			require.Equal(t, tc.expected, result, "协议%d的字符串表示应该是'%s'", tc.protocol, tc.expected)
		}
	})
}

// TestGetProtocol 测试getProtocol函数
// 验证TLS版本字符串到协议枚举的转换
func TestGetProtocol(t *testing.T) {
	t.Run("有效版本转换", func(t *testing.T) {
		testCases := []struct {
			version  string
			expected Protocols
		}{
			{"tls10", TLSv1},
			{"tls11", TLSv1_1},
			{"tls12", TLSv1_2},
		}

		for _, tc := range testCases {
			protocol, err := getProtocol(tc.version)
			require.NoError(t, err, "转换版本'%s'应该成功", tc.version)
			require.Equal(t, tc.expected, protocol, "版本'%s'应该转换为协议%d", tc.version, tc.expected)
		}
	})

	t.Run("无效版本转换", func(t *testing.T) {
		invalidVersions := []string{
			"tls13",     // 注释掉的版本
			"dtls10",    // 注释掉的版本
			"dtls12",    // 注释掉的版本
			"ssl30",     // 不支持的版本
			"invalid",   // 完全无效的版本
			"",          // 空字符串
		}

		for _, version := range invalidVersions {
			protocol, err := getProtocol(version)
			require.Error(t, err, "转换无效版本'%s'应该失败", version)
			require.Equal(t, TLSUnsupported, protocol, "无效版本应该返回TLSUnsupported")
			require.Contains(t, err.Error(), "unsupported", "错误信息应该包含'unsupported'")
		}
	})
}

// TestOptions 测试Options结构体
// 验证Options结构体的基本功能
func TestOptions(t *testing.T) {
	t.Run("Options结构体字段验证", func(t *testing.T) {
		opts := &Options{
			Address:       "example.com:443",
			Cipher:        []string{"AES128-SHA", "AES256-SHA"},
			ServerName:    "example.com",
			CertChain:     true,
			Protocol:      TLSv1_2,
			CAFile:        "/path/to/ca.pem",
			SkipCertParse: false,
		}

		require.Equal(t, "example.com:443", opts.Address, "Address字段应该正确设置")
		require.Equal(t, []string{"AES128-SHA", "AES256-SHA"}, opts.Cipher, "Cipher字段应该正确设置")
		require.Equal(t, "example.com", opts.ServerName, "ServerName字段应该正确设置")
		require.True(t, opts.CertChain, "CertChain字段应该正确设置")
		require.Equal(t, TLSv1_2, opts.Protocol, "Protocol字段应该正确设置")
		require.Equal(t, "/path/to/ca.pem", opts.CAFile, "CAFile字段应该正确设置")
		require.False(t, opts.SkipCertParse, "SkipCertParse字段应该正确设置")
	})

	t.Run("Options零值验证", func(t *testing.T) {
		opts := &Options{}
		
		require.Empty(t, opts.Address, "Address默认应该为空")
		require.Empty(t, opts.Cipher, "Cipher默认应该为空")
		require.Empty(t, opts.ServerName, "ServerName默认应该为空")
		require.False(t, opts.CertChain, "CertChain默认应该为false")
		require.Equal(t, Protocols(0), opts.Protocol, "Protocol默认应该为0")
		require.Empty(t, opts.CAFile, "CAFile默认应该为空")
		require.False(t, opts.SkipCertParse, "SkipCertParse默认应该为false")
	})
}

// TestOptionsArgs 测试Options.Args方法
// 验证OpenSSL命令参数生成功能
func TestOptionsArgs(t *testing.T) {
	t.Run("基本参数生成", func(t *testing.T) {
		opts := &Options{
			Address: "example.com:443",
		}

		args, err := opts.Args()
		require.NoError(t, err, "生成基本参数应该成功")
		require.Equal(t, []string{"s_client", "-connect", "example.com:443"}, args, "基本参数应该正确")
	})

	t.Run("完整参数生成", func(t *testing.T) {
		opts := &Options{
			Address:    "example.com:443",
			Cipher:     []string{"AES128-SHA", "AES256-SHA"},
			ServerName: "example.com",
			CertChain:  true,
			Protocol:   TLSv1_2,
			CAFile:     "/path/to/ca.pem",
		}

		args, err := opts.Args()
		require.NoError(t, err, "生成完整参数应该成功")
		
		expectedArgs := []string{
			"s_client",
			"-connect", "example.com:443",
			"-cipher", "AES128-SHA,AES256-SHA",
			"-servername", "example.com",
			"-showcerts",
			"-tls1_2",
			"-CAfile", "/path/to/ca.pem",
		}
		require.Equal(t, expectedArgs, args, "完整参数应该正确")
	})

	t.Run("缺少地址参数", func(t *testing.T) {
		opts := &Options{
			ServerName: "example.com",
			CertChain:  true,
		}

		args, err := opts.Args()
		require.Error(t, err, "缺少地址应该返回错误")
		require.Contains(t, err.Error(), "address missing", "错误信息应该包含'address missing'")
		require.Equal(t, []string{"s_client"}, args, "错误时应该只返回基础命令")
	})

	t.Run("空地址参数", func(t *testing.T) {
		opts := &Options{
			Address: "",
		}

		args, err := opts.Args()
		require.Error(t, err, "空地址应该返回错误")
		require.Contains(t, err.Error(), "address missing", "错误信息应该包含'address missing'")
	})

	t.Run("可选参数跳过", func(t *testing.T) {
		opts := &Options{
			Address:    "example.com:443",
			Cipher:     []string{}, // 空密码套件列表
			ServerName: "",         // 空服务器名称
			CertChain:  false,      // 不显示证书链
			Protocol:   TLSUnsupported, // 不支持的协议
			CAFile:     "",         // 空CA文件
		}

		args, err := opts.Args()
		require.NoError(t, err, "可选参数为空应该成功")
		require.Equal(t, []string{"s_client", "-connect", "example.com:443"}, args, 
			"空的可选参数应该被跳过")
	})

	t.Run("单个密码套件", func(t *testing.T) {
		opts := &Options{
			Address: "example.com:443",
			Cipher:  []string{"AES128-SHA"},
		}

		args, err := opts.Args()
		require.NoError(t, err, "单个密码套件应该成功")
		require.Contains(t, args, "-cipher", "应该包含-cipher参数")
		require.Contains(t, args, "AES128-SHA", "应该包含密码套件")
	})

	t.Run("多个密码套件连接", func(t *testing.T) {
		opts := &Options{
			Address: "example.com:443",
			Cipher:  []string{"AES128-SHA", "AES256-SHA", "ECDHE-RSA-AES128-GCM-SHA256"},
		}

		args, err := opts.Args()
		require.NoError(t, err, "多个密码套件应该成功")
		
		// 查找-cipher参数的位置
		cipherIndex := -1
		for i, arg := range args {
			if arg == "-cipher" {
				cipherIndex = i
				break
			}
		}
		require.NotEqual(t, -1, cipherIndex, "应该包含-cipher参数")
		require.Less(t, cipherIndex+1, len(args), "cipher参数后应该有值")
		
		cipherValue := args[cipherIndex+1]
		require.Equal(t, "AES128-SHA,AES256-SHA,ECDHE-RSA-AES128-GCM-SHA256", cipherValue,
			"多个密码套件应该用逗号连接")
	})
}

// TestSession 测试Session结构体
// 验证Session结构体的基本功能
func TestSession(t *testing.T) {
	t.Run("Session结构体字段验证", func(t *testing.T) {
		session := &Session{
			Protocol:  "TLSv1.2",
			Cipher:    "ECDHE-RSA-AES256-GCM-SHA384",
			SessionID: "1234567890ABCDEF",
			MasterKey: "FEDCBA0987654321",
		}

		require.Equal(t, "TLSv1.2", session.Protocol, "Protocol字段应该正确设置")
		require.Equal(t, "ECDHE-RSA-AES256-GCM-SHA384", session.Cipher, "Cipher字段应该正确设置")
		require.Equal(t, "1234567890ABCDEF", session.SessionID, "SessionID字段应该正确设置")
		require.Equal(t, "FEDCBA0987654321", session.MasterKey, "MasterKey字段应该正确设置")
	})

	t.Run("Session零值验证", func(t *testing.T) {
		session := &Session{}

		require.Empty(t, session.Protocol, "Protocol默认应该为空")
		require.Empty(t, session.Cipher, "Cipher默认应该为空")
		require.Empty(t, session.SessionID, "SessionID默认应该为空")
		require.Empty(t, session.MasterKey, "MasterKey默认应该为空")
	})
}

// TestSessionGetTLSVersion 测试Session.getTLSVersion方法
// 验证TLS版本标准化功能
func TestSessionGetTLSVersion(t *testing.T) {
	t.Run("标准TLS版本转换", func(t *testing.T) {
		testCases := []struct {
			protocol string
			expected string
		}{
			{"TLSv1", "tls10"},
			{"TLSv1.1", "tls11"},
			{"TLSv1.2", "tls12"},
		}

		for _, tc := range testCases {
			session := &Session{Protocol: tc.protocol}
			result := session.getTLSVersion()
			require.Equal(t, tc.expected, result, "协议'%s'应该转换为'%s'", tc.protocol, tc.expected)
		}
	})

	t.Run("未知协议版本", func(t *testing.T) {
		unknownProtocols := []string{
			"TLSv1.3",    // 注释掉的版本
			"SSLv3",      // 旧版本
			"DTLSv1",     // DTLS版本
			"Unknown",    // 完全未知
			"",           // 空字符串
		}

		for _, protocol := range unknownProtocols {
			session := &Session{Protocol: protocol}
			result := session.getTLSVersion()
			require.Equal(t, protocol, result, "未知协议'%s'应该原样返回", protocol)
		}
	})

	t.Run("大小写敏感性", func(t *testing.T) {
		// 测试大小写敏感性
		session := &Session{Protocol: "tlsv1.2"} // 小写
		result := session.getTLSVersion()
		require.Equal(t, "tlsv1.2", result, "大小写不匹配应该原样返回")
	})
}

// TestResponse 测试Response结构体
// 验证Response结构体的基本功能
func TestResponse(t *testing.T) {
	t.Run("Response结构体字段验证", func(t *testing.T) {
		// 创建测试证书（这里使用nil，实际使用中会是真实证书）
		var testCerts []*x509.Certificate
		testSession := &Session{
			Protocol: "TLSv1.2",
			Cipher:   "ECDHE-RSA-AES256-GCM-SHA384",
		}

		response := &Response{
			AllCerts:           testCerts,
			Session:            testSession,
			ClientCertRequired: true,
		}

		require.Equal(t, testCerts, response.AllCerts, "AllCerts字段应该正确设置")
		require.Equal(t, testSession, response.Session, "Session字段应该正确设置")
		require.True(t, response.ClientCertRequired, "ClientCertRequired字段应该正确设置")
	})

	t.Run("Response零值验证", func(t *testing.T) {
		response := &Response{}

		require.Nil(t, response.AllCerts, "AllCerts默认应该为nil")
		require.Nil(t, response.Session, "Session默认应该为nil")
		require.False(t, response.ClientCertRequired, "ClientCertRequired默认应该为false")
	})

	t.Run("Response字段类型验证", func(t *testing.T) {
		response := &Response{}

		require.IsType(t, []*x509.Certificate{}, response.AllCerts, "AllCerts应该是证书切片类型")
		require.IsType(t, (*Session)(nil), response.Session, "Session应该是Session指针类型")
		require.IsType(t, false, response.ClientCertRequired, "ClientCertRequired应该是布尔类型")
	})
}

// TestProtocolStringConsistency 测试协议字符串一致性
// 验证协议枚举和字符串转换的一致性
func TestProtocolStringConsistency(t *testing.T) {
	t.Run("支持的协议一致性", func(t *testing.T) {
		// 验证SupportedTLSVersions中的每个版本都能正确转换
		for _, version := range SupportedTLSVersions {
			protocol, err := getProtocol(version)
			require.NoError(t, err, "支持的版本'%s'应该能正确转换", version)

			protocolStr := protocol.String()
			require.NotEmpty(t, protocolStr, "协议%d应该有非空字符串表示", protocol)

			t.Logf("版本'%s' -> 协议%d -> 字符串'%s'", version, protocol, protocolStr)
		}
	})

	t.Run("协议字符串双向转换", func(t *testing.T) {
		// 测试从协议枚举到字符串再回到协议的转换
		supportedProtocols := []Protocols{TLSv1, TLSv1_1, TLSv1_2}

		for _, protocol := range supportedProtocols {
			protocolStr := protocol.String()
			require.NotEmpty(t, protocolStr, "协议%d应该有字符串表示", protocol)

			// 注意：这里不能直接反向转换，因为getProtocol使用的是不同的格式
			// 这个测试主要验证String()方法的一致性
			t.Logf("协议%d的字符串表示: '%s'", protocol, protocolStr)
		}
	})
}

// TestEdgeCases 测试边界条件
// 验证各种边界情况的处理
func TestEdgeCases(t *testing.T) {
	t.Run("极长地址处理", func(t *testing.T) {
		// 测试极长的地址字符串
		longAddress := strings.Repeat("a", 1000) + ":443"
		opts := &Options{Address: longAddress}

		args, err := opts.Args()
		require.NoError(t, err, "极长地址应该能正常处理")
		require.Contains(t, args, longAddress, "参数中应该包含完整地址")
	})

	t.Run("特殊字符处理", func(t *testing.T) {
		// 测试包含特殊字符的参数
		opts := &Options{
			Address:    "test-server.example.com:443",
			ServerName: "test-server.example.com",
			CAFile:     "/path/with spaces/ca.pem",
		}

		args, err := opts.Args()
		require.NoError(t, err, "特殊字符应该能正常处理")
		require.Contains(t, args, "test-server.example.com:443", "应该包含带连字符的地址")
		require.Contains(t, args, "/path/with spaces/ca.pem", "应该包含带空格的路径")
	})

	t.Run("大量密码套件", func(t *testing.T) {
		// 测试大量密码套件的处理
		manyCiphers := make([]string, 100)
		for i := 0; i < 100; i++ {
			manyCiphers[i] = fmt.Sprintf("CIPHER_%d", i)
		}

		opts := &Options{
			Address: "example.com:443",
			Cipher:  manyCiphers,
		}

		args, err := opts.Args()
		require.NoError(t, err, "大量密码套件应该能正常处理")

		// 查找cipher参数
		cipherIndex := -1
		for i, arg := range args {
			if arg == "-cipher" {
				cipherIndex = i
				break
			}
		}
		require.NotEqual(t, -1, cipherIndex, "应该包含-cipher参数")

		cipherValue := args[cipherIndex+1]
		require.Contains(t, cipherValue, "CIPHER_0", "应该包含第一个密码套件")
		require.Contains(t, cipherValue, "CIPHER_99", "应该包含最后一个密码套件")
		require.Equal(t, 99, strings.Count(cipherValue, ","), "应该有99个逗号分隔符")
	})
}
