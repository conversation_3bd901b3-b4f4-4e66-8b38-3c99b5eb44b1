// Author: chenjb
// Version: V1.0
// Date: 2025-06-23 20:20:04
// FilePath: /yaml_scan/pkg/tlsx/tlsx/clients/ciphers.go
// Description: TLS密码套件安全级别定义和相关工具函数

// Package clients 提供TLS密码套件的安全级别分类和筛选功能
// 根据密码套件的加密强度、算法安全性等因素进行分级
package clients

import (
	"strings"                       // 字符串操作工具
	"yaml_scan/pkg/tlsx/assets"     // TLS资产数据，包含预定义的密码套件分类
)

// CipherSecLevel 定义密码套件的安全级别枚举
// 用于对TLS密码套件进行安全性分类和风险评估
type CipherSecLevel uint

const (
	// All 表示所有密码套件，不进行安全级别限制
	// 用于需要测试所有可用密码套件的场景
	All CipherSecLevel = iota

	// Weak 表示弱密码套件
	// 特征：存在已知安全漏洞或使用强度不足的加密算法
	// 示例：使用RC4、DES、3DES等弱加密算法的密码套件
	// 风险：容易被攻击者破解，不应在生产环境中使用
	Weak

	// Insecure 表示不安全密码套件
	// 特征：虽然不如Weak级别危险，但仍存在安全隐患
	// 示例：使用较弱的密钥交换算法、短密钥长度等
	// 风险：在特定条件下可能被攻击，建议避免使用
	Insecure

	// Secure 表示安全密码套件
	// 特征：符合当前安全标准，使用强加密算法和安全的密钥交换方法
	// 示例：使用AES-GCM、ChaCha20-Poly1305、ECDHE等现代算法
	// 推荐：生产环境中应优先使用此级别的密码套件
	Secure

	// Unknown 表示未知安全级别的密码套件
	// 特征：未在预定义的安全级别映射表中找到对应分类
	// 原因：可能是新的密码套件或非标准密码套件
	// 处理：需要人工评估其安全性
	Unknown
)

// GetCiphersWithLevel 根据指定的安全级别筛选密码套件列表
// 支持同时指定多个安全级别，返回符合任一级别的所有密码套件
//
// 参数:
//   - cipherList: 待筛选的密码套件名称列表，通常来自TLS库支持的密码套件
//   - SecLevel: 可变参数，指定要包含的安全级别（可同时指定多个）
//
// 返回值:
//   - []string: 符合指定安全级别的密码套件列表
//
// 使用场景:
//   - 密码套件枚举时按安全级别过滤
//   - 安全审计时分析特定级别的密码套件
//   - 配置TLS客户端时选择合适的密码套件
//
// 示例:
//   - GetCiphersWithLevel(allCiphers, Secure) // 仅返回安全密码套件
//   - GetCiphersWithLevel(allCiphers, Weak, Insecure) // 返回弱和不安全密码套件
func GetCiphersWithLevel(cipherList []string, SecLevel ...CipherSecLevel) []string {
	toEnumerate := []string{} // 存储筛选结果的切片

	// 如果未指定任何安全级别，返回所有输入的密码套件
	if len(SecLevel) == 0 {
		return cipherList
	}

	// 遍历每个指定的安全级别，收集符合条件的密码套件
	for _, level := range SecLevel {
		switch level {
		case All:
			// 如果指定了All级别，直接返回所有密码套件，无需进一步筛选
			return cipherList

		case Weak:
			// 获取输入列表中属于弱安全级别的密码套件
			// 通过与预定义的弱密码套件列表求交集实现
			weakCiphers := IntersectStringSlices(cipherList, assets.GetWeakCipherSuites())
			toEnumerate = append(toEnumerate, weakCiphers...)

		case Insecure:
			// 获取输入列表中属于不安全级别的密码套件
			// 通过与预定义的不安全密码套件列表求交集实现
			insecureCiphers := IntersectStringSlices(cipherList, assets.GetInSecureCipherSuites())
			toEnumerate = append(toEnumerate, insecureCiphers...)

		case Secure:
			// 获取输入列表中属于安全级别的密码套件
			// 通过与预定义的安全密码套件列表求交集实现
			secureCiphers := IntersectStringSlices(cipherList, assets.GetSecureCipherSuites())
			toEnumerate = append(toEnumerate, secureCiphers...)
		}
	}

	return toEnumerate
}

// GetCipherLevel 评估指定密码套件的安全级别
// 通过查询预定义的密码套件安全级别映射表来确定安全等级
//
// 参数:
//   - cipherName: 要评估的密码套件名称
//     示例: "TLS_RSA_WITH_AES_128_CBC_SHA", "TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384"
//
// 返回值:
//   - CipherSecLevel: 密码套件的安全级别枚举值
//     * Secure: 安全密码套件，推荐使用
//     * Insecure: 不安全密码套件，存在安全隐患
//     * Weak: 弱密码套件，存在已知漏洞
//     * Unknown: 未知安全级别，未在映射表中找到
//
// 实现逻辑:
//   - 遍历预定义的密码套件安全级别映射表
//   - 使用大小写不敏感的字符串比较
//   - 将字符串级别转换为枚举值
//   - 未找到匹配项时返回Unknown级别
func GetCipherLevel(cipherName string) CipherSecLevel {
	// 遍历预定义的密码套件安全级别映射表
	for k, v := range assets.CipherSecLevel {
		// 使用大小写不敏感的字符串比较
		if strings.EqualFold(k, cipherName) {
			// 根据字符串值转换为对应的安全级别枚举
			switch v {
			case "Recommended":
				// 推荐级别映射为安全级别
				return Secure
			case "Secure":
				// 安全级别
				return Secure
			case "Insecure":
				// 不安全级别
				return Insecure
			case "Weak":
				// 弱安全级别
				return Weak
			}
		}
	}

	// 如果在映射表中未找到对应的密码套件，返回未知级别
	return Unknown
}