<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TLS扫描库 - 部署指南</title>
    <style>
        :root {
            --primary: #3b82f6;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --dark: #1f2937;
            --light: #f8fafc;
        }

        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Segoe UI', sans-serif;
            line-height: 1.6;
            color: var(--dark);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        h1 {
            text-align: center;
            color: var(--primary);
            margin-bottom: 30px;
            font-size: 2.5rem;
        }

        h2 {
            color: var(--primary);
            margin: 30px 0 20px 0;
            font-size: 2rem;
            border-bottom: 3px solid var(--primary);
            padding-bottom: 10px;
        }

        h3 {
            color: var(--primary);
            margin: 25px 0 15px 0;
            font-size: 1.5rem;
        }

        .nav {
            background: #f8fafc;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: center;
        }

        .nav a {
            color: var(--primary);
            text-decoration: none;
            margin: 0 15px;
            padding: 8px 16px;
            border-radius: 8px;
            transition: background 0.3s;
        }

        .nav a:hover {
            background: var(--primary);
            color: white;
        }

        .deployment-option {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border-left: 5px solid var(--primary);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .deployment-option h4 {
            color: var(--primary);
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .code {
            background: var(--dark);
            color: #e2e8f0;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            overflow-x: auto;
            margin: 15px 0;
            position: relative;
        }

        .code::before {
            content: attr(data-lang);
            position: absolute;
            top: 10px;
            right: 15px;
            background: var(--primary);
            color: white;
            padding: 4px 8px;
            border-radius: 5px;
            font-size: 0.8rem;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            border-top: 4px solid var(--primary);
        }

        .card h4 {
            color: var(--primary);
            margin-bottom: 15px;
        }

        .requirements-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .requirements-table th {
            background: var(--primary);
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 600;
        }

        .requirements-table td {
            padding: 15px;
            border-bottom: 1px solid #e5e7eb;
        }

        .requirements-table tr:hover {
            background: #f8fafc;
        }

        .highlight {
            background: linear-gradient(45deg, #fef3c7, #fde68a);
            padding: 20px;
            border-radius: 15px;
            border-left: 5px solid var(--warning);
            margin: 20px 0;
        }

        .success-box {
            background: linear-gradient(45deg, #d1fae5, #a7f3d0);
            padding: 20px;
            border-radius: 15px;
            border-left: 5px solid var(--success);
            margin: 20px 0;
        }

        .warning-box {
            background: linear-gradient(45deg, #fef3c7, #fde68a);
            padding: 20px;
            border-radius: 15px;
            border-left: 5px solid var(--warning);
            margin: 20px 0;
        }

        .step {
            background: #f8fafc;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid var(--success);
        }

        .step-number {
            background: var(--primary);
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
        }

        ul {
            margin: 15px 0;
            padding-left: 20px;
        }

        li {
            margin: 8px 0;
        }

        @media (max-width: 768px) {
            .container { padding: 20px; }
            .grid { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 TLS扫描库部署指南</h1>
        
        <div class="nav">
            <a href="#requirements">环境要求</a>
            <a href="#docker">Docker部署</a>
            <a href="#kubernetes">Kubernetes</a>
            <a href="#cloud">云平台</a>
            <a href="#monitoring">监控配置</a>
            <a href="index.html">返回主文档</a>
        </div>

        <!-- 环境要求 -->
        <section id="requirements">
            <h2>📋 环境要求</h2>
            
            <table class="requirements-table">
                <thead>
                    <tr>
                        <th>组件</th>
                        <th>最低要求</th>
                        <th>推荐配置</th>
                        <th>说明</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>Go版本</strong></td>
                        <td>1.24+</td>
                        <td>1.24+</td>
                        <td>支持最新TLS特性和性能优化</td>
                    </tr>
                    <tr>
                        <td><strong>CPU</strong></td>
                        <td>1核</td>
                        <td>4核+</td>
                        <td>多核提升并发扫描性能</td>
                    </tr>
                    <tr>
                        <td><strong>内存</strong></td>
                        <td>512MB</td>
                        <td>2GB+</td>
                        <td>并发扫描和结果缓存需要更多内存</td>
                    </tr>
                    <tr>
                        <td><strong>存储</strong></td>
                        <td>1GB</td>
                        <td>10GB+</td>
                        <td>存储扫描结果、日志和临时文件</td>
                    </tr>
                    <tr>
                        <td><strong>网络</strong></td>
                        <td>100Mbps</td>
                        <td>1Gbps+</td>
                        <td>高带宽支持大规模并发扫描</td>
                    </tr>
                    <tr>
                        <td><strong>操作系统</strong></td>
                        <td>Linux/Windows/macOS</td>
                        <td>Linux</td>
                        <td>Linux提供最佳性能和稳定性</td>
                    </tr>
                </tbody>
            </table>

            <div class="warning-box">
                <strong>⚠️ 注意事项:</strong>
                <ul>
                    <li>确保系统时间同步，证书验证依赖准确的时间</li>
                    <li>配置足够的文件描述符限制（ulimit -n）</li>
                    <li>确保网络连接稳定，避免频繁的连接中断</li>
                    <li>生产环境建议使用专用的扫描网络</li>
                </ul>
            </div>
        </section>

        <!-- Docker部署 -->
        <section id="docker">
            <h2>🐳 Docker容器化部署</h2>
            
            <div class="deployment-option">
                <h4>1. 基础Docker部署</h4>
                <p>适用于单机部署和开发测试环境</p>
                
                <div class="code" data-lang="dockerfile">
# Dockerfile
FROM golang:1.24-alpine AS builder

# 设置工作目录
WORKDIR /app

# 安装依赖
RUN apk add --no-cache git ca-certificates openssl

# 复制源码
COPY . .

# 下载依赖
RUN go mod download

# 构建应用
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o tlsx-scanner ./cmd/scanner

# 运行时镜像
FROM alpine:latest

# 安装运行时依赖
RUN apk add --no-cache ca-certificates openssl tzdata

# 创建非root用户
RUN addgroup -g 1001 scanner && \
    adduser -D -s /bin/sh -u 1001 -G scanner scanner

# 创建必要目录
RUN mkdir -p /app/config /app/results /app/logs && \
    chown -R scanner:scanner /app

# 复制二进制文件
COPY --from=builder /app/tlsx-scanner /usr/local/bin/
RUN chmod +x /usr/local/bin/tlsx-scanner

# 切换到非root用户
USER scanner

# 设置工作目录
WORKDIR /app

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD tlsx-scanner --health || exit 1

# 入口点
ENTRYPOINT ["tlsx-scanner"]
CMD ["--help"]
                </div>
                
                <div class="code" data-lang="bash">
# 构建镜像
docker build -t tlsx-scanner:latest .

# 运行容器
docker run -d \
  --name tlsx-scanner \
  --restart unless-stopped \
  -v $(pwd)/config:/app/config:ro \
  -v $(pwd)/results:/app/results \
  -v $(pwd)/logs:/app/logs \
  -e SCAN_MODE=ctls \
  -e TIMEOUT=10 \
  -e CONCURRENCY=5 \
  tlsx-scanner:latest \
  --mode ctls \
  --timeout 10 \
  --concurrency 5 \
  --json \
  --output /app/results/scan.json \
  --targets /app/config/targets.txt
                </div>
            </div>

            <div class="deployment-option">
                <h4>2. Docker Compose部署</h4>
                <p>适用于多服务协同和复杂配置</p>
                
                <div class="code" data-lang="yaml">
# docker-compose.yml
version: '3.8'

services:
  tlsx-scanner:
    build: .
    container_name: tlsx-scanner
    restart: unless-stopped
    
    # 资源限制
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '2'
        reservations:
          memory: 512M
          cpus: '0.5'
    
    # 环境变量
    environment:
      - SCAN_MODE=ctls
      - TIMEOUT=10
      - CONCURRENCY=5
      - LOG_LEVEL=info
      - TZ=Asia/Shanghai
    
    # 挂载卷
    volumes:
      - ./config:/app/config:ro
      - ./results:/app/results
      - ./logs:/app/logs
      - /etc/ssl/certs:/etc/ssl/certs:ro
    
    # 网络配置
    networks:
      - scanner-network
    
    # 健康检查
    healthcheck:
      test: ["CMD", "tlsx-scanner", "--health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # 依赖服务
    depends_on:
      - redis
      - postgres

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: tlsx-redis
    restart: unless-stopped
    volumes:
      - redis-data:/data
    networks:
      - scanner-network
    command: redis-server --appendonly yes

  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    container_name: tlsx-postgres
    restart: unless-stopped
    environment:
      - POSTGRES_DB=tlsx
      - POSTGRES_USER=scanner
      - POSTGRES_PASSWORD=secure_password
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./sql/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - scanner-network

  # Grafana监控
  grafana:
    image: grafana/grafana:latest
    container_name: tlsx-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana-data:/var/lib/grafana
      - ./grafana/dashboards:/etc/grafana/provisioning/dashboards
    networks:
      - scanner-network

volumes:
  redis-data:
  postgres-data:
  grafana-data:

networks:
  scanner-network:
    driver: bridge
                </div>
                
                <div class="code" data-lang="bash">
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f tlsx-scanner

# 停止服务
docker-compose down

# 清理数据
docker-compose down -v
                </div>
            </div>
        </section>

        <!-- Kubernetes部署 -->
        <section id="kubernetes">
            <h2>☸️ Kubernetes集群部署</h2>

            <div class="deployment-option">
                <h4>1. 基础Kubernetes部署</h4>
                <p>适用于生产环境的高可用部署</p>

                <div class="code" data-lang="yaml">
# k8s-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: tlsx-scanner
  namespace: tlsx-scanner
  labels:
    app: tlsx-scanner
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 1
  selector:
    matchLabels:
      app: tlsx-scanner
  template:
    metadata:
      labels:
        app: tlsx-scanner
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8080"
    spec:
      serviceAccountName: tlsx-scanner
      securityContext:
        runAsNonRoot: true
        runAsUser: 1001
        fsGroup: 1001
      containers:
      - name: tlsx-scanner
        image: tlsx-scanner:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
          name: http

        # 环境变量
        env:
        - name: SCAN_MODE
          value: "ctls"
        - name: POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name

        # 资源配置
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "2000m"

        # 健康检查
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10

        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5

        # 挂载配置
        volumeMounts:
        - name: config-volume
          mountPath: /app/config
          readOnly: true
        - name: results-volume
          mountPath: /app/results

      volumes:
      - name: config-volume
        configMap:
          name: tlsx-config
      - name: results-volume
        persistentVolumeClaim:
          claimName: tlsx-results-pvc

---
# k8s-service.yaml
apiVersion: v1
kind: Service
metadata:
  name: tlsx-scanner-service
  namespace: tlsx-scanner
spec:
  selector:
    app: tlsx-scanner
  ports:
  - name: http
    protocol: TCP
    port: 80
    targetPort: 8080
  type: ClusterIP
                </div>
            </div>

            <div class="deployment-option">
                <h4>2. Helm Chart部署</h4>
                <p>使用Helm进行包管理和版本控制</p>

                <div class="code" data-lang="bash">
# 安装Helm Chart
helm repo add tlsx https://charts.tlsx.io
helm repo update

# 安装到生产环境
helm install tlsx-prod tlsx/tlsx-scanner \
  --namespace tlsx-scanner \
  --create-namespace \
  --set image.tag=latest \
  --set replicaCount=3 \
  --set resources.limits.memory=2Gi

# 升级部署
helm upgrade tlsx-prod tlsx/tlsx-scanner

# 回滚到上一版本
helm rollback tlsx-prod 1
                </div>
            </div>
        </section>

        <!-- 云平台部署 -->
        <section id="cloud">
            <h2>☁️ 云平台部署</h2>

            <div class="grid">
                <div class="card">
                    <h4>🚀 AWS Lambda</h4>
                    <p>无服务器函数部署</p>
                    <div class="code" data-lang="yaml">
# serverless.yml
service: tlsx-scanner

provider:
  name: aws
  runtime: go1.x
  region: us-east-1
  timeout: 300
  memorySize: 1024

functions:
  scan:
    handler: bin/scanner
    events:
      - schedule: rate(1 hour)
      - http:
          path: /scan
          method: post
    environment:
      SCAN_MODE: ctls
      TIMEOUT: 30
                    </div>
                </div>

                <div class="card">
                    <h4>🔵 Azure Container Instances</h4>
                    <p>容器实例部署</p>
                    <div class="code" data-lang="bash">
# 创建资源组
az group create --name tlsx-rg --location eastus

# 部署容器实例
az container create \
  --resource-group tlsx-rg \
  --name tlsx-scanner \
  --image tlsx-scanner:latest \
  --cpu 2 \
  --memory 4 \
  --restart-policy OnFailure
                    </div>
                </div>

                <div class="card">
                    <h4>🟡 Google Cloud Run</h4>
                    <p>完全托管的容器平台</p>
                    <div class="code" data-lang="bash">
# 构建并推送镜像
gcloud builds submit --tag gcr.io/PROJECT-ID/tlsx-scanner

# 部署到Cloud Run
gcloud run deploy tlsx-scanner \
  --image gcr.io/PROJECT-ID/tlsx-scanner \
  --platform managed \
  --region us-central1 \
  --memory 2Gi \
  --cpu 2
                    </div>
                </div>
            </div>
        </section>

        <!-- 监控配置 -->
        <section id="monitoring">
            <h2>📊 监控和日志配置</h2>

            <div class="deployment-option">
                <h4>1. Prometheus监控</h4>
                <div class="code" data-lang="yaml">
# prometheus-config.yaml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'tlsx-scanner'
    static_configs:
      - targets: ['tlsx-scanner:8080']
    metrics_path: /metrics
    scrape_interval: 30s
                </div>
            </div>

            <div class="success-box">
                <strong>✅ 部署检查清单:</strong>
                <ul>
                    <li>✓ 环境要求满足</li>
                    <li>✓ 配置文件正确</li>
                    <li>✓ 网络连接正常</li>
                    <li>✓ 存储空间充足</li>
                    <li>✓ 监控告警配置</li>
                    <li>✓ 备份策略制定</li>
                    <li>✓ 安全策略实施</li>
                    <li>✓ 性能测试通过</li>
                </ul>
            </div>
        </section>

        <footer style="text-align: center; margin-top: 30px; padding: 20px; background: #f8fafc; border-radius: 15px;">
            <p>🚀 <strong>TLS扫描库部署指南</strong></p>
            <p>更多信息请查看 <a href="index.html" style="color: var(--primary);">完整文档</a> 或 <a href="api-reference.html" style="color: var(--primary);">API参考</a></p>
        </footer>
    </div>
</body>
</html>
