//
// Author: chenjb
// Version: V1.0
// Date: 2025-06-26
// FilePath: /yaml_scan/pkg/tlsx/tlsx/ztls/util_test.go
// Description: ztls包util.go的单元测试

package ztls

import (
	"fmt"
	"strings"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
)

// TestGlobalVariablesInitialization 测试全局变量的初始化
// 验证包初始化时全局变量的正确设置
func TestGlobalVariablesInitialization(t *testing.T) {
	// 测试AllCiphers全局变量
	t.Run("AllCiphers初始化", func(t *testing.T) {
		require.NotNil(t, AllCiphers, "AllCiphers不应该为nil")
		require.NotEmpty(t, AllCiphers, "AllCiphers不应该为空")
		
		// 验证密码套件数值的有效性
		for _, cipher := range AllCiphers {
			require.Greater(t, cipher, uint16(0), "密码套件数值应该大于0")
		}
		
		t.Logf("总共初始化了 %d 个密码套件数值", len(AllCiphers))
	})

	// 测试AllCiphersNames全局变量
	t.Run("AllCiphersNames初始化", func(t *testing.T) {
		require.NotNil(t, AllCiphersNames, "AllCiphersNames不应该为nil")
		require.NotEmpty(t, AllCiphersNames, "AllCiphersNames不应该为空")
		
		// 验证密码套件名称格式
		for _, cipher := range AllCiphersNames {
			require.NotEmpty(t, cipher, "密码套件名称不应该为空")
			// ZMap支持更多类型的密码套件，包括SSL_前缀
			require.True(t, 
				len(cipher) >= 4 && (cipher[:4] == "TLS_" || cipher[:4] == "SSL_"), 
				"密码套件名称应该以TLS_或SSL_开头: %s", cipher)
		}
		
		t.Logf("总共初始化了 %d 个密码套件名称", len(AllCiphersNames))
		
		// 显示前几个密码套件作为示例
		maxShow := 5
		if len(AllCiphersNames) < maxShow {
			maxShow = len(AllCiphersNames)
		}
		t.Logf("示例密码套件: %v", AllCiphersNames[:maxShow])
	})

	// 测试SupportedTlsVersions全局变量
	t.Run("SupportedTlsVersions初始化", func(t *testing.T) {
		require.NotNil(t, SupportedTlsVersions, "SupportedTlsVersions不应该为nil")
		require.NotEmpty(t, SupportedTlsVersions, "SupportedTlsVersions不应该为空")
		
		// ZMap支持更多TLS版本，包括SSL 3.0
		expectedVersions := []string{"1.0", "1.1", "1.2", "1.3"}
		foundCount := 0
		for _, expected := range expectedVersions {
			for _, version := range SupportedTlsVersions {
				if version == expected {
					foundCount++
					break
				}
			}
		}
		require.Greater(t, foundCount, 0, "应该至少支持一些标准TLS版本")
		
		t.Logf("支持的TLS版本: %v", SupportedTlsVersions)
	})
}

// TestAllCiphersConsistency 测试密码套件数组的一致性
// 验证AllCiphers和AllCiphersNames的长度一致性
func TestAllCiphersConsistency(t *testing.T) {
	require.Equal(t, len(AllCiphers), len(AllCiphersNames), 
		"AllCiphers和AllCiphersNames的长度应该一致")
	
	t.Logf("密码套件数组一致性验证通过：%d个密码套件", len(AllCiphers))
}

// TestAllCiphersUniqueness 测试密码套件的唯一性
// 验证密码套件数组中没有重复项
func TestAllCiphersUniqueness(t *testing.T) {
	// 测试密码套件数值的唯一性
	t.Run("密码套件数值唯一性", func(t *testing.T) {
		seen := make(map[uint16]bool)
		duplicates := []uint16{}
		
		for _, cipher := range AllCiphers {
			if seen[cipher] {
				duplicates = append(duplicates, cipher)
			}
			seen[cipher] = true
		}
		
		require.Empty(t, duplicates, "密码套件数值中不应该有重复项: %v", duplicates)
		t.Logf("验证通过：%d个密码套件数值都是唯一的", len(AllCiphers))
	})

	// 测试密码套件名称的唯一性
	t.Run("密码套件名称唯一性", func(t *testing.T) {
		seen := make(map[string]bool)
		duplicates := []string{}
		
		for _, cipher := range AllCiphersNames {
			if seen[cipher] {
				duplicates = append(duplicates, cipher)
			}
			seen[cipher] = true
		}
		
		require.Empty(t, duplicates, "密码套件名称中不应该有重复项: %v", duplicates)
		t.Logf("验证通过：%d个密码套件名称都是唯一的", len(AllCiphersNames))
	})
}

// TestSupportedTlsVersionsUniqueness 测试TLS版本的唯一性
// 验证TLS版本数组中没有重复项
func TestSupportedTlsVersionsUniqueness(t *testing.T) {
	seen := make(map[string]bool)
	duplicates := []string{}
	
	for _, version := range SupportedTlsVersions {
		if seen[version] {
			duplicates = append(duplicates, version)
		}
		seen[version] = true
	}
	
	require.Empty(t, duplicates, "TLS版本中不应该有重复项: %v", duplicates)
	require.Equal(t, len(SupportedTlsVersions), len(seen), "TLS版本数组长度应该等于唯一项数量")
	
	t.Logf("验证通过：%d个TLS版本都是唯一的", len(SupportedTlsVersions))
}

// TestCipherNamesContent 测试密码套件名称的内容
// 验证包含预期的常见密码套件
func TestCipherNamesContent(t *testing.T) {
	// 一些常见的密码套件，应该在列表中
	expectedCiphers := []string{
		"TLS_RSA_WITH_AES_128_CBC_SHA",
		"TLS_RSA_WITH_AES_256_CBC_SHA",
		"TLS_RSA_WITH_AES_128_GCM_SHA256",
		"TLS_RSA_WITH_AES_256_GCM_SHA384",
	}
	
	// 将AllCiphersNames转换为map以便快速查找
	cipherMap := make(map[string]bool)
	for _, cipher := range AllCiphersNames {
		cipherMap[cipher] = true
	}
	
	// 检查常见密码套件是否存在
	foundCount := 0
	for _, expected := range expectedCiphers {
		if cipherMap[expected] {
			foundCount++
			t.Logf("找到预期的密码套件: %s", expected)
		}
	}
	
	// ZMap支持更多密码套件，至少应该找到一些常见的
	require.Greater(t, foundCount, 0, "应该至少包含一些常见的密码套件")
	t.Logf("在 %d 个预期密码套件中找到了 %d 个", len(expectedCiphers), foundCount)
}

// TestTlsVersionsContent 测试TLS版本的内容
// 验证包含预期的TLS版本
func TestTlsVersionsContent(t *testing.T) {
	// 常见的TLS版本
	expectedVersions := []string{
		"1.0",
		"1.1", 
		"1.2",
		"1.3",
	}
	
	// 将SupportedTlsVersions转换为map以便快速查找
	versionMap := make(map[string]bool)
	for _, version := range SupportedTlsVersions {
		versionMap[version] = true
	}
	
	// 检查常见TLS版本是否存在
	foundCount := 0
	for _, expected := range expectedVersions {
		if versionMap[expected] {
			foundCount++
			t.Logf("找到预期的TLS版本: %s", expected)
		}
	}
	
	// 至少应该支持TLS 1.2和1.3
	require.True(t, versionMap["1.2"] || versionMap["1.3"], "应该至少支持TLS 1.2或1.3")
	t.Logf("在 %d 个预期TLS版本中找到了 %d 个", len(expectedVersions), foundCount)
}

// TestGlobalVariablesNotEmpty 测试全局变量非空性
// 确保初始化过程产生了有意义的结果
func TestGlobalVariablesNotEmpty(t *testing.T) {
	// 测试密码套件列表
	require.Greater(t, len(AllCiphersNames), 0, "密码套件列表应该包含至少一个元素")
	require.Greater(t, len(AllCiphersNames), 10, "密码套件列表应该包含足够多的元素")
	
	// 测试TLS版本列表
	require.Greater(t, len(SupportedTlsVersions), 0, "TLS版本列表应该包含至少一个元素")
	require.GreaterOrEqual(t, len(SupportedTlsVersions), 2, "TLS版本列表应该包含至少2个版本")
	
	t.Logf("初始化结果统计 - 密码套件: %d个, TLS版本: %d个", 
		len(AllCiphersNames), len(SupportedTlsVersions))
}

// TestCipherNamesFormat 测试密码套件名称格式的一致性
// 验证所有密码套件名称都符合预期的格式规范
func TestCipherNamesFormat(t *testing.T) {
	invalidCiphers := []string{}
	
	for _, cipher := range AllCiphersNames {
		// 检查基本格式要求
		if len(cipher) < 4 {
			invalidCiphers = append(invalidCiphers, cipher)
			continue
		}
		
		// 检查前缀（ZMap支持SSL_和TLS_前缀）
		if cipher[:4] != "TLS_" && cipher[:4] != "SSL_" {
			invalidCiphers = append(invalidCiphers, cipher)
			continue
		}
		
		// 检查是否包含非法字符（密码套件名称应该只包含字母、数字和下划线）
		for _, char := range cipher {
			if !((char >= 'A' && char <= 'Z') || 
				 (char >= 'a' && char <= 'z') || 
				 (char >= '0' && char <= '9') || 
				 char == '_') {
				invalidCiphers = append(invalidCiphers, cipher)
				break
			}
		}
	}
	
	require.Empty(t, invalidCiphers, "发现格式不正确的密码套件: %v", invalidCiphers)
	t.Logf("所有 %d 个密码套件的格式都正确", len(AllCiphersNames))
}

// TestTlsVersionsFormat 测试TLS版本格式的一致性
// 验证所有TLS版本都符合预期的格式规范
func TestTlsVersionsFormat(t *testing.T) {
	invalidVersions := []string{}
	
	for _, version := range SupportedTlsVersions {
		// TLS版本应该是"主版本.次版本"的格式，如"1.2", "1.3"
		// 或者是特殊版本如"3.0"（SSL 3.0）
		if !isValidTlsVersionFormat(version) {
			invalidVersions = append(invalidVersions, version)
		}
	}
	
	require.Empty(t, invalidVersions, "发现格式不正确的TLS版本: %v", invalidVersions)
	t.Logf("所有 %d 个TLS版本的格式都正确", len(SupportedTlsVersions))
}

// isValidTlsVersionFormat 检查TLS版本格式是否有效
// 辅助函数，用于验证TLS版本字符串格式
func isValidTlsVersionFormat(version string) bool {
	// ZMap支持的版本格式：ssl30, tls10, tls11, tls12
	validVersions := map[string]bool{
		"ssl30": true,
		"tls10": true,
		"tls11": true,
		"tls12": true,
	}

	return validVersions[version]
}

// TestToZTLSCiphers 测试密码套件名称转换功能
// 验证toZTLSCiphers函数的正确性
func TestToZTLSCiphers(t *testing.T) {
	// 测试有效的密码套件转换
	t.Run("有效密码套件转换", func(t *testing.T) {
		validCiphers := []string{
			"TLS_RSA_WITH_AES_128_CBC_SHA",
			"TLS_RSA_WITH_AES_256_CBC_SHA",
			"TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256",
		}

		result, err := toZTLSCiphers(validCiphers)
		require.NoError(t, err, "转换有效密码套件应该成功")
		require.Len(t, result, len(validCiphers), "转换结果长度应该与输入一致")

		// 验证转换结果都是有效的数值
		for _, cipher := range result {
			require.Greater(t, cipher, uint16(0), "密码套件数值应该大于0")
		}

		t.Logf("成功转换了 %d 个密码套件", len(result))
	})

	// 测试无效的密码套件
	t.Run("无效密码套件处理", func(t *testing.T) {
		invalidCiphers := []string{
			"INVALID_CIPHER_SUITE",
			"TLS_NONEXISTENT_CIPHER",
		}

		result, err := toZTLSCiphers(invalidCiphers)
		require.Error(t, err, "转换无效密码套件应该返回错误")
		require.Nil(t, result, "转换失败时结果应该为nil")
		require.Contains(t, err.Error(), "not supported", "错误信息应该包含'not supported'")
	})

	// 测试空列表
	t.Run("空密码套件列表", func(t *testing.T) {
		result, err := toZTLSCiphers([]string{})
		require.NoError(t, err, "转换空列表应该成功")
		require.Empty(t, result, "空列表转换结果应该为空")
	})

	// 测试混合有效和无效密码套件
	t.Run("混合有效无效密码套件", func(t *testing.T) {
		mixedCiphers := []string{
			"TLS_RSA_WITH_AES_128_CBC_SHA", // 有效
			"INVALID_CIPHER",               // 无效
		}

		result, err := toZTLSCiphers(mixedCiphers)
		require.Error(t, err, "包含无效密码套件应该返回错误")
		require.Nil(t, result, "转换失败时结果应该为nil")
	})
}

// TestConvertCertificateToResponse 测试证书转换功能
// 验证ConvertCertificateToResponse函数的正确性
func TestConvertCertificateToResponse(t *testing.T) {
	// 测试nil证书处理
	t.Run("nil证书处理", func(t *testing.T) {
		result := ConvertCertificateToResponse(nil, "example.com", nil)
		require.Nil(t, result, "nil证书应该返回nil")
	})

	// 由于需要真实的x509.Certificate对象，这里创建一个模拟测试
	// 在实际环境中，可以使用测试证书文件
	t.Run("证书转换基本功能", func(t *testing.T) {
		// 这个测试需要真实的证书数据，暂时跳过
		// 在实际使用中，可以加载测试证书文件进行完整测试
		t.Skip("需要真实证书数据进行测试")
	})
}

// TestParseSimpleTLSCertificate 测试简单TLS证书解析功能
// 验证ParseSimpleTLSCertificate函数的正确性
func TestParseSimpleTLSCertificate(t *testing.T) {
	// 由于需要真实的tls.SimpleCertificate对象，这里创建一个基本测试
	t.Run("证书解析基本功能", func(t *testing.T) {
		// 这个测试需要真实的证书数据，暂时跳过
		// 在实际使用中，可以使用测试证书数据进行完整测试
		t.Skip("需要真实证书数据进行测试")
	})
}

// TestGlobalVariablesInitialization 测试全局变量初始化
// 验证包初始化时全局变量的正确设置
func TestGlobalVariablesInitialization(t *testing.T) {
	t.Run("AllCiphers初始化验证", func(t *testing.T) {
		require.NotEmpty(t, AllCiphers, "AllCiphers不应该为空")
		require.Greater(t, len(AllCiphers), 100, "AllCiphers应该包含大量密码套件")

		// 验证所有密码套件数值都大于0
		for i, cipher := range AllCiphers {
			require.Greater(t, cipher, uint16(0), "密码套件[%d]数值应该大于0", i)
		}

		t.Logf("AllCiphers包含 %d 个密码套件", len(AllCiphers))
	})

	t.Run("AllCiphersNames初始化验证", func(t *testing.T) {
		require.NotEmpty(t, AllCiphersNames, "AllCiphersNames不应该为空")
		require.Equal(t, len(AllCiphers), len(AllCiphersNames), "密码套件数量和名称数量应该一致")

		// 验证所有密码套件名称都不为空
		for i, name := range AllCiphersNames {
			require.NotEmpty(t, name, "密码套件名称[%d]不应该为空", i)
			require.Contains(t, name, "TLS_", "密码套件名称[%d]应该包含'TLS_'前缀", i)
		}

		t.Logf("AllCiphersNames包含 %d 个密码套件名称", len(AllCiphersNames))
	})

	t.Run("SupportedTlsVersions初始化验证", func(t *testing.T) {
		require.NotEmpty(t, SupportedTlsVersions, "SupportedTlsVersions不应该为空")

		// 验证包含预期的TLS版本
		expectedVersions := []string{"ssl30", "tls10", "tls11", "tls12"}
		for _, expectedVersion := range expectedVersions {
			require.Contains(t, SupportedTlsVersions, expectedVersion, "应该包含版本: %s", expectedVersion)
		}

		// 验证所有版本格式都有效
		for _, version := range SupportedTlsVersions {
			require.True(t, isValidTlsVersionFormat(version), "版本格式应该有效: %s", version)
		}

		t.Logf("SupportedTlsVersions包含版本: %v", SupportedTlsVersions)
	})
}

// TestZTLSCiphersMapping 测试密码套件映射表
// 验证ztlsCiphers映射表的完整性和正确性
func TestZTLSCiphersMapping(t *testing.T) {
	t.Run("密码套件映射表完整性", func(t *testing.T) {
		require.NotEmpty(t, ztlsCiphers, "ztlsCiphers映射表不应该为空")
		require.Greater(t, len(ztlsCiphers), 100, "应该包含大量密码套件")

		// 验证映射表中的每个条目
		for name, value := range ztlsCiphers {
			require.NotEmpty(t, name, "密码套件名称不应该为空")
			require.Greater(t, value, uint16(0), "密码套件值应该大于0")
			require.Contains(t, name, "TLS_", "密码套件名称应该包含'TLS_'前缀")
		}

		t.Logf("ztlsCiphers映射表包含 %d 个条目", len(ztlsCiphers))
	})

	t.Run("常见密码套件存在性验证", func(t *testing.T) {
		// 验证一些常见的密码套件是否存在
		commonCiphers := []string{
			"TLS_RSA_WITH_AES_128_CBC_SHA",
			"TLS_RSA_WITH_AES_256_CBC_SHA",
			"TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256",
			"TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384",
			"TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256",
		}

		for _, cipher := range commonCiphers {
			value, exists := ztlsCiphers[cipher]
			require.True(t, exists, "常见密码套件应该存在: %s", cipher)
			require.Greater(t, value, uint16(0), "密码套件值应该大于0: %s", cipher)
		}
	})

	t.Run("密码套件名称唯一性验证", func(t *testing.T) {
		// 验证没有重复的密码套件名称
		nameSet := make(map[string]bool)
		for name := range ztlsCiphers {
			require.False(t, nameSet[name], "密码套件名称不应该重复: %s", name)
			nameSet[name] = true
		}
	})

	t.Run("密码套件值唯一性验证", func(t *testing.T) {
		// 验证没有重复的密码套件值
		valueSet := make(map[uint16]bool)
		duplicateValues := make(map[uint16][]string)

		for name, value := range ztlsCiphers {
			if valueSet[value] {
				duplicateValues[value] = append(duplicateValues[value], name)
			} else {
				valueSet[value] = true
				duplicateValues[value] = []string{name}
			}
		}

		// 检查是否有重复值（某些情况下可能是合理的）
		for value, names := range duplicateValues {
			if len(names) > 1 {
				t.Logf("密码套件值 %d 被多个名称使用: %v", value, names)
			}
		}
	})
}

// TestErrorHandling 测试错误处理
// 验证各种错误情况的处理
func TestErrorHandling(t *testing.T) {
	t.Run("toZTLSCiphers错误标签验证", func(t *testing.T) {
		_, err := toZTLSCiphers([]string{"INVALID_CIPHER"})
		require.Error(t, err, "应该返回错误")

		// 验证错误包含正确的标签和信息
		require.Contains(t, err.Error(), "ztls", "错误应该包含'ztls'标签")
		require.Contains(t, err.Error(), "not supported", "错误应该包含'not supported'信息")
		require.Contains(t, err.Error(), "INVALID_CIPHER", "错误应该包含具体的密码套件名称")
	})
}

// TestBoundaryConditions 测试边界条件
// 验证各种边界情况的处理
func TestBoundaryConditions(t *testing.T) {
	t.Run("大量密码套件转换", func(t *testing.T) {
		// 使用所有可用的密码套件进行转换测试
		result, err := toZTLSCiphers(AllCiphersNames)
		require.NoError(t, err, "转换所有密码套件应该成功")
		require.Equal(t, len(AllCiphersNames), len(result), "转换结果数量应该一致")

		// 验证转换结果与AllCiphers一致（顺序可能不同）
		resultSet := make(map[uint16]bool)
		for _, cipher := range result {
			resultSet[cipher] = true
		}

		for _, expectedCipher := range AllCiphers {
			require.True(t, resultSet[expectedCipher], "转换结果应该包含所有预期的密码套件")
		}

		t.Logf("成功转换了所有 %d 个密码套件", len(result))
	})

	t.Run("重复密码套件处理", func(t *testing.T) {
		// 测试包含重复密码套件的列表
		duplicateCiphers := []string{
			"TLS_RSA_WITH_AES_128_CBC_SHA",
			"TLS_RSA_WITH_AES_256_CBC_SHA",
			"TLS_RSA_WITH_AES_128_CBC_SHA", // 重复
		}

		result, err := toZTLSCiphers(duplicateCiphers)
		require.NoError(t, err, "包含重复密码套件应该成功")
		require.Len(t, result, len(duplicateCiphers), "结果应该包含所有输入（包括重复）")

		// 验证重复的密码套件确实被包含
		require.Equal(t, result[0], result[2], "重复的密码套件应该有相同的值")
	})

	t.Run("极长密码套件名称", func(t *testing.T) {
		// 测试极长的无效密码套件名称
		longInvalidName := strings.Repeat("VERY_LONG_INVALID_CIPHER_NAME_", 10)

		result, err := toZTLSCiphers([]string{longInvalidName})
		require.Error(t, err, "极长的无效密码套件名称应该返回错误")
		require.Nil(t, result, "转换失败时结果应该为nil")
		require.Contains(t, err.Error(), "not supported", "错误信息应该正确")
	})
}

// TestPerformance 性能测试
// 验证函数在大量数据下的性能表现
func TestPerformance(t *testing.T) {
	// 只在需要时运行性能测试
	if testing.Short() {
		t.Skip("跳过性能测试（使用 -short 标志）")
	}

	t.Run("toZTLSCiphers性能测试", func(t *testing.T) {
		// 准备测试数据：选择一些常见的密码套件
		testCiphers := []string{
			"TLS_RSA_WITH_AES_128_CBC_SHA",
			"TLS_RSA_WITH_AES_256_CBC_SHA",
			"TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256",
			"TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384",
			"TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256",
		}

		// 性能基准测试
		start := time.Now()
		iterations := 1000

		for i := 0; i < iterations; i++ {
			result, err := toZTLSCiphers(testCiphers)
			require.NoError(t, err, "性能测试中不应该有错误")
			require.Len(t, result, len(testCiphers), "结果长度应该正确")
		}

		duration := time.Since(start)
		avgDuration := duration / time.Duration(iterations)

		t.Logf("toZTLSCiphers性能测试: %d次迭代，总耗时: %v，平均耗时: %v",
			iterations, duration, avgDuration)

		// 性能要求：平均每次转换应该在1毫秒以内
		require.Less(t, avgDuration, time.Millisecond, "平均转换时间应该小于1毫秒")
	})

	t.Run("全局变量访问性能", func(t *testing.T) {
		// 测试访问全局变量的性能
		start := time.Now()
		iterations := 10000

		for i := 0; i < iterations; i++ {
			// 访问全局变量
			_ = len(AllCiphers)
			_ = len(AllCiphersNames)
			_ = len(SupportedTlsVersions)
		}

		duration := time.Since(start)
		avgDuration := duration / time.Duration(iterations)

		t.Logf("全局变量访问性能测试: %d次迭代，总耗时: %v，平均耗时: %v",
			iterations, duration, avgDuration)

		// 全局变量访问应该非常快
		require.Less(t, avgDuration, time.Microsecond, "全局变量访问应该非常快")
	})
}

// TestConcurrency 并发测试
// 验证函数在并发环境下的安全性
func TestConcurrency(t *testing.T) {
	t.Run("toZTLSCiphers并发安全性", func(t *testing.T) {
		testCiphers := []string{
			"TLS_RSA_WITH_AES_128_CBC_SHA",
			"TLS_RSA_WITH_AES_256_CBC_SHA",
		}

		const numGoroutines = 100
		const numIterations = 10

		var wg sync.WaitGroup
		errors := make(chan error, numGoroutines*numIterations)

		// 启动多个goroutine并发执行转换
		for i := 0; i < numGoroutines; i++ {
			wg.Add(1)
			go func() {
				defer wg.Done()
				for j := 0; j < numIterations; j++ {
					result, err := toZTLSCiphers(testCiphers)
					if err != nil {
						errors <- err
						return
					}
					if len(result) != len(testCiphers) {
						errors <- fmt.Errorf("结果长度不正确: 期望 %d，实际 %d", len(testCiphers), len(result))
						return
					}
				}
			}()
		}

		wg.Wait()
		close(errors)

		// 检查是否有错误
		for err := range errors {
			t.Errorf("并发测试中发生错误: %v", err)
		}

		t.Logf("并发测试完成: %d个goroutine，每个执行%d次迭代", numGoroutines, numIterations)
	})

	t.Run("全局变量并发访问", func(t *testing.T) {
		const numGoroutines = 50
		const numIterations = 100

		var wg sync.WaitGroup

		// 启动多个goroutine并发访问全局变量
		for i := 0; i < numGoroutines; i++ {
			wg.Add(1)
			go func() {
				defer wg.Done()
				for j := 0; j < numIterations; j++ {
					// 并发访问全局变量
					_ = AllCiphers
					_ = AllCiphersNames
					_ = SupportedTlsVersions
				}
			}()
		}

		wg.Wait()
		t.Logf("全局变量并发访问测试完成: %d个goroutine，每个执行%d次迭代", numGoroutines, numIterations)
	})
}
