//
// Author: chenjb
// Version: V1.0
// Date: 2025-06-26
// FilePath: /yaml_scan/pkg/tlsx/tlsx/clients/ciphers_test.go
// Description: clients包ciphers.go的单元测试

package clients

import (
	"testing"

	"github.com/stretchr/testify/require"
)

// TestCipherSecLevel 测试密码套件安全级别常量
// 验证安全级别枚举的定义和值
func TestCipherSecLevel(t *testing.T) {
	// 验证安全级别的值
	require.Equal(t, CipherSecLevel(0), All, "All级别应该是0")
	require.Equal(t, CipherSecLevel(1), Weak, "Weak级别应该是1")
	require.Equal(t, CipherSecLevel(2), Insecure, "Insecure级别应该是2")
	require.Equal(t, CipherSecLevel(3), Secure, "Secure级别应该是3")
	require.Equal(t, CipherSecLevel(4), Unknown, "Unknown级别应该是4")

	// 验证安全级别的类型
	var level CipherSecLevel
	level = All
	require.Equal(t, All, level, "应该能够赋值All级别")
	
	level = Weak
	require.Equal(t, Weak, level, "应该能够赋值Weak级别")
	
	level = Insecure
	require.Equal(t, Insecure, level, "应该能够赋值Insecure级别")
	
	level = Secure
	require.Equal(t, Secure, level, "应该能够赋值Secure级别")
	
	level = Unknown
	require.Equal(t, Unknown, level, "应该能够赋值Unknown级别")
}

// TestGetCiphersWithLevel 测试根据安全级别筛选密码套件功能
// 验证不同安全级别的密码套件筛选逻辑
func TestGetCiphersWithLevel(t *testing.T) {
	// 创建测试用的密码套件列表
	testCiphers := []string{
		"TLS_RSA_WITH_AES_256_GCM_SHA384",     // 通常是安全的
		"TLS_RSA_WITH_RC4_128_SHA",            // 通常是不安全的
		"TLS_RSA_WITH_AES_128_CBC_SHA",        // 通常是弱的
		"TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384", // 通常是安全的
		"UNKNOWN_CIPHER_SUITE",                // 未知的
	}

	tests := []struct {
		name        string            // 测试用例名称
		cipherList  []string          // 输入的密码套件列表
		secLevels   []CipherSecLevel  // 要筛选的安全级别
		description string            // 测试描述
	}{
		{
			name:        "筛选所有级别",
			cipherList:  testCiphers,
			secLevels:   []CipherSecLevel{All},
			description: "All级别应该返回所有密码套件",
		},
		{
			name:        "筛选安全级别",
			cipherList:  testCiphers,
			secLevels:   []CipherSecLevel{Secure},
			description: "Secure级别应该只返回安全的密码套件",
		},
		{
			name:        "筛选弱级别",
			cipherList:  testCiphers,
			secLevels:   []CipherSecLevel{Weak},
			description: "Weak级别应该只返回弱的密码套件",
		},
		{
			name:        "筛选不安全级别",
			cipherList:  testCiphers,
			secLevels:   []CipherSecLevel{Insecure},
			description: "Insecure级别应该只返回不安全的密码套件",
		},
		{
			name:        "筛选多个级别",
			cipherList:  testCiphers,
			secLevels:   []CipherSecLevel{Secure, Weak},
			description: "多个级别应该返回所有匹配级别的密码套件",
		},
		{
			name:        "无安全级别参数",
			cipherList:  testCiphers,
			secLevels:   []CipherSecLevel{},
			description: "无安全级别参数应该返回所有密码套件",
		},
		{
			name:        "空密码套件列表",
			cipherList:  []string{},
			secLevels:   []CipherSecLevel{Secure},
			description: "空列表应该返回空结果",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := GetCiphersWithLevel(tt.cipherList, tt.secLevels...)
			
			// 基本验证
			require.NotNil(t, result, "结果不应该为nil")
			
			if len(tt.secLevels) == 0 || (len(tt.secLevels) == 1 && tt.secLevels[0] == All) {
				// 无级别限制或All级别应该返回所有输入
				require.Equal(t, tt.cipherList, result, tt.description)
			} else if len(tt.cipherList) == 0 {
				// 空输入应该返回空结果
				require.Empty(t, result, tt.description)
			} else {
				// 其他情况验证结果不超过输入长度
				require.LessOrEqual(t, len(result), len(tt.cipherList), "结果长度不应该超过输入长度")
			}
			
			t.Logf("筛选结果: %d个密码套件 - %v", len(result), result)
		})
	}
}

// TestGetCipherLevel 测试密码套件安全级别评估功能
// 验证不同密码套件的安全级别识别
func TestGetCipherLevel(t *testing.T) {
	tests := []struct {
		name        string          // 测试用例名称
		cipherName  string          // 密码套件名称
		expected    CipherSecLevel  // 期望的安全级别
		description string          // 测试描述
	}{
		{
			name:        "空密码套件名称",
			cipherName:  "",
			expected:    Unknown,
			description: "空名称应该返回Unknown级别",
		},
		{
			name:        "未知密码套件",
			cipherName:  "UNKNOWN_CIPHER_SUITE_12345",
			expected:    Unknown,
			description: "未知密码套件应该返回Unknown级别",
		},
		{
			name:        "大小写不敏感测试",
			cipherName:  "tls_rsa_with_aes_256_gcm_sha384",
			expected:    Unknown, // 实际结果取决于assets中的定义
			description: "应该支持大小写不敏感的匹配",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := GetCipherLevel(tt.cipherName)
			
			// 验证返回值是有效的安全级别
			require.True(t, result >= All && result <= Unknown, "返回值应该是有效的安全级别")
			
			// 对于空名称和明确的未知密码套件，应该返回Unknown
			if tt.cipherName == "" || tt.cipherName == "UNKNOWN_CIPHER_SUITE_12345" {
				require.Equal(t, Unknown, result, tt.description)
			}
			
			t.Logf("密码套件 '%s' 的安全级别: %d", tt.cipherName, result)
		})
	}
}

// TestGetCipherLevelWithKnownCiphers 测试已知密码套件的安全级别
// 使用一些常见的密码套件进行测试
func TestGetCipherLevelWithKnownCiphers(t *testing.T) {
	// 这些是一些常见的密码套件，实际的安全级别取决于assets包中的定义
	knownCiphers := []string{
		"TLS_RSA_WITH_AES_128_CBC_SHA",
		"TLS_RSA_WITH_AES_256_CBC_SHA",
		"TLS_RSA_WITH_AES_128_GCM_SHA256",
		"TLS_RSA_WITH_AES_256_GCM_SHA384",
		"TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256",
		"TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384",
		"TLS_RSA_WITH_RC4_128_SHA",
		"TLS_RSA_WITH_3DES_EDE_CBC_SHA",
	}

	for _, cipher := range knownCiphers {
		t.Run(cipher, func(t *testing.T) {
			level := GetCipherLevel(cipher)
			
			// 验证返回值是有效的安全级别
			require.True(t, level >= All && level <= Unknown, "返回值应该是有效的安全级别")
			
			t.Logf("密码套件 '%s' 的安全级别: %d", cipher, level)
		})
	}
}

// TestGetCiphersWithLevelEdgeCases 测试边界情况
// 验证各种边界条件下的行为
func TestGetCiphersWithLevelEdgeCases(t *testing.T) {
	tests := []struct {
		name        string            // 测试用例名称
		cipherList  []string          // 输入的密码套件列表
		secLevels   []CipherSecLevel  // 要筛选的安全级别
		description string            // 测试描述
	}{
		{
			name:        "nil密码套件列表",
			cipherList:  nil,
			secLevels:   []CipherSecLevel{Secure},
			description: "nil列表应该返回空结果",
		},
		{
			name:        "重复的安全级别",
			cipherList:  []string{"TLS_RSA_WITH_AES_256_GCM_SHA384"},
			secLevels:   []CipherSecLevel{Secure, Secure, Secure},
			description: "重复的安全级别应该正常处理",
		},
		{
			name:        "所有安全级别",
			cipherList:  []string{"TLS_RSA_WITH_AES_256_GCM_SHA384"},
			secLevels:   []CipherSecLevel{All, Secure, Weak, Insecure, Unknown},
			description: "包含All级别时应该返回所有密码套件",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := GetCiphersWithLevel(tt.cipherList, tt.secLevels...)
			
			// 基本验证
			require.NotNil(t, result, "结果不应该为nil")
			
			// 对于nil输入，应该返回空结果
			if tt.cipherList == nil {
				require.Empty(t, result, tt.description)
			}
			
			// 如果包含All级别，应该返回所有输入
			containsAll := false
			for _, level := range tt.secLevels {
				if level == All {
					containsAll = true
					break
				}
			}
			if containsAll {
				require.Equal(t, tt.cipherList, result, "包含All级别时应该返回所有密码套件")
			}
			
			t.Logf("边界情况测试结果: %d个密码套件", len(result))
		})
	}
}

// TestCipherSecLevelString 测试安全级别的字符串表示
// 虽然没有String()方法，但可以测试级别的数值表示
func TestCipherSecLevelString(t *testing.T) {
	levels := []struct {
		level CipherSecLevel
		value uint
		name  string
	}{
		{All, 0, "All"},
		{Weak, 1, "Weak"},
		{Insecure, 2, "Insecure"},
		{Secure, 3, "Secure"},
		{Unknown, 4, "Unknown"},
	}

	for _, l := range levels {
		t.Run(l.name, func(t *testing.T) {
			require.Equal(t, l.value, uint(l.level), "安全级别的数值应该匹配")
		})
	}
}
