//
// Author: chenjb
// Version: V1.0
// Date: 2025-06-23 20:21:20
// FilePath: /yaml_scan/pkg/tlsx/tlsx/tls/tls.go
// Description: 标准Go TLS库实现的TLS客户端

// Package tls 提供基于Go标准库crypto/tls的TLS客户端实现
// 使用Go原生的TLS实现，提供稳定可靠的TLS连接和证书分析功能
// 支持TLS 1.0到1.3的所有版本，以及完整的密码套件枚举
package tls

import (
	"context"                           // 上下文控制
	"crypto/tls"                        // Go标准TLS库
	"crypto/x509"                       // X.509证书处理
	"errors"                            // 错误处理
	"net"                               // 网络操作
	"os"                                // 操作系统接口
	"time"                              // 时间处理
	"yaml_scan/pkg/fastdialer"          // 快速拨号器
	"yaml_scan/pkg/gologger"            // 日志记录器
	"yaml_scan/pkg/tlsx/output/stats"   // 统计信息收集
	"yaml_scan/pkg/tlsx/tlsx/clients"   // TLS客户端接口
	"yaml_scan/utils/conn"              // 连接池工具
	errorutil "yaml_scan/utils/errors"  // 错误处理工具
	iputil "yaml_scan/utils/ip"         // IP地址工具
	stringsutil "yaml_scan/utils/strings" // 字符串工具

	"github.com/rs/xid"                 // 唯一ID生成器
)

// Client 是基于Go标准库crypto/tls的TLS客户端实现
// 实现了clients.Implementation接口，提供稳定的TLS连接功能
type Client struct {
	dialer    *fastdialer.Dialer // 用于创建网络连接的快速拨号器
	tlsConfig *tls.Config        // TLS连接配置，包含证书、密码套件等设置
	options   *clients.Options   // TLS扫描选项，包含超时、重试等参数
}

// versionToTLSVersionString TLS版本号到版本字符串的映射表
// 用于将Go TLS库的版本常量转换为可读的版本字符串
var versionToTLSVersionString = map[uint16]string{
	tls.VersionTLS10: "tls10", // TLS 1.0版本
	tls.VersionTLS11: "tls11", // TLS 1.1版本
	tls.VersionTLS12: "tls12", // TLS 1.2版本
	tls.VersionTLS13: "tls13", // TLS 1.3版本
}

// versionStringToTLSVersion TLS版本字符串到版本号的映射表
// 用于将版本字符串转换为Go TLS库的版本常量
var versionStringToTLSVersion = map[string]uint16{
	"tls10": tls.VersionTLS10, // TLS 1.0版本
	"tls11": tls.VersionTLS11, // TLS 1.1
	"tls12": tls.VersionTLS12, // TLS 1.2
	"tls13": tls.VersionTLS13, // TLS 1.3
}

// New 创建一个新的基于Go标准库crypto/tls的TLS客户端
// 根据提供的选项配置TLS连接参数，包括版本、密码套件、证书验证等
//
// 参数:
//   - options: TLS连接配置选项，包含以下重要设置：
//     * Fastdialer: 网络连接拨号器
//     * VerifyServerCertificate: 是否验证服务器证书
//     * Ciphers: 自定义密码套件列表
//     * CACertificate: 自定义CA证书文件路径
//     * MinVersion/MaxVersion: TLS版本范围
//
// 返回值:
//   - *Client: 配置完成的TLS客户端实例
//   - error: 配置过程中的错误，成功时为nil
//
// 配置特点:
//   - 默认支持TLS 1.0到1.3的所有版本
//   - 可选择性跳过服务器证书验证（用于测试）
//   - 支持自定义密码套件和CA证书
//   - 默认启用所有可用的密码套件以提高兼容性
func New(options *clients.Options) (*Client, error) {
	// 创建基本的TLS客户端结构
	c := &Client{
		dialer: options.Fastdialer, // 使用提供的快速拨号器
		tlsConfig: &tls.Config{
			MinVersion:         tls.VersionTLS10,                 // 默认最小支持TLS 1.0（兼容性考虑）
			MaxVersion:         tls.VersionTLS13,                 // 默认最大支持TLS 1.3（最新版本）
			InsecureSkipVerify: !options.VerifyServerCertificate, // 根据选项决定是否跳过证书验证
		},
		options: options, // 保存原始配置选项
	}

	// 配置密码套件
	if len(options.Ciphers) > 0 {
		// 用户指定了自定义密码套件，进行转换和验证
		if customCiphers, err := toTLSCiphers(options.Ciphers); err != nil {
			return nil, errorutil.NewWithTag("ctls", "could not get tls ciphers").Wrap(err)
		} else {
			c.tlsConfig.CipherSuites = customCiphers // 使用自定义密码套件
		}
	} else {
		// 未指定密码套件，使用所有支持的密码套件以提高兼容性
		// 注意：Go标准库默认只启用安全的密码套件子集
		// 这里启用所有密码套件是为了进行全面的TLS扫描
		c.tlsConfig.CipherSuites = AllCiphers
	}

	// 配置自定义CA证书（如果提供）
	if options.CACertificate != "" {
		// 读取CA证书文件
		caCert, err := os.ReadFile(options.CACertificate)
		if err != nil {
			return nil, errorutil.NewWithTag("ctls", "could not read ca certificate").Wrap(err)
		}

		// 创建新的证书池并添加自定义CA证书
		certPool := x509.NewCertPool()
		if !certPool.AppendCertsFromPEM(caCert) {
			// CA证书解析失败，记录错误但不中断执行
			gologger.Error().Msgf("Could not append parsed ca-cert to config!")
		}
		c.tlsConfig.RootCAs = certPool // 设置自定义根CA证书池
	}

	// 配置TLS版本范围
	// 如果用户指定了最小TLS版本，覆盖默认设置
	if options.MinVersion != "" {
		version, ok := versionStringToTLSVersion[options.MinVersion]
		if !ok {
			return nil, errorutil.NewWithTag("ctls", "invalid min version specified: %s", options.MinVersion)
		} else {
			c.tlsConfig.MinVersion = version // 设置最小支持的TLS版本
		}
	}

	// 如果用户指定了最大TLS版本，覆盖默认设置
	if options.MaxVersion != "" {
		version, ok := versionStringToTLSVersion[options.MaxVersion]
		if !ok {
			return nil, errorutil.NewWithTag("ctls", "invalid max version specified: %s", options.MaxVersion)
		} else {
			c.tlsConfig.MaxVersion = version // 设置最大支持的TLS版本
		}
	}

	return c, nil
}

// ConnectWithOptions 使用指定选项连接到目标主机并获取TLS响应数据
// 执行完整的TLS握手过程，收集证书信息和连接详情
//
// 参数:
//   - hostname: 目标主机名或域名
//   - ip: 目标IP地址
//   - port: 目标端口号（字符串格式）
//   - options: 连接选项，包含SNI、TLS版本、枚举模式等配置
//
// 返回值:
//   - *clients.Response: TLS连接响应，包含证书信息、版本、密码套件等
//   - error: 连接过程中的错误，成功时为nil
//
// 连接流程:
//   - 根据选项生成TLS配置
//   - 建立TCP连接
//   - 执行TLS握手
//   - 收集证书链和连接信息
//   - 检查客户端证书要求
func (c *Client) ConnectWithOptions(hostname, ip, port string, options clients.ConnectOptions) (*clients.Response, error) {
	// 根据连接选项生成特定的TLS配置
	config, err := c.getConfig(hostname, ip, port, options)
	if err != nil {
		return nil, errorutil.NewWithErr(err).Msgf("failed to connect got cfg error")
	}

	// 创建上下文控制，如果设置了超时则添加超时控制
	ctx := context.Background()
	if c.options.Timeout != 0 {
		var cancel context.CancelFunc
		// 设置连接超时时间
		ctx, cancel = context.WithTimeout(ctx, time.Duration(c.options.Timeout)*time.Second)
		defer cancel() // 确保在函数结束时取消上下文
	}

	// 建立底层TCP网络连接
	rawConn, err := clients.GetConn(ctx, hostname, ip, port, c.options)
	if err != nil {
		return nil, errorutil.NewWithErr(err).Msgf("failed to setup connection").WithTag("ctls")
	}
	// defer rawConn.Close() //internally done by conn.Close() so just a placeholder

	// 是否需要客户端请求证书
	var clientCertRequired bool

	// 创建TLS客户端连接
	conn := tls.Client(rawConn, config)
	// 执行TLS握手
	err = conn.HandshakeContext(ctx)
	if err != nil {
		if clients.IsClientCertRequiredError(err) {
			clientCertRequired = true
		} else {
			rawConn.Close()
			return nil, errorutil.NewWithTag("ctls", "could not do handshake").Wrap(err)
		}
	}
	defer conn.Close()

	// 获取连接状态
	connectionState := conn.ConnectionState()
	if len(connectionState.PeerCertificates) == 0 {
		return nil, errorutil.New("no certificates returned by server")
	}

	// 获取TLS版本和密码套件
	tlsVersion := versionToTLSVersionString[connectionState.Version]
	tlsCipher := tls.CipherSuiteName(connectionState.CipherSuite)

	// 提取证书信息
	leafCertificate := connectionState.PeerCertificates[0]
	certificateChain := connectionState.PeerCertificates[1:]

	// 获取远程IP地址
	resolvedIP, _, err := net.SplitHostPort(rawConn.RemoteAddr().String())
	if err != nil {
		return nil, err
	}

	// 创建响应
	now := time.Now()
	response := &clients.Response{
		Timestamp:           &now,
		Host:                hostname,
		IP:                  resolvedIP,
		ProbeStatus:         true,
		Port:                port,
		Version:             tlsVersion,
		Cipher:              tlsCipher,
		TLSConnection:       "ctls",
		CertificateResponse: clients.Convertx509toResponse(c.options, hostname, leafCertificate, c.options.Cert),
		ServerName:          config.ServerName,
	}
	// 检查证书链是否受信任
	response.Untrusted = clients.IsUntrustedCA(certificateChain)
	
	// 如果需要，添加证书链信息
	if c.options.TLSChain {
		for _, cert := range certificateChain {
			// 将证书链中的每个证书转换为响应格式并添加到响应中
			// 证书链对于验证服务器证书的真实性和完整性非常重要
			response.Chain = append(response.Chain, clients.Convertx509toResponse(c.options, hostname, cert, c.options.Cert))
		}
	}

	// crypto/tls允许在不提供客户端证书的情况下完成握手，即使需要客户端证书
	// 直到实际使用底层连接时才会返回错误。因此，我们将暂时
	// 跳过为TLS 1.3服务器设置ClientCertRequired，因为在这个阶段我们还不知道
	// 是否需要客户端证书。
	if response.Version != "tls13" {
		// 只为非TLS 1.3连接设置客户端证书要求标志
		// TLS 1.3处理客户端证书的方式与早期版本不同
		response.ClientCertRequired = &clientCertRequired
	}
	return response, nil
}

// EnumerateCiphers 枚举目标支持的密码套件
// @receiver c 
// @param hostname string:  主机名
// @param ip string: IP地址
// @param port string: 端口号
// @param options clients.ConnectOptions: 连接选项
// @return []string []string: 支持的密码套件列表
// @return error error:  可能的错误
func (c *Client) EnumerateCiphers(hostname, ip, port string, options clients.ConnectOptions) ([]string, error) {
	// 根据给定的安全级别筛选密码套件
	toEnumerate := clients.GetCiphersWithLevel(AllCiphersNames, options.CipherLevel...)

	// TLS 1.3不支持密码套件枚举
	if options.VersionTLS == "tls13" {
		return nil, errorutil.NewWithTag("ctls", "cipher enum not supported in ctls with tls1.3")
	}

	enumeratedCiphers := []string{}

	// 获取基础配置
	baseCfg, err := c.getConfig(hostname, ip, port, options)
	if err != nil {
		return enumeratedCiphers, errorutil.NewWithErr(err).Msgf("failed to setup cfg")
	}
	gologger.Debug().Label("ctls").Msgf("Starting cipher enumeration with %v ciphers and version %v", len(toEnumerate), options.VersionTLS)

	// 获取网络地址
	var address string
	if iputil.IsIP(ip) && (c.options.ScanAllIPs || len(c.options.IPVersion) > 0) {
		address = net.JoinHostPort(ip, port)
	} else {
		address = net.JoinHostPort(hostname, port)
	}

	// 设置并发数
	threads := c.options.CipherConcurrency
	if len(toEnumerate) < threads {
		threads = len(toEnumerate)
	}

	// 设置连接池
	pool, err := connpool.NewOneTimePool(context.Background(), address, threads)
	if err != nil {
		return enumeratedCiphers, errorutil.NewWithErr(err).Msgf("failed to setup connection pool")
	}
	pool.Dialer = c.dialer
	go func() {
		if err := pool.Run(); err != nil && !errors.Is(err, context.Canceled) {
			gologger.Error().Msgf("tlsx: ctls: failed to run connection pool: %v", err)
		}
	}()
	defer pool.Close()

	// 遍历所有要枚举的密码套件
	for _, v := range toEnumerate {
		// 创建新的基础连接并传递给TLS客户端
		baseConn, err := pool.Acquire(context.Background())
		if err != nil {
			return enumeratedCiphers, errorutil.NewWithErr(err).WithTag("ctls")
		}
		stats.IncrementCryptoTLSConnections()
		baseCfg.CipherSuites = []uint16{tlsCiphers[v]}

		// 创建TLS客户端连接
		conn := tls.Client(baseConn, baseCfg)

			// 尝试握手，如果成功，记录使用的密码套件
		if err := conn.Handshake(); err == nil {
			ciphersuite := conn.ConnectionState().CipherSuite
			enumeratedCiphers = append(enumeratedCiphers, tls.CipherSuiteName(ciphersuite))
		}
		_ = conn.Close() // 内部关闭baseConn
	}
	return enumeratedCiphers, nil
}

// SupportedTLSVersions 返回标准TLS库支持的TLS版本列表
func (c *Client) SupportedTLSVersions() ([]string, error) {
	return SupportedTlsVersions, nil
}

// SupportedTLSCiphers 返回标准TLS库支持的密码套件列表
func (c *Client) SupportedTLSCiphers() ([]string, error) {
	return AllCiphersNames, nil
}

// getConfig 根据主机名、IP、端口和选项获取TLS配置
// @receiver c
// @param hostname string: 主机名
// @param ip string: IP地址
// @param port string: 端口号
// @param options clients.ConnectOptions: 连接选项
// @return *tls.Config *tls.Config: TLS配置
// @return error error: 可能的错误
func (c *Client) getConfig(hostname, ip, port string, options clients.ConnectOptions) (*tls.Config, error) {
	// 在枚举模式下，如果给定的选项不受支持，则返回
	if options.EnumMode == clients.Version && (options.VersionTLS == "" || !stringsutil.EqualFoldAny(options.VersionTLS, SupportedTlsVersions...)) {
		return nil, errorutil.NewWithTag("ctls", "tlsversion `%v` not supported in ctls", options.VersionTLS)
	}
	// 获取基础TLS配置
	config := c.tlsConfig

	// 如果ServerName为空，设置ServerName（SNI）
	if config.ServerName == "" {
		// 创建配置副本
		cfg := config.Clone()
		// 优先使用选项中指定的SNI
		if options.SNI != "" {
			cfg.ServerName = options.SNI
		} else if iputil.IsIP(hostname) && c.options.RandomForEmptyServerName {
			// 如果主机名是IP且设置了随机SNI选项，使用随机SNI
			// 使用随机SNI将返回服务器的默认证书
			cfg.ServerName = xid.New().String()
		} else {
			// 否则使用主机名作为SNI
			cfg.ServerName = hostname
		}

		config = cfg
	}

	// 如果指定了TLS版本，设置最小和最大版本
	if options.VersionTLS != "" {
		version, ok := versionStringToTLSVersion[options.VersionTLS]
		if !ok {
			return nil, errorutil.New("invalid tls version specified: %s", options.VersionTLS).WithTag("ctls")
		}
		config.MinVersion = version
		config.MaxVersion = version
	}	
	
	// 如果指定了密码套件且不是密码套件枚举模式，设置自定义密码套件
	if len(options.Ciphers) > 0 && options.EnumMode != clients.Cipher {
		customCiphers, err := toTLSCiphers(options.Ciphers)
		if err != nil {
			return nil, errorutil.NewWithTag("ctls", "could not get tls ciphers").Wrap(err)
		}
		c.tlsConfig.CipherSuites = customCiphers
	}
	
	// 如果是密码套件枚举模式且TLS版本不受支持，返回错误
	if options.EnumMode == clients.Cipher && !stringsutil.EqualFoldAny(options.VersionTLS, SupportedTlsVersions...) {
		return nil, errorutil.NewWithTag("ctls", "cipher enum with version %v not implemented", options.VersionTLS)
	}
	return config, nil
}
