/**
 * TLS扫描库文档交互功能
 * Author: chenjb
 * Version: V1.0
 * Date: 2025-07-01
 */

(function() {
    'use strict';

    // 文档加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
        initializeFeatures();
    });

    /**
     * 初始化所有功能
     */
    function initializeFeatures() {
        initSmoothScroll();
        initCodeCopy();
        initThemeToggle();
        initSearchFunction();
        initProgressBar();
        initTooltips();
        initAnimations();
    }

    /**
     * 平滑滚动功能
     */
    function initSmoothScroll() {
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function(e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                    
                    // 更新URL但不触发滚动
                    history.pushState(null, null, this.getAttribute('href'));
                }
            });
        });
    }

    /**
     * 代码复制功能
     */
    function initCodeCopy() {
        document.querySelectorAll('.code-block, .code').forEach(block => {
            // 创建复制按钮
            const button = document.createElement('button');
            button.innerHTML = '<i class="fas fa-copy"></i> 复制';
            button.className = 'copy-btn';
            button.style.cssText = `
                position: absolute;
                top: 10px;
                right: 15px;
                background: #10b981;
                color: white;
                border: none;
                padding: 8px 12px;
                border-radius: 6px;
                cursor: pointer;
                font-size: 0.85rem;
                transition: all 0.3s;
                z-index: 10;
            `;

            // 添加悬浮效果
            button.addEventListener('mouseenter', function() {
                this.style.background = '#059669';
                this.style.transform = 'translateY(-2px)';
            });

            button.addEventListener('mouseleave', function() {
                this.style.background = '#10b981';
                this.style.transform = 'translateY(0)';
            });

            // 复制功能
            button.addEventListener('click', function() {
                const code = block.querySelector('code, pre');
                const text = code ? code.textContent : block.textContent;
                
                navigator.clipboard.writeText(text).then(() => {
                    this.innerHTML = '<i class="fas fa-check"></i> 已复制';
                    this.style.background = '#059669';
                    
                    setTimeout(() => {
                        this.innerHTML = '<i class="fas fa-copy"></i> 复制';
                        this.style.background = '#10b981';
                    }, 2000);
                }).catch(() => {
                    // 降级方案
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                    
                    this.innerHTML = '<i class="fas fa-check"></i> 已复制';
                    setTimeout(() => {
                        this.innerHTML = '<i class="fas fa-copy"></i> 复制';
                    }, 2000);
                });
            });

            // 设置相对定位并添加按钮
            block.style.position = 'relative';
            block.appendChild(button);
        });
    }

    /**
     * 主题切换功能
     */
    function initThemeToggle() {
        // 创建主题切换按钮
        const themeToggle = document.createElement('button');
        themeToggle.innerHTML = '<i class="fas fa-moon"></i>';
        themeToggle.className = 'theme-toggle';
        themeToggle.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 1.2rem;
            color: #374151;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s;
            z-index: 1000;
            backdrop-filter: blur(10px);
        `;

        // 检查本地存储的主题设置
        const currentTheme = localStorage.getItem('theme') || 'light';
        if (currentTheme === 'dark') {
            document.body.classList.add('dark-theme');
            themeToggle.innerHTML = '<i class="fas fa-sun"></i>';
        }

        // 主题切换事件
        themeToggle.addEventListener('click', function() {
            document.body.classList.toggle('dark-theme');
            const isDark = document.body.classList.contains('dark-theme');
            
            this.innerHTML = isDark ? '<i class="fas fa-sun"></i>' : '<i class="fas fa-moon"></i>';
            localStorage.setItem('theme', isDark ? 'dark' : 'light');
        });

        document.body.appendChild(themeToggle);
    }

    /**
     * 搜索功能
     */
    function initSearchFunction() {
        // 创建搜索框
        const searchContainer = document.createElement('div');
        searchContainer.className = 'search-container';
        searchContainer.style.cssText = `
            position: fixed;
            top: 80px;
            right: 20px;
            width: 300px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 10px;
            padding: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            transform: translateX(320px);
            transition: transform 0.3s;
            z-index: 999;
        `;

        const searchInput = document.createElement('input');
        searchInput.type = 'text';
        searchInput.placeholder = '搜索文档内容...';
        searchInput.style.cssText = `
            width: 100%;
            padding: 10px;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            font-size: 0.9rem;
            outline: none;
        `;

        const searchResults = document.createElement('div');
        searchResults.className = 'search-results';
        searchResults.style.cssText = `
            max-height: 200px;
            overflow-y: auto;
            margin-top: 10px;
        `;

        searchContainer.appendChild(searchInput);
        searchContainer.appendChild(searchResults);

        // 搜索按钮
        const searchButton = document.createElement('button');
        searchButton.innerHTML = '<i class="fas fa-search"></i>';
        searchButton.style.cssText = `
            position: fixed;
            top: 80px;
            right: 20px;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 1.1rem;
            color: #374151;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s;
            z-index: 1000;
            backdrop-filter: blur(10px);
        `;

        // 搜索功能实现
        let isSearchOpen = false;
        searchButton.addEventListener('click', function() {
            isSearchOpen = !isSearchOpen;
            searchContainer.style.transform = isSearchOpen ? 'translateX(0)' : 'translateX(320px)';
            if (isSearchOpen) {
                searchInput.focus();
            }
        });

        // 搜索逻辑
        searchInput.addEventListener('input', function() {
            const query = this.value.toLowerCase();
            searchResults.innerHTML = '';

            if (query.length < 2) return;

            const sections = document.querySelectorAll('.section');
            sections.forEach(section => {
                const text = section.textContent.toLowerCase();
                if (text.includes(query)) {
                    const title = section.querySelector('h2, h3');
                    if (title) {
                        const result = document.createElement('div');
                        result.style.cssText = `
                            padding: 8px;
                            cursor: pointer;
                            border-radius: 4px;
                            margin: 2px 0;
                            transition: background 0.2s;
                        `;
                        result.textContent = title.textContent;
                        result.addEventListener('click', function() {
                            section.scrollIntoView({ behavior: 'smooth' });
                            searchContainer.style.transform = 'translateX(320px)';
                            isSearchOpen = false;
                        });
                        result.addEventListener('mouseenter', function() {
                            this.style.background = '#f3f4f6';
                        });
                        result.addEventListener('mouseleave', function() {
                            this.style.background = 'transparent';
                        });
                        searchResults.appendChild(result);
                    }
                }
            });
        });

        document.body.appendChild(searchContainer);
        document.body.appendChild(searchButton);
    }

    /**
     * 进度条功能
     */
    function initProgressBar() {
        const progressBar = document.createElement('div');
        progressBar.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 0%;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            z-index: 9999;
            transition: width 0.3s;
        `;

        document.body.appendChild(progressBar);

        window.addEventListener('scroll', function() {
            const scrollTop = window.pageYOffset;
            const docHeight = document.body.scrollHeight - window.innerHeight;
            const scrollPercent = (scrollTop / docHeight) * 100;
            progressBar.style.width = scrollPercent + '%';
        });
    }

    /**
     * 工具提示功能
     */
    function initTooltips() {
        document.querySelectorAll('[data-tooltip]').forEach(element => {
            element.addEventListener('mouseenter', function(e) {
                const tooltip = document.createElement('div');
                tooltip.textContent = this.getAttribute('data-tooltip');
                tooltip.style.cssText = `
                    position: absolute;
                    background: #1f2937;
                    color: white;
                    padding: 8px 12px;
                    border-radius: 6px;
                    font-size: 0.85rem;
                    z-index: 10000;
                    pointer-events: none;
                    white-space: nowrap;
                `;
                
                document.body.appendChild(tooltip);
                
                const rect = this.getBoundingClientRect();
                tooltip.style.left = rect.left + 'px';
                tooltip.style.top = (rect.top - tooltip.offsetHeight - 5) + 'px';
                
                this._tooltip = tooltip;
            });

            element.addEventListener('mouseleave', function() {
                if (this._tooltip) {
                    document.body.removeChild(this._tooltip);
                    this._tooltip = null;
                }
            });
        });
    }

    /**
     * 动画功能
     */
    function initAnimations() {
        // 观察器用于触发动画
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.animation = 'fadeInUp 0.6s ease forwards';
                }
            });
        }, { threshold: 0.1 });

        // 为所有卡片添加动画
        document.querySelectorAll('.section, .card, .feature-card').forEach(el => {
            el.style.opacity = '0';
            observer.observe(el);
        });
    }

})();
