// Author: chenjb
// Version: V1.0
// Date: 2025-06-23 19:55:58
// FilePath: /yaml_scan/pkg/tlsx/tlsx/openssl/common.go
// Description: OpenSSL TLS客户端的通用功能和初始化

// Package openssl 提供基于OpenSSL命令行工具的TLS客户端实现
// 通过调用系统中的OpenSSL二进制文件来执行TLS连接和证书分析
// 支持OpenSSL和LibreSSL两种实现，提供最广泛的TLS兼容性
package openssl

import (
	"context"                           // 上下文控制
	"os"                                // 操作系统接口
	"os/exec"                           // 外部命令执行
	"path/filepath"                     // 文件路径操作
	"runtime"                           // 运行时信息
	"strings"                           // 字符串操作
	"yaml_scan/pkg/gologger"            // 日志记录器
	errorutils "yaml_scan/utils/errors" // 错误处理工具
)

// 预定义的错误类型，用于标识不同类型的OpenSSL操作错误
var (
	ErrParse          = errorutils.NewWithTag("openssl", "failed to parse openssl response")      // OpenSSL响应解析失败
	ErrCertParse      = errorutils.NewWithTag("openssl", "failed to parse server certificate")   // 服务器证书解析失败
	ErrNotImplemented = errorutils.NewWithTag("openssl", "feature not implemented")               // 功能未实现
	ErrNotAvailable   = errorutils.NewWithTag("openssl", "executable not installed or in PATH")  // OpenSSL可执行文件不可用
	ErrNoSession      = errorutils.NewWithTag("openssl", "session not created/found")            // TLS会话未创建或找不到
)

// 全局变量，用于存储OpenSSL的配置和状态信息
var (
	BinaryPath   = ""    // OpenSSL二进制文件的完整路径
	OPENSSL_CONF = ""    // 自定义OpenSSL配置文件路径
	IsLibreSSL   = false // 标识是否使用LibreSSL而非OpenSSL
	PkgTag       = ""    // 包标签，包含OpenSSL/LibreSSL的版本信息，用于错误报告
)

// openSSLConfig 定义了自定义的OpenSSL配置内容
// 某些Linux发行版（如Ubuntu 18/19/20等）的OpenSSL配置文件设置了较高的最小协议版本
// 这会阻止对旧版TLS协议的测试，因此需要临时覆盖配置
// 注意：此配置覆盖仅适用于OpenSSL，不适用于LibreSSL（由于兼容性问题）
var openSSLConfig string = `openssl_conf = default_conf

[ default_conf ]
ssl_conf = ssl_sect

[ssl_sect]
system_default = system_default_sect

[system_default_sect]
MinProtocol = SSLv3
CipherString = DEFAULT:@SECLEVEL=0
`

// init 包初始化函数
// 在包加载时自动执行，负责查找和配置OpenSSL二进制文件
//
// 初始化流程:
//   - 根据操作系统类型查找OpenSSL可执行文件
//   - Windows系统查找"openssl.exe"
//   - 其他系统查找"openssl"
//   - 如果找到可执行文件，进行进一步的设置和配置
//   - 设置过程中的错误会记录到调试日志中
func init() {
	// 根据操作系统类型查找OpenSSL二进制文件
	if runtime.GOOS == "windows" {
		// Windows系统查找openssl.exe
		BinaryPath, _ = exec.LookPath("openssl.exe")
	} else {
		// Unix-like系统查找openssl
		BinaryPath, _ = exec.LookPath("openssl")
	}

	// 检查是否成功找到OpenSSL可执行文件
	if BinaryPath == "" {
		// 未找到OpenSSL，记录调试信息并跳过后续设置
		gologger.Debug().Label("openssl").Msg("openssl binary not found skipping")
		return
	}

	// 执行OpenSSL的详细设置和配置
	if err := openSSLSetup(); err != nil {
		// 设置过程中出现错误，记录到调试日志
		gologger.Debug().Label("openssl").Msg(err.Error())
	}
}

// openSSLSetup 获取OpenSSL版本信息并进行必要的配置设置
// 识别OpenSSL或LibreSSL的版本，并为OpenSSL创建自定义配置文件
//
// 返回值:
//   - errorutils.Error: 设置过程中的错误，成功时为nil
//
// 设置流程:
//   - 执行"openssl version"命令获取版本信息
//   - 解析输出以识别是OpenSSL还是LibreSSL
//   - 对于OpenSSL，创建临时配置文件以支持旧版TLS协议
//   - 对于LibreSSL，跳过配置文件创建（避免兼容性问题）
//   - 设置包标签用于错误报告
func openSSLSetup() errorutils.Error {
	// 执行OpenSSL version命令获取版本信息
	result, err := execOpenSSL(context.TODO(), []string{"version"})
	if err != nil {
		return errorutils.NewWithErr(err).WithTag("openssl").Msgf(result.Stderr)
	}

	// 解析版本输出，格式通常为："OpenSSL 1.1.1f  31 Mar 2020" 或 "LibreSSL 2.8.3"
	arr := strings.Fields(result.Stdout)
	if len(arr) < 2 {
		return errorutils.NewWithTag("openssl", "failed to parse openssl version got %v", result.Stdout)
	}

	// 检查是否为LibreSSL
	if arr[0] == "LibreSSL" {
		IsLibreSSL = true
	}
	// 否则假设为OpenSSL

	OpenSSLVersion := arr[1] // 提取版本号

	// 仅为OpenSSL创建自定义配置文件，LibreSSL不需要
	if !IsLibreSSL {
		// 在临时目录创建自定义OpenSSL配置文件
		OPENSSL_CONF = filepath.Join(os.TempDir(), "openssl.cnf")
		err := os.WriteFile(OPENSSL_CONF, []byte(openSSLConfig), 0600)
		if err != nil {
			// 配置文件创建失败，记录日志并清空配置路径
			gologger.Debug().Label("openssl").Msg("Failed to create openssl.cnf file")
			OPENSSL_CONF = ""
		}
		PkgTag = "OpenSSL" + OpenSSLVersion // 设置OpenSSL包标签
	} else {
		PkgTag = "LibreSSL" + OpenSSLVersion // 设置LibreSSL包标签
	}

	return nil
}

// IsAvailable 检查OpenSSL是否可用
// 通过检查二进制文件路径是否已设置来判断可用性
//
// 返回值:
//   - bool: 如果OpenSSL可用返回true，否则返回false
func IsAvailable() bool {
	return BinaryPath != ""
}

// UseOpenSSLBinary 使用指定路径的OpenSSL二进制文件
// 允许用户指定自定义的OpenSSL可执行文件路径
//
// 参数:
//   - binpath: OpenSSL二进制文件的完整路径
//
// 注意:
//   - 如果设置失败，程序会致命退出（不会回退到默认路径）
//   - 适用于需要使用特定版本OpenSSL的场景
func UseOpenSSLBinary(binpath string) {
	BinaryPath = binpath // 设置自定义二进制文件路径

	// 重新执行OpenSSL设置
	if err := openSSLSetup(); err != nil {
		// 设置失败时不进行回退，直接致命退出
		gologger.Fatal().Label("openssl").Msg(err.Error())
	}
}