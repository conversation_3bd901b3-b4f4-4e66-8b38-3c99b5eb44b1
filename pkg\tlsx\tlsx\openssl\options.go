//
// Author: chenjb
// Version: V1.0
// Date: 2025-06-24 15:57:39
// FilePath: /yaml_scan/pkg/tlsx/tlsx/openssl/options.go
// Description: OpenSSL命令选项和协议定义

// Package openssl 的选项模块
// 定义OpenSSL命令行参数、支持的协议版本和相关配置
package openssl

import (
	"crypto/x509"  // X.509证书处理
	"errors"       // 错误处理
	"fmt"          // 格式化输出
	"strings"      // 字符串操作
)

// SupportedTLSVersions 定义OpenSSL模式支持的TLS版本列表
// 注意：TLS 1.3支持取决于OpenSSL版本，某些旧版本可能不支持
var SupportedTLSVersions = []string{
	"tls10", // TLS 1.0版本
	"tls11", // TLS 1.1版本
	"tls12", // TLS 1.2版本
	// "tls13", // TLS 1.3版本（注释掉，因为需要较新的OpenSSL版本）
}

// Protocols 定义支持的协议类型枚举
// 包括TLS和DTLS的各个版本
type Protocols int

// 协议版本常量定义
const (
	TLSv1          Protocols = iota // TLS 1.0协议
	TLSv1_1                         // TLS 1.1协议
	TLSv1_2                         // TLS 1.2协议
	TLSv1_3                         // TLS 1.3协议
	DTLSv1                          // DTLS 1.0协议（用于UDP）
	DTLSv1_2                        // DTLS 1.2协议（用于UDP）
	TLSUnsupported                  // 不支持的协议版本
)

// String 将协议枚举转换为OpenSSL命令行参数字符串
// 返回OpenSSL s_client命令中使用的协议版本参数
//
// 返回值:
//   - string: 对应的OpenSSL协议版本字符串
func (p *Protocols) String() string {
	switch *p {
	case 0:
		return "tls1"     // TLS 1.0
	case 1:
		return "tls1_1"   // TLS 1.1
	case 2:
		return "tls1_2"   // TLS 1.2
	// case 3:
	// 	return "tls1_3"   // TLS 1.3（注释掉，兼容性考虑）
	case 4:
		return "dtls1"    // DTLS 1.0
	case 5:
		return "dtls1_2"  // DTLS 1.2
	default:
		return ""
	}
}

// getProtocol 将TLS版本字符串转换为协议枚举类型
// 该函数负责将用户友好的版本字符串映射到内部协议枚举
//
// 参数:
//   - versionTLS: TLS版本字符串，如"tls10", "tls11", "tls12"等
//
// 返回值:
//   - Protocols: 对应的协议枚举值
//   - error: 如果版本字符串不被支持则返回错误
//
// 支持的版本映射:
//   - "tls10" -> TLSv1 (TLS 1.0)
//   - "tls11" -> TLSv1_1 (TLS 1.1)
//   - "tls12" -> TLSv1_2 (TLS 1.2)
//
// 注意:
//   - TLS 1.3和DTLS版本当前被注释掉，因为需要更新的OpenSSL版本支持
//   - 不支持的版本将返回TLSUnsupported和相应错误
func getProtocol(versionTLS string) (Protocols, error) {
	switch versionTLS {
	case "tls10":
		return TLSv1, nil      // TLS 1.0协议
	case "tls11":
		return TLSv1_1, nil    // TLS 1.1协议
	case "tls12":
		return TLSv1_2, nil    // TLS 1.2协议
	// case "tls13":
	// 	return TLSv1_3, nil   // TLS 1.3协议（需要OpenSSL 1.1.1+）
	// case "dtls10":
	// 	return DTLSv1, nil    // DTLS 1.0协议（用于UDP传输）
	// case "dtls12":
	// 	return DTLSv1_2, nil  // DTLS 1.2协议（用于UDP传输）
	default:
		return TLSUnsupported, errors.New("unsupported version")
	}
}

// Options 定义OpenSSL命令行选项结构
// 该结构体包含了执行OpenSSL s_client命令所需的所有配置参数
// 每个字段对应OpenSSL命令行的特定参数或行为控制
type Options struct {
	Address       string    // 连接地址，格式为"host:port"，对应-connect参数
	Cipher        []string  // 要使用的密码套件列表，对应-cipher参数（以逗号分隔）
	ServerName    string    // TLS扩展中的服务器名称指示（SNI），对应-servername参数
	CertChain     bool      // 是否显示完整证书链，对应-showcerts参数
	Protocol      Protocols // 要使用的TLS/DTLS协议版本，对应-tls1_2等参数
	CAFile        string    // CA证书文件路径，对应-CAfile参数，用于证书验证
	SkipCertParse bool      // 是否跳过证书解析和验证，仅用于内部控制（不影响命令行）
}

// Args 根据配置选项生成OpenSSL s_client命令的参数列表
// 该方法将Options结构体中的配置转换为可执行的命令行参数
//
// 返回值:
//   - []string: OpenSSL命令参数列表，第一个元素始终为"s_client"
//   - error: 如果必要参数缺失则返回错误
//
// 生成的命令格式示例:
//   ["s_client", "-connect", "example.com:443", "-servername", "example.com", "-tls1_2"]
//
// 参数生成规则:
//   - 空值参数会被自动忽略（不添加到命令行中）
//   - Address是必需参数，缺失时返回错误
//   - 密码套件列表会用逗号连接为单个字符串
//   - 协议版本会添加"-"前缀（如"-tls1_2"）
func (o *Options) Args() ([]string, error) {
	args := []string{"s_client"} // 基础命令，始终为s_client子命令

	// 添加连接地址参数（必需）
	if o.Address != "" {
		args = append(args, "-connect", o.Address)
	} else {
		return args, fmt.Errorf("openssl: address missing")
	}

	// 添加密码套件参数（可选）
	// 多个密码套件用逗号分隔为单个字符串
	if len(o.Cipher) != 0 {
		args = append(args, "-cipher", strings.Join(o.Cipher, ","))
	}

	// 添加SNI服务器名称参数（可选）
	if o.ServerName != "" {
		args = append(args, "-servername", o.ServerName)
	}

	// 添加证书链显示参数（可选）
	if o.CertChain {
		args = append(args, "-showcerts")
	}

	// 添加协议版本参数（可选）
	// 协议字符串前需要添加"-"前缀
	if o.Protocol.String() != "" {
		args = append(args, "-"+o.Protocol.String())
	}

	// 添加CA证书文件参数（可选）
	if o.CAFile != "" {
		args = append(args, "-CAfile", o.CAFile)
	}

	return args, nil
}

// Session 表示OpenSSL返回的TLS会话详细信息
// 该结构体包含了TLS握手成功后的会话状态和协商结果
// 信息来源于OpenSSL s_client命令的输出解析
type Session struct {
	Protocol  string // 协商使用的TLS协议版本，如"TLSv1.2"、"TLSv1.3"等
	Cipher    string // 协商使用的密码套件名称，如"ECDHE-RSA-AES256-GCM-SHA384"
	SessionID string // TLS会话标识符，用于会话复用（十六进制字符串）
	MasterKey string // TLS主密钥，用于加密通信（十六进制字符串，敏感信息）
}

// getTLSVersion 将OpenSSL格式的协议版本转换为内部标准格式
// 该方法负责统一不同来源的TLS版本表示方式
//
// 返回值:
//   - string: 标准化的TLS版本字符串，如"tls10", "tls11", "tls12"
//
// 版本映射规则:
//   - "TLSv1" -> "tls10" (TLS 1.0)
//   - "TLSv1.1" -> "tls11" (TLS 1.1)
//   - "TLSv1.2" -> "tls12" (TLS 1.2)
//   - 其他格式保持原样返回
//
// 注意:
//   - TLS 1.3的映射当前被注释掉，因为需要确保OpenSSL版本兼容性
//   - 对于无法识别的协议版本，直接返回原始字符串
func (s *Session) getTLSVersion() string {
	switch s.Protocol {
	case "TLSv1":
		return "tls10"    // TLS 1.0版本
	case "TLSv1.1":
		return "tls11"    // TLS 1.1版本
	case "TLSv1.2":
		return "tls12"    // TLS 1.2版本
	// case "TLSv1.3":
	// 	return "tls13"   // TLS 1.3版本（需要OpenSSL 1.1.1+支持）
	default:
		return s.Protocol // 无法识别的协议版本，返回原始字符串
	}
}

// Response 表示OpenSSL s_client命令的完整响应结果
// 该结构体包含了TLS连接建立后获取的所有重要信息
// 包括证书链、会话详情和服务器要求等
type Response struct {
	AllCerts           []*x509.Certificate // 服务器返回的完整证书链，按从叶证书到根证书的顺序排列
	Session            *Session            // TLS会话详细信息，包含协议版本、密码套件等协商结果
	ClientCertRequired bool                // 服务器是否要求客户端提供证书进行双向认证
}