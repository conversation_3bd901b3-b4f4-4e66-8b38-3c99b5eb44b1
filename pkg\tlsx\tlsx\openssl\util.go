// Author: chenjb
// Version: V1.0
// Date: 2025-06-24 15:58:12
// FilePath: /yaml_scan/pkg/tlsx/tlsx/openssl/util.go
// Description: OpenSSL工具函数和密码套件管理

// Package openssl 的工具模块
// 提供OpenSSL密码套件验证、会话数据解析和错误处理等功能
package openssl

import (
	"strings"                           // 字符串操作
	"yaml_scan/pkg/gologger"            // 日志记录器

	errorutil "yaml_scan/utils/errors"  // 错误处理工具
)

// AllCiphersNames 包含OpenSSL支持的所有密码套件名称
// 这个列表在初始化时通过调用OpenSSL命令动态获取
var AllCiphersNames []string = []string{}

// cipherMap 密码套件名称的快速查找映射表
// 用于验证给定的密码套件是否被OpenSSL支持
var cipherMap map[string]struct{} = map[string]struct{}{}

// toOpenSSLCiphers 验证给定的密码套件并返回有效的密码套件列表
// 检查每个密码套件是否被当前OpenSSL版本支持
//
// 参数:
//   - cipher: 可变参数，要验证的密码套件名称列表
//
// 返回值:
//   - []string: 验证通过的密码套件名称列表
//   - error: 如果某个密码套件不被支持则返回错误
//
// 验证逻辑:
//   - 遍历每个输入的密码套件名称
//   - 在cipherMap中查找是否存在
//   - 只返回被OpenSSL支持的密码套件
func toOpenSSLCiphers(cipher ...string) ([]string, error) {
	arr := []string{} // 存储验证通过的密码套件

	// 遍历每个要验证的密码套件
	for _, v := range cipher {
		if _, ok := cipherMap[v]; ok {
			// 密码套件被支持，添加到结果列表
			arr = append(arr, v)
		} else {
			// 密码套件不被支持，返回错误
			return arr, errorutil.NewWithTag("openssl", "cipher suite %v not supported", v)
		}
	}
	return arr, nil
}

// parseSessionValue 从OpenSSL会话输出行中解析值
// 解析格式为"key: value"的会话数据行
//
// 参数:
//   - line: OpenSSL输出的单行文本
//
// 返回值:
//   - string: 解析出的值，如果解析失败则返回空字符串
//
// 解析逻辑:
//   - 使用Fields分割避免空白字符问题
//   - 期望格式：[key, ":", value]
//   - 返回第三个字段作为值
func parseSessionValue(line string) string {
	// 使用Fields分割以避免空白字符问题
	tarr := strings.Fields(line)
	if len(tarr) == 3 {
		return tarr[2] // 返回值部分
	} else {
		return "" // 格式不正确，返回空字符串
	}
}

// Wrap 将err2包装到err1上，即使err1为nil也会返回err2
// 这是一个错误组合函数，用于累积多个错误
//
// 参数:
//   - err1: 第一个错误（可能为nil）
//   - err2: 要包装的第二个错误
//
// 返回值:
//   - errorutil.Error: 组合后的错误
//
// 包装逻辑:
//   - 如果err1为nil，直接返回err2
//   - 如果err1不为nil，将err2包装到err1上
func Wrap(err1 errorutil.Error, err2 errorutil.Error) errorutil.Error {
	if err1 == nil {
		return err2 // 第一个错误为空，返回第二个错误
	}
	return err1.Wrap(err2) // 将第二个错误包装到第一个错误上
}

// certRequiredAlerts 定义表示服务器要求客户端证书的SSL警告消息列表
// 这些警告码对应TLS规范中的特定错误情况
var certRequiredAlerts = []string{
	"SSL alert number 42",  // bad_certificate - 证书无效或缺失
	"SSL alert number 116", // certificate_required - 服务器明确要求客户端证书
}

// isClientCertRequired 检查OpenSSL输出以确定错误是否由于服务器要求客户端证书
// 该函数通过分析OpenSSL的错误输出来判断是否需要进行双向TLS认证
//
// 参数:
//   - data: OpenSSL命令的输出数据（通常是stderr内容）
//
// 返回值:
//   - bool: 如果服务器要求客户端证书则返回true，否则返回false
//
// 检测逻辑:
//   - 逐行扫描OpenSSL的输出内容
//   - 查找预定义的SSL警告消息
//   - 匹配到相关警告码时表示服务器要求客户端证书
//
// 应用场景:
//   - 双向TLS认证检测
//   - 错误原因分析
//   - 自动化证书配置建议
func isClientCertRequired(data string) bool {
	// 按行分割OpenSSL输出内容
	for _, line := range strings.Split(data, "\n") {
		// 检查每行是否包含客户端证书要求的警告信息
		for _, alert := range certRequiredAlerts {
			if strings.Contains(line, alert) {
				return true // 发现相关警告，服务器要求客户端证书
			}
		}
	}
	return false // 未发现相关警告，服务器不要求客户端证书
}

// init 包初始化函数，负责动态获取OpenSSL支持的密码套件列表
// 该函数在包加载时自动执行，初始化全局的密码套件数据结构
//
// 初始化流程:
//   1. 检查OpenSSL是否可用（通过IsAvailable函数）
//   2. 如果OpenSSL不可用，跳过初始化过程
//   3. 调用getCiphers函数获取系统OpenSSL支持的所有密码套件
//   4. 构建密码套件的快速查找映射表（cipherMap）
//   5. 填充全局密码套件名称列表（AllCiphersNames）
//
// 错误处理:
//   - 如果获取密码套件失败，记录调试日志但不中断程序执行
//   - 这确保了即使OpenSSL配置有问题，程序仍能正常启动
//
// 注意:
//   - 该函数的执行结果直接影响toOpenSSLCiphers函数的验证能力
//   - 如果初始化失败，所有密码套件验证都将失败
func init() {
	// 检查OpenSSL是否在系统中可用
	if !IsAvailable() {
		return // OpenSSL不可用，跳过密码套件初始化
	}

	// 获取OpenSSL支持的所有密码套件
	ciphers, err := getCiphers()
	if err != nil {
		// 获取密码套件失败，记录调试日志
		gologger.Debug().Label("openssl").Msg(err.Error())
	}

	// 构建密码套件数据结构
	for _, v := range ciphers {
		cipherMap[v] = struct{}{}                    // 添加到快速查找映射表
		AllCiphersNames = append(AllCiphersNames, v) // 添加到全局名称列表
	}
}
