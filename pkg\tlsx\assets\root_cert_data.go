// Author: chenjb
// Version: V1.0
// Date: 2025-06-24 14:15:38
// FilePath: /yaml_scan/pkg/tlsx/assets/root_cert_data.go
// Description: 根证书数据管理和证书验证功能

// Package assets 提供TLS扫描所需的静态资源管理
// 主要包含根证书数据的嵌入、解析和验证功能
// 用于在TLS连接验证过程中识别和验证根证书颁发机构
package assets

import (
	"crypto/x509"                   // X.509证书处理标准库
	"encoding/pem"                  // PEM格式编码解码
	"fmt"                           // 格式化输出
	"yaml_scan/pkg/gologger"        // 日志记录器
)

//go:embed root-certs.pem
// rootCertDataBin 包含嵌入的根证书数据
// 使用go:embed指令将root-certs.pem文件的内容嵌入到二进制文件中
// 这样可以避免运行时依赖外部证书文件，提高程序的可移植性
var rootCertDataBin string

// RootCerts 包含解析后的根证书列表
// 该变量在包初始化时从rootCertDataBin中解析得到
// 包含了用于TLS证书链验证的可信根证书颁发机构列表
var RootCerts []*x509.Certificate

// init 包初始化函数，负责解析嵌入的根证书数据
// 在包加载时自动执行，将PEM格式的根证书数据解析为x509.Certificate结构
//
// 初始化流程:
//   1. 调用ParseCertificates函数解析嵌入的证书数据
//   2. 将解析结果存储到全局变量RootCerts中
//   3. 如果解析失败，记录错误日志但不中断程序执行
//
// 错误处理:
//   - 解析失败时记录错误日志，但允许程序继续运行
//   - 这确保了即使根证书数据有问题，TLS扫描功能仍可部分工作
func init() {
	var err error
	// 解析嵌入的根证书数据
	RootCerts, err = ParseCertificates([]byte(rootCertDataBin))
	if err != nil {
		// 记录解析错误，但不中断程序执行
		gologger.Error().Label("rootcert").Msgf("failed to parse root certs: %v", err)
	}
}

// ParseCertificates 从PEM格式数据中解析X.509证书
// 该函数能够处理包含多个证书的PEM文件，逐个解析每个证书块
//
// 参数:
//   - data: 包含一个或多个PEM格式证书的字节数据
//
// 返回值:
//   - []*x509.Certificate: 成功解析的X.509证书列表
//   - error: 解析过程中遇到的最后一个错误，如果所有证书都解析成功则为nil
//
// 解析流程:
//   1. 使用pem.Decode逐个解析PEM块
//   2. 检查PEM块类型是否为"CERTIFICATE"
//   3. 将有效的证书块转换为x509.Certificate结构
//   4. 跳过无效的证书块，继续处理剩余数据
//   5. 返回所有成功解析的证书
//
// 错误处理:
//   - 单个证书解析失败不会中断整个过程
//   - 记录最后遇到的错误，但继续处理其他证书
//   - 即使有错误，也会返回成功解析的证书列表
func ParseCertificates(data []byte) ([]*x509.Certificate, error) {
	var parsedCerts []*x509.Certificate // 存储成功解析的证书
	var err error                       // 记录解析过程中的错误

	// 开始解析第一个PEM块
	block, rest := pem.Decode(data)

	// 循环处理所有PEM块
	for block != nil {
		// 检查PEM块类型是否为证书
		if block.Type == "CERTIFICATE" {
			// 解析X.509证书结构
			cert, errx := x509.ParseCertificate(block.Bytes)
			if errx != nil {
				// 记录错误但继续处理其他证书
				err = fmt.Errorf("could not parse certificate: %s", errx)
				continue
			}
			// 将成功解析的证书添加到列表中
			parsedCerts = append(parsedCerts, cert)
		}

		// 检查是否还有剩余数据需要处理
		if len(rest) == 0 {
			break // 没有更多数据，退出循环
		}

		// 解析下一个PEM块
		block, rest = pem.Decode(rest)
	}

	return parsedCerts, err
}

// IsRootCert 检查给定证书是否为可信根证书
// 该函数通过与预加载的根证书列表进行比较来判断证书的根证书状态
//
// 参数:
//   - cert: 要检查的X.509证书
//
// 返回值:
//   - bool: 如果证书是可信根证书则返回true，否则返回false
//
// 检查逻辑:
//   - 遍历全局根证书列表RootCerts中的每个证书
//   - 使用x509.Certificate.Equal方法进行精确比较
//   - Equal方法比较证书的所有关键字段（包括公钥、签名等）
//   - 只要找到一个匹配的根证书就返回true
//
// 应用场景:
//   - TLS证书链验证过程中的根证书识别
//   - 证书信任状态判断
//   - 安全策略实施中的证书分类
//
// 注意:
//   - 该函数依赖于包初始化时加载的根证书数据
//   - 如果根证书数据加载失败，可能影响检查结果的准确性
//   - 比较操作的时间复杂度为O(n)，其中n为根证书数量
func IsRootCert(cert *x509.Certificate) bool {
	// 遍历所有预加载的根证书
	for _, c := range RootCerts {
		// 使用Equal方法进行精确的证书比较
		if c.Equal(cert) {
			return true // 找到匹配的根证书
		}
	}
	return false // 未找到匹配的根证书
}