// Author: chenjb
// Version: V1.0
// Date: 2025-06-23 20:25:04
// FilePath: /yaml_scan/pkg/tlsx/tlsx/tls/util.go
// Description: 标准TLS库工具函数和常量定义

// Package tls 的工具模块
// 提供TLS密码套件和版本的映射表，以及相关的转换函数
package tls

import (
	"crypto/tls"                        // Go标准TLS库
	errorutil "yaml_scan/utils/errors"  // 错误处理工具
)

// 全局变量，存储所有支持的TLS密码套件和版本信息
var (
	// AllCiphers 包含所有支持的TLS密码套件的数值标识符
	// 这些数值对应Go TLS库中定义的密码套件常量
	AllCiphers []uint16

	// AllCiphersNames 包含所有支持的TLS密码套件的名称
	// 这些名称是标准的TLS密码套件名称字符串
	AllCiphersNames []string

	// SupportedTlsVersions 包含所有支持的TLS版本的名称
	// 包括"tls10", "tls11", "tls12", "tls13"等版本标识
	SupportedTlsVersions []string
)

// init 包初始化函数
// 在包加载时自动执行，从映射表中提取所有支持的密码套件和版本
func init() {
	// 从密码套件映射表中提取所有密码套件的名称和数值标识符
	for name, cipher := range tlsCiphers {
		AllCiphersNames = append(AllCiphersNames, name)  // 添加密码套件名称
		AllCiphers = append(AllCiphers, cipher)          // 添加密码套件数值标识符
	}

	// 从版本映射表中提取所有支持的TLS版本名称
	for name := range versionStringToTLSVersion {
		SupportedTlsVersions = append(SupportedTlsVersions, name)
	}
}

// toTLSCiphers 将密码套件名称列表转换为Go TLS库的数值标识符列表
// 用于将用户指定的密码套件名称转换为TLS配置中使用的数值
//
// 参数:
//   - items: 密码套件名称列表，如["TLS_RSA_WITH_AES_128_CBC_SHA", "TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384"]
//
// 返回值:
//   - []uint16: 对应的密码套件数值标识符列表
//   - error: 如果某个密码套件名称不被支持则返回错误
//
// 使用场景:
//   - 配置TLS连接时指定特定的密码套件
//   - 密码套件枚举时验证密码套件的有效性
func toTLSCiphers(items []string) ([]uint16, error) {
	var convertedCiphers []uint16 // 存储转换后的数值标识符

	// 遍历每个密码套件名称并进行转换
	for _, item := range items {
		cipher, ok := tlsCiphers[item] // 在映射表中查找对应的数值
		if !ok {
			// 密码套件不被支持，返回错误
			return nil, errorutil.NewWithTag("ctls", "cipher suite %v not supported", item)
		}
		convertedCiphers = append(convertedCiphers, cipher) // 添加到结果列表
	}

	return convertedCiphers, nil
}


// tlsCiphers 是密码套件名称到对应数值标识符的映射
// 该映射包含了标准TLS库支持的所有密码套件
var tlsCiphers = map[string]uint16{
	// TLS_RSA 系列密码套件
	"TLS_RSA_WITH_RC4_128_SHA":                      tls.TLS_RSA_WITH_RC4_128_SHA,                      // 使用RSA密钥交换和RC4流加密
	"TLS_RSA_WITH_3DES_EDE_CBC_SHA":                 tls.TLS_RSA_WITH_3DES_EDE_CBC_SHA,                 // 使用RSA密钥交换和3DES块加密
	"TLS_RSA_WITH_AES_128_CBC_SHA":                  tls.TLS_RSA_WITH_AES_128_CBC_SHA,                  // 使用RSA密钥交换和AES-128块加密（CBC模式）
	"TLS_RSA_WITH_AES_256_CBC_SHA":                  tls.TLS_RSA_WITH_AES_256_CBC_SHA,                  // 使用RSA密钥交换和AES-256块加密（CBC模式）
	"TLS_RSA_WITH_AES_128_CBC_SHA256":               tls.TLS_RSA_WITH_AES_128_CBC_SHA256,               // 使用RSA密钥交换和AES-128块加密（CBC模式），SHA-256哈希
	"TLS_RSA_WITH_AES_128_GCM_SHA256":               tls.TLS_RSA_WITH_AES_128_GCM_SHA256,               // 使用RSA密钥交换和AES-128块加密（GCM模式），SHA-256哈希
	"TLS_RSA_WITH_AES_256_GCM_SHA384":               tls.TLS_RSA_WITH_AES_256_GCM_SHA384,               // 使用RSA密钥交换和AES-256块加密（GCM模式），SHA-384哈希
	
	// ECDHE_ECDSA 系列密码套件
	"TLS_ECDHE_ECDSA_WITH_RC4_128_SHA":              tls.TLS_ECDHE_ECDSA_WITH_RC4_128_SHA,              // 使用ECDHE密钥交换、ECDSA认证和RC4流加密
	"TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA":          tls.TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA,          // 使用ECDHE密钥交换、ECDSA认证和AES-128块加密（CBC模式）
	"TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA":          tls.TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA,          // 使用ECDHE密钥交换、ECDSA认证和AES-256块加密（CBC模式）
	
	// ECDHE_RSA 系列密码套件
	"TLS_ECDHE_RSA_WITH_RC4_128_SHA":                tls.TLS_ECDHE_RSA_WITH_RC4_128_SHA,                // 使用ECDHE密钥交换、RSA认证和RC4流加密
	"TLS_ECDHE_RSA_WITH_3DES_EDE_CBC_SHA":           tls.TLS_ECDHE_RSA_WITH_3DES_EDE_CBC_SHA,           // 使用ECDHE密钥交换、RSA认证和3DES块加密
	"TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA":            tls.TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA,            // 使用ECDHE密钥交换、RSA认证和AES-128块加密（CBC模式）
	"TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA":            tls.TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA,            // 使用ECDHE密钥交换、RSA认证和AES-256块加密（CBC模式）
	
	// 现代密码套件（更安全）
	"TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA256":       tls.TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA256,       // 使用ECDHE密钥交换、ECDSA认证和AES-128块加密（CBC模式），SHA-256哈希
	"TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA256":         tls.TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA256,         // 使用ECDHE密钥交换、RSA认证和AES-128块加密（CBC模式），SHA-256哈希
	"TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256":         tls.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,         // 使用ECDHE密钥交换、RSA认证和AES-128块加密（GCM模式），SHA-256哈希
	"TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256":       tls.TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,       // 使用ECDHE密钥交换、ECDSA认证和AES-128块加密（GCM模式），SHA-256哈希
	"TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384":         tls.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,         // 使用ECDHE密钥交换、RSA认证和AES-256块加密（GCM模式），SHA-384哈希
	"TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384":       tls.TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384,       // 使用ECDHE密钥交换、ECDSA认证和AES-256块加密（GCM模式），SHA-384哈希
	
	// ChaCha20-Poly1305 密码套件（适用于移动设备）
	"TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256":   tls.TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256,   // 使用ECDHE密钥交换、RSA认证和ChaCha20-Poly1305加密
	"TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256": tls.TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256, // 使用ECDHE密钥交换、ECDSA认证和ChaCha20-Poly1305加密
	
	// TLS 1.3 密码套件
	"TLS_AES_128_GCM_SHA256":                        tls.TLS_AES_128_GCM_SHA256,                        // TLS 1.3的AES-128-GCM密码套件
	"TLS_AES_256_GCM_SHA384":                        tls.TLS_AES_256_GCM_SHA384,                        // TLS 1.3的AES-256-GCM密码套件
	"TLS_CHACHA20_POLY1305_SHA256":                  tls.TLS_CHACHA20_POLY1305_SHA256,                  // TLS 1.3的ChaCha20-Poly1305密码套件
	
	// 特殊用途密码套件
	"TLS_FALLBACK_SCSV":                             tls.TLS_FALLBACK_SCSV,                             // 防止协议降级攻击的伪密码套件
	"TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305":          tls.TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305,          // 使用ECDHE密钥交换、RSA认证和ChaCha20-Poly1305加密（旧版本）
	"TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305":        tls.TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305,          // 使用ECDHE密钥交换、ECDSA认证和ChaCha20-Poly1305加密（旧版本）
}