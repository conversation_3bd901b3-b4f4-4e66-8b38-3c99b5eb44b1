// 
// Author: chenjb
// Version: V1.0
// Date: 2025-05-27 15:59:33
// FilePath: /yaml_scan/pkg/fastdialer/utils/dialerwarp_test.go
// Description: 
package utils

import (
	"context"
	"net"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
)

// setupTestServer 设置测试TCP服务器
// 返回监听器和服务器地址
func setupTestServer(t *testing.T) (net.Listener, string) {
	listener, err := net.Listen("tcp", "127.0.0.1:0")
	require.NoError(t, err, "创建测试TCP服务器失败")
	
	// 启动接受连接的goroutine
	go func() {
		for {
			conn, err := listener.Accept()
			if err != nil {
				return // 监听器已关闭
			}
			// 简单地关闭连接，这对测试已足够
			conn.Close()
		}
	}()
	
	return listener, listener.Addr().String()
}

// TestNewDialWrap 测试创建新的拨号包装器
func TestNewDialWrap(t *testing.T) {
	// 创建基本拨号器
	dialer := &net.Dialer{
		Timeout: 5 * time.Second,
	}
	
	// 测试创建拨号包装器，使用有效的IP
	dw, err := NewDialWrap(dialer, []string{"127.0.0.1", "::1"}, "tcp", "example.com", "80")
	require.NoError(t, err, "使用有效IP创建DialWrap应该成功")
	require.NotNil(t, dw, "返回的DialWrap不应为nil")
	
	// 验证IPv4和IPv6地址是否正确分离
	require.Len(t, dw.ipv4, 1, "应有1个IPv4地址")
	require.Len(t, dw.ipv6, 1, "应有1个IPv6地址")
	require.Len(t, dw.ips, 2, "总共应有2个IP地址")
	
	// 测试创建拨号包装器，使用无效的IP
	_, err = NewDialWrap(dialer, []string{"invalid-ip"}, "tcp", "example.com", "80")
	require.Error(t, err, "使用无效IP创建DialWrap应该失败")
	require.Equal(t, ErrNoIPs, err, "错误应该是ErrNoIPs")
	
	// 测试创建拨号包装器，使用空IP列表
	_, err = NewDialWrap(dialer, []string{}, "tcp", "example.com", "80")
	require.Error(t, err, "使用空IP列表创建DialWrap应该失败")
	require.Equal(t, ErrNoIPs, err, "错误应该是ErrNoIPs")
}

// TestAddress 测试获取地址功能
func TestAddress(t *testing.T) {
	// 创建基本拨号器
	dialer := &net.Dialer{
		Timeout: 5 * time.Second,
	}
	
	// 创建拨号包装器
	dw, err := NewDialWrap(dialer, []string{"***********", "********"}, "tcp", "example.com", "8080")
	require.NoError(t, err, "创建DialWrap应该成功")
	
	// 测试Address方法
	ip, port := dw.Address()
	require.Equal(t, "***********", ip, "应返回第一个IP地址")
	require.Equal(t, "8080", port, "应返回正确的端口")
	
	// 测试没有IP时的情况
	emptyDw := &DialWrap{
		ips:  []net.IP{},
		port: "8080",
	}
	ip, port = emptyDw.Address()
	require.Equal(t, "", ip, "没有IP时应返回空字符串")
	require.Equal(t, "", port, "没有IP时应返回空字符串")
}

// TestSetFirstConnectionDuration 测试设置第一个连接时间
func TestSetFirstConnectionDuration(t *testing.T) {
	// 创建基本拨号器
	dialer := &net.Dialer{
		Timeout: 5 * time.Second,
	}
	
	// 创建拨号包装器
	dw, err := NewDialWrap(dialer, []string{"127.0.0.1"}, "tcp", "example.com", "80")
	require.NoError(t, err, "创建DialWrap应该成功")
	
	// 验证初始值
	require.Equal(t, time.Duration(0), dw.FirstConnectionTook(), "初始连接时间应为0")
	
	// 设置值并验证
	testDuration := 2 * time.Second
	dw.SetFirstConnectionDuration(testDuration)
	require.Equal(t, testDuration, dw.FirstConnectionTook(), "应返回设置的连接时间")
}

// TestDialContext 测试拨号上下文功能
func TestDialContext(t *testing.T) {
	// 设置测试服务器
	listener, addr := setupTestServer(t)
	defer listener.Close()
	
	// 解析地址获取主机和端口
	host, port, err := net.SplitHostPort(addr)
	require.NoError(t, err, "解析地址失败")
	
	// 创建基本拨号器
	dialer := &net.Dialer{
		Timeout: 5 * time.Second,
	}
	
	// 创建拨号包装器
	dw, err := NewDialWrap(dialer, []string{host}, "tcp", host, port)
	require.NoError(t, err, "创建DialWrap应该成功")
	
	// 测试拨号
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	
	conn, err := dw.DialContext(ctx, "", "")
	require.NoError(t, err, "拨号到测试服务器应该成功")
	require.NotNil(t, conn, "连接不应为nil")
	defer conn.Close()
	
	// 测试已取消的上下文
	canceledCtx, cancelFunc := context.WithCancel(context.Background())
	cancelFunc() // 立即取消
	
	_, err = dw.DialContext(canceledCtx, "", "")
	require.Error(t, err, "使用已取消的上下文拨号应该失败")
	
	// 测试不存在的服务器
	invalidDw, err := NewDialWrap(dialer, []string{"127.0.0.1"}, "tcp", "127.0.0.1", "12345")
	require.NoError(t, err, "创建无效目标的DialWrap应该成功")
	
	_, err = invalidDw.DialContext(ctx, "", "")
	require.Error(t, err, "拨号到不存在的服务器应该失败")
}

// TestMultiIPDialing 测试多IP拨号功能，验证修复后的超时处理
func TestMultiIPDialing(t *testing.T) {
	// 设置测试服务器
	listener, addr := setupTestServer(t)
	defer listener.Close()

	// 解析地址获取主机和端口
	host, port, err := net.SplitHostPort(addr)
	require.NoError(t, err, "解析地址失败")

	// 创建基本拨号器，设置较短的超时时间
	dialer := &net.Dialer{
		Timeout: 3 * time.Second,
	}

	// 测试多个IP地址，包括有效和无效的IP
	// 127.0.0.1 是有效的（测试服务器），********* 是无效的（RFC5737测试地址）
	ips := []string{host, "*********", "*********"}

	// 创建拨号包装器
	dw, err := NewDialWrap(dialer, ips, "tcp", "example.com", port)
	require.NoError(t, err, "创建多IP DialWrap应该成功")
	require.Len(t, dw.ips, 3, "应有3个IP地址")

	// 测试拨号 - 应该成功连接到有效的IP
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	start := time.Now()
	conn, err := dw.DialContext(ctx, "", "")
	duration := time.Since(start)

	require.NoError(t, err, "多IP拨号应该成功（至少有一个有效IP）")
	require.NotNil(t, conn, "连接不应为nil")
	defer conn.Close()

	// 验证连接时间合理（不应该因为deadline计算错误而过早超时）
	require.Less(t, duration, 8*time.Second, "连接时间不应过长")

	t.Logf("多IP连接成功，耗时: %v", duration)
}

// TestDeadlineCalculation 测试deadline计算逻辑的修复
func TestDeadlineCalculation(t *testing.T) {
	// 创建基本拨号器
	dialer := &net.Dialer{
		Timeout: 5 * time.Second,
	}

	// 创建拨号包装器
	dw, err := NewDialWrap(dialer, []string{"127.0.0.1"}, "tcp", "example.com", "80")
	require.NoError(t, err, "创建DialWrap应该成功")

	// 测试初始状态的deadline计算
	ctx := context.Background()
	now := time.Now()
	deadline := dw.deadline(ctx, now)

	// 验证deadline计算正确（应该是now + timeout，不包括FirstConnectionTook）
	expected := now.Add(5 * time.Second)
	require.True(t, deadline.Equal(expected), "deadline应该等于now + timeout")

	// 设置首次连接时间，验证不会影响后续deadline计算
	dw.SetFirstConnectionDuration(2 * time.Second)
	deadline2 := dw.deadline(ctx, now)
	require.True(t, deadline2.Equal(expected), "设置FirstConnectionDuration后deadline不应改变")

	// 测试带有上下文deadline的情况
	ctxDeadline := now.Add(3 * time.Second)
	ctxWithDeadline, cancel := context.WithDeadline(context.Background(), ctxDeadline)
	defer cancel()

	deadline3 := dw.deadline(ctxWithDeadline, now)
	// 应该选择更早的deadline（3秒 vs 5秒）
	require.True(t, deadline3.Equal(ctxDeadline), "应该选择更早的deadline")
}

// TestParallelDialingWithTimeout 测试并行拨号的超时处理
func TestParallelDialingWithTimeout(t *testing.T) {
	// 创建基本拨号器，设置较短的超时
	dialer := &net.Dialer{
		Timeout: 2 * time.Second,
	}

	// 使用多个无效IP地址测试超时行为
	invalidIPs := []string{"*********", "*********", "*********"}

	dw, err := NewDialWrap(dialer, invalidIPs, "tcp", "example.com", "12345")
	require.NoError(t, err, "创建DialWrap应该成功")

	// 测试并行拨号超时
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	start := time.Now()
	conns, err := dw.dialAllParallel(ctx)
	duration := time.Since(start)

	require.Error(t, err, "拨号到无效IP应该失败")
	require.Nil(t, conns, "连接列表应为nil")

	// 验证超时时间合理（应该在拨号器超时时间附近，不会因为deadline计算错误而过早）
	require.Greater(t, duration, 1*time.Second, "超时时间不应过短")
	require.Less(t, duration, 4*time.Second, "超时时间不应过长")

	t.Logf("并行拨号超时测试完成，耗时: %v", duration)
}

