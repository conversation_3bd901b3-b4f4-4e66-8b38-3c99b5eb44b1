<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TLS扫描库 - 快速参考</title>
    <style>
        :root {
            --primary: #3b82f6;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --dark: #1f2937;
            --light: #f8fafc;
        }

        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Segoe UI', sans-serif;
            line-height: 1.6;
            color: var(--dark);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        h1 {
            text-align: center;
            color: var(--primary);
            margin-bottom: 30px;
            font-size: 2.5rem;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            border-left: 4px solid var(--primary);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .card h3 {
            color: var(--primary);
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .code {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            overflow-x: auto;
            margin: 10px 0;
        }

        .badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 600;
            color: white;
            margin: 2px;
        }

        .badge.stable { background: var(--success); }
        .badge.beta { background: var(--warning); }
        .badge.new { background: var(--danger); }

        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
        }

        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }

        th {
            background: var(--primary);
            color: white;
            font-weight: 600;
        }

        tr:hover { background: #f8fafc; }

        .highlight {
            background: linear-gradient(45deg, #fef3c7, #fde68a);
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid var(--warning);
            margin: 15px 0;
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #f3f4f6;
        }

        .feature-list li:before {
            content: "✓";
            color: var(--success);
            font-weight: bold;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔒 TLS扫描库快速参考</h1>
        
        <div class="highlight">
            <strong>yaml_scan/pkg/tlsx</strong> - 专业的Go语言TLS安全扫描工具包
            <span class="badge stable">Go 1.24+</span>
            <span class="badge stable">生产就绪</span>
            <span class="badge new">多实现支持</span>
        </div>

        <div class="grid">
            <!-- 快速开始 -->
            <div class="card">
                <h3>🚀 快速开始</h3>
                <div class="code">package main

import (
    "fmt"
    "yaml_scan/pkg/tlsx/tlsx"
    "yaml_scan/pkg/tlsx/tlsx/clients"
)

func main() {
    options := &clients.Options{
        ScanMode: "ctls",
        Timeout:  10,
        Retries:  3,
    }
    
    service, _ := tlsx.New(options)
    response, _ := service.Connect(
        "www.baidu.com", "", "443")
    
    fmt.Printf("TLS版本: %s\n", 
        response.Version)
}</div>
            </div>

            <!-- 核心API -->
            <div class="card">
                <h3>🔧 核心API</h3>
                <table>
                    <tr><th>方法</th><th>用途</th></tr>
                    <tr><td><code>tlsx.New()</code></td><td>创建服务</td></tr>
                    <tr><td><code>Connect()</code></td><td>基本连接</td></tr>
                    <tr><td><code>ConnectWithOptions()</code></td><td>高级连接</td></tr>
                    <tr><td><code>runner.New()</code></td><td>批量扫描</td></tr>
                    <tr><td><code>output.New()</code></td><td>输出处理</td></tr>
                </table>
            </div>

            <!-- 扫描模式 -->
            <div class="card">
                <h3>⚙️ 扫描模式</h3>
                <table>
                    <tr><th>模式</th><th>特点</th></tr>
                    <tr><td><code>ctls</code></td><td>Go原生，稳定</td></tr>
                    <tr><td><code>ztls</code></td><td>功能丰富，指纹强</td></tr>
                    <tr><td><code>openssl</code></td><td>兼容性最强</td></tr>
                    <tr><td><code>auto</code></td><td>智能选择</td></tr>
                </table>
            </div>

            <!-- 主要功能 -->
            <div class="card">
                <h3>✨ 主要功能</h3>
                <ul class="feature-list">
                    <li>TLS版本枚举</li>
                    <li>密码套件分析</li>
                    <li>证书安全检查</li>
                    <li>JARM/JA3指纹识别</li>
                    <li>批量并发扫描</li>
                    <li>多格式输出</li>
                    <li>安全合规检查</li>
                    <li>性能优化</li>
                </ul>
            </div>

            <!-- 配置示例 -->
            <div class="card">
                <h3>📋 常用配置</h3>
                <div class="code">// 基础扫描
&clients.Options{
    ScanMode: "ctls",
    Timeout:  10,
    Retries:  3,
}

// 深度分析
&clients.Options{
    ScanMode:        "ztls",
    TlsVersionsEnum: true,
    TlsCiphersEnum:  true,
    Jarm:            true,
    Expired:         true,
    SelfSigned:      true,
}

// 批量扫描
&clients.Options{
    Concurrency: 10,
    Ports:       []string{"443"},
    Inputs:      []string{"host1", "host2"},
    JSON:        true,
}</div>
            </div>

            <!-- 性能建议 -->
            <div class="card">
                <h3>⚡ 性能建议</h3>
                <table>
                    <tr><th>场景</th><th>并发</th><th>超时</th></tr>
                    <tr><td>小规模</td><td>1-5</td><td>10s</td></tr>
                    <tr><td>中等规模</td><td>5-20</td><td>10-15s</td></tr>
                    <tr><td>大规模</td><td>20-50</td><td>5-10s</td></tr>
                    <tr><td>JARM指纹</td><td>1-10</td><td>20s+</td></tr>
                </table>
            </div>
        </div>

        <div class="grid">
            <!-- 证书检查 -->
            <div class="card">
                <h3>🛡️ 证书检查选项</h3>
                <div class="code">options := &clients.Options{
    Expired:           true, // 过期检查
    SelfSigned:        true, // 自签名检查
    MisMatched:        true, // 主机名匹配
    Revoked:           true, // 撤销检查
    Untrusted:         true, // 信任检查
    WildcardCertCheck: true, // 通配符检查
    Hash: "md5,sha1,sha256", // 指纹哈希
}</div>
            </div>

            <!-- 输出格式 -->
            <div class="card">
                <h3>📄 输出选项</h3>
                <div class="code">options := &clients.Options{
    JSON:       true,           // JSON格式
    OutputFile: "results.json", // 输出文件
    NoColor:    true,           // 禁用颜色
    SAN:        true,           // 显示SAN
    CN:         true,           // 显示CN
    TLSVersion: true,           // 显示版本
    Cipher:     true,           // 显示密码套件
    Serial:     true,           // 显示序列号
}</div>
            </div>
        </div>

        <div class="grid">
            <!-- 错误处理 -->
            <div class="card">
                <h3>🐛 常见错误</h3>
                <table>
                    <tr><th>错误类型</th><th>解决方案</th></tr>
                    <tr><td>连接超时</td><td>增加Timeout值</td></tr>
                    <tr><td>证书验证失败</td><td>设置VerifyServerCertificate=false</td></tr>
                    <tr><td>内存不足</td><td>减少Concurrency</td></tr>
                    <tr><td>DNS解析失败</td><td>使用IP地址或自定义Resolvers</td></tr>
                </table>
            </div>

            <!-- 最佳实践 -->
            <div class="card">
                <h3>✨ 最佳实践</h3>
                <ul class="feature-list">
                    <li>生产环境使用ctls模式</li>
                    <li>并发数不超过20</li>
                    <li>添加适当的延迟</li>
                    <li>启用JSON输出便于处理</li>
                    <li>定期更新CA证书</li>
                    <li>监控扫描性能</li>
                    <li>遵守扫描授权</li>
                    <li>保护敏感数据</li>
                </ul>
            </div>
        </div>

        <div class="grid">
            <!-- 集成示例 -->
            <div class="card">
                <h3>🔗 集成示例</h3>
                <div class="code">// CI/CD集成
name: TLS Security Scan
on:
  schedule:
    - cron: '0 2 * * *'
jobs:
  scan:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Run TLS Scan
      run: |
        go run ./cmd/scanner \
          -mode ctls \
          -json \
          -output results.json \
          -targets hosts.txt</div>
            </div>

            <!-- 监控告警 -->
            <div class="card">
                <h3>📊 监控告警</h3>
                <div class="code">// Slack告警集成
func checkAndAlert(response *Response) {
    if response.CertificateResponse.Expired {
        sendSlackAlert(
            "🚨 证书过期: " + response.Host)
    }

    if response.CertificateResponse.SelfSigned {
        sendSlackAlert(
            "⚠️ 自签名证书: " + response.Host)
    }
}</div>
            </div>
        </div>

        <div class="grid">
            <!-- 高级功能 -->
            <div class="card">
                <h3>🚀 高级功能</h3>
                <table>
                    <tr><th>功能</th><th>用途</th></tr>
                    <tr><td>JARM指纹</td><td>服务器识别</td></tr>
                    <tr><td>JA3/JA3S</td><td>客户端/服务器指纹</td></tr>
                    <tr><td>证书链分析</td><td>完整证书验证</td></tr>
                    <tr><td>密码套件枚举</td><td>安全配置检查</td></tr>
                    <tr><td>版本降级检测</td><td>协议安全分析</td></tr>
                </table>
            </div>

            <!-- 部署选项 -->
            <div class="card">
                <h3>🐳 部署选项</h3>
                <ul class="feature-list">
                    <li>Docker容器化部署</li>
                    <li>Kubernetes集群部署</li>
                    <li>AWS Lambda无服务器</li>
                    <li>GitHub Actions CI/CD</li>
                    <li>本地二进制部署</li>
                    <li>云函数集成</li>
                </ul>
            </div>
        </div>

        <div class="highlight">
            <strong>📚 学习路径:</strong>
            <br>1. 阅读 <a href="index.html#overview" style="color: var(--primary);">项目概述</a> 了解基本概念
            <br>2. 运行 <a href="../example/" style="color: var(--primary);">示例代码</a> 快速上手
            <br>3. 查看 <a href="index.html#api" style="color: var(--primary);">API文档</a> 深入了解
            <br>4. 参考 <a href="index.html#configuration" style="color: var(--primary);">配置选项</a> 进行定制
            <br>5. 学习 <a href="index.html#security" style="color: var(--primary);">安全最佳实践</a>
        </div>

        <div style="text-align: center; margin-top: 30px; padding: 20px; background: linear-gradient(45deg, #667eea, #764ba2); border-radius: 15px; color: white;">
            <h3 style="margin-bottom: 15px;">🎯 快速开始三步骤</h3>
            <div style="display: flex; justify-content: space-around; flex-wrap: wrap; gap: 20px;">
                <div style="flex: 1; min-width: 200px;">
                    <div style="font-size: 2rem; margin-bottom: 10px;">1️⃣</div>
                    <strong>安装配置</strong><br>
                    下载并配置扫描器
                </div>
                <div style="flex: 1; min-width: 200px;">
                    <div style="font-size: 2rem; margin-bottom: 10px;">2️⃣</div>
                    <strong>运行扫描</strong><br>
                    执行第一次TLS扫描
                </div>
                <div style="flex: 1; min-width: 200px;">
                    <div style="font-size: 2rem; margin-bottom: 10px;">3️⃣</div>
                    <strong>分析结果</strong><br>
                    查看和分析扫描结果
                </div>
            </div>
        </div>
    </div>
</body>
</html>
