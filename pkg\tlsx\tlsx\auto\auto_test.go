//
// Author: chenjb
// Version: V1.0
// Date: 2025-06-26
// FilePath: /yaml_scan/pkg/tlsx/tlsx/auto/auto_test.go
// Description: auto包的单元测试

package auto

import (
	"testing"
	"yaml_scan/pkg/tlsx/tlsx/clients"

	"github.com/stretchr/testify/require"
)

// TestNew 测试自动回退客户端的创建功能
// 验证不同配置下自动客户端的正确创建
func TestNew(t *testing.T) {
	tests := []struct {
		name        string           // 测试用例名称
		options     *clients.Options // 输入的配置选项
		expectError bool             // 是否期望出现错误
		description string           // 测试用例描述
	}{
		{
			name: "默认配置创建自动客户端",
			options: &clients.Options{
				Timeout: 5,
				Retries: 3,
			},
			expectError: false,
			description: "使用默认配置创建自动客户端应该成功",
		},
		{
			name: "高超时配置",
			options: &clients.Options{
				Timeout: 30,
				Retries: 5,
			},
			expectError: false,
			description: "使用高超时配置创建自动客户端应该成功",
		},
		{
			name: "低重试配置",
			options: &clients.Options{
				Timeout: 5,
				Retries: 1,
			},
			expectError: false,
			description: "使用低重试配置创建自动客户端应该成功",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 执行自动客户端创建
			client, err := New(tt.options)

			if tt.expectError {
				// 期望出现错误的情况
				require.Error(t, err, "应该返回错误: %s", tt.description)
				require.Nil(t, client, "出错时client应该为nil")
			} else {
				// 期望成功的情况
				require.NoError(t, err, "不应该返回错误: %s", tt.description)
				require.NotNil(t, client, "client不应该为nil")
				require.NotNil(t, client.options, "client.options不应该为nil")

				// 验证至少有一个TLS实现可用
				hasAvailableClient := client.tlsClient != nil || 
					client.ztlsClient != nil || 
					client.opensslClient != nil
				require.True(t, hasAvailableClient, "至少应该有一个TLS实现可用")

				t.Logf("可用的TLS实现 - ctls: %v, ztls: %v, openssl: %v",
					client.tlsClient != nil,
					client.ztlsClient != nil,
					client.opensslClient != nil)
			}
		})
	}
}

// TestConnectWithOptions 测试自动回退连接功能
// 使用真实的网络地址进行连接测试
func TestConnectWithOptions(t *testing.T) {
	// 创建自动客户端
	client, err := New(&clients.Options{
		Timeout: 15,
		Retries: 2,
	})
	require.NoError(t, err, "创建自动客户端不应该出错")

	tests := []struct {
		name        string                  // 测试用例名称
		hostname    string                  // 目标主机名
		ip          string                  // 目标IP地址
		port        string                  // 目标端口
		options     clients.ConnectOptions  // 连接选项
		expectError bool                    // 是否期望出现错误
		description string                  // 测试用例描述
	}{
		{
			name:        "连接百度HTTPS",
			hostname:    "www.baidu.com",
			ip:          "**************",
			port:        "443",
			options:     clients.ConnectOptions{},
			expectError: false,
			description: "连接百度的HTTPS服务应该成功",
		},
		{
			name:     "使用SNI连接",
			hostname: "www.baidu.com",
			ip:       "**************",
			port:     "443",
			options: clients.ConnectOptions{
				SNI: "www.baidu.com",
			},
			expectError: false,
			description: "使用SNI连接应该成功",
		},
		{
			name:     "指定TLS版本连接",
			hostname: "www.baidu.com",
			ip:       "**************",
			port:     "443",
			options: clients.ConnectOptions{
				VersionTLS: "1.2",
			},
			expectError: false,
			description: "指定TLS 1.2版本连接应该成功",
		},
		{
			name:        "连接不存在的端口",
			hostname:    "www.baidu.com",
			ip:          "**************",
			port:        "9999",
			options:     clients.ConnectOptions{},
			expectError: true,
			description: "连接不存在的端口应该失败",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 执行连接
			response, err := client.ConnectWithOptions(tt.hostname, tt.ip, tt.port, tt.options)

			if tt.expectError {
				// 期望出现错误的情况
				require.Error(t, err, "应该返回错误: %s", tt.description)
			} else {
				// 期望成功的情况
				require.NoError(t, err, "不应该返回错误: %s", tt.description)
				require.NotNil(t, response, "响应不应该为nil")
				require.Equal(t, tt.port, response.Port, "端口应该匹配")
				require.Equal(t, tt.hostname, response.Host, "主机名应该匹配")
				
				// 验证TLS连接类型被正确设置
				require.NotEmpty(t, response.TLSConnection, "TLS连接类型不应该为空")
				require.Contains(t, []string{"ctls", "ztls", "openssl"}, response.TLSConnection, 
					"TLS连接类型应该是已知的实现")
				
				// 验证TLS版本和密码套件
				require.NotEmpty(t, response.Version, "TLS版本不应该为空")
				require.NotEmpty(t, response.Cipher, "密码套件不应该为空")
				
				t.Logf("连接成功 - 实现: %s, TLS版本: %s, 密码套件: %s", 
					response.TLSConnection, response.Version, response.Cipher)
			}
		})
	}
}

// TestEnumerateCiphers 测试密码套件枚举功能
// 验证自动模式下的密码套件枚举能力
func TestEnumerateCiphers(t *testing.T) {
	// 创建自动客户端
	client, err := New(&clients.Options{
		Timeout: 20,
		Retries: 2,
	})
	require.NoError(t, err, "创建自动客户端不应该出错")

	// 测试密码套件枚举
	ciphers, err := client.EnumerateCiphers("www.baidu.com", "**************", "443", clients.ConnectOptions{
		VersionTLS: "1.2",
		CipherLevel: []clients.CipherSecLevel{clients.All},
	})

	require.NoError(t, err, "密码套件枚举不应该出错")
	require.NotNil(t, ciphers, "密码套件列表不应该为nil")
	
	// 验证枚举结果
	if len(ciphers) > 0 {
		t.Logf("枚举到 %d 个密码套件", len(ciphers))
		
		// 验证密码套件名称格式
		for _, cipher := range ciphers {
			require.NotEmpty(t, cipher, "密码套件名称不应该为空")
			require.Contains(t, cipher, "TLS_", "密码套件名称应该包含TLS_前缀")
		}
		
		// 显示前几个密码套件作为示例
		maxShow := 5
		if len(ciphers) < maxShow {
			maxShow = len(ciphers)
		}
		t.Logf("示例密码套件: %v", ciphers[:maxShow])
	} else {
		t.Log("未枚举到密码套件（可能是网络或服务器配置原因）")
	}
}

// TestSupportedTLSVersions 测试支持的TLS版本获取功能
// 验证自动模式下的TLS版本支持信息
func TestSupportedTLSVersions(t *testing.T) {
	// 创建自动客户端
	client, err := New(&clients.Options{
		Timeout: 5,
		Retries: 1,
	})
	require.NoError(t, err, "创建自动客户端不应该出错")

	// 获取支持的TLS版本
	versions, err := client.SupportedTLSVersions()
	require.NoError(t, err, "获取支持的TLS版本不应该出错")
	require.NotNil(t, versions, "TLS版本列表不应该为nil")
	require.NotEmpty(t, versions, "TLS版本列表不应该为空")

	// 验证版本格式
	for _, version := range versions {
		require.NotEmpty(t, version, "TLS版本不应该为空")
		// TLS版本通常是"1.0", "1.1", "1.2", "1.3"等格式
		require.Regexp(t, `^\d+\.\d+$`, version, "TLS版本格式应该正确")
	}

	t.Logf("支持的TLS版本: %v", versions)
}

// TestSupportedTLSCiphers 测试支持的密码套件获取功能
// 验证自动模式下的密码套件支持信息
func TestSupportedTLSCiphers(t *testing.T) {
	// 创建自动客户端
	client, err := New(&clients.Options{
		Timeout: 5,
		Retries: 1,
	})
	require.NoError(t, err, "创建自动客户端不应该出错")

	// 获取支持的密码套件
	ciphers, err := client.SupportedTLSCiphers()
	require.NoError(t, err, "获取支持的密码套件不应该出错")
	require.NotNil(t, ciphers, "密码套件列表不应该为nil")
	require.NotEmpty(t, ciphers, "密码套件列表不应该为空")

	// 验证密码套件格式
	for _, cipher := range ciphers {
		require.NotEmpty(t, cipher, "密码套件名称不应该为空")
		// 大多数密码套件都以TLS_开头
		require.True(t, 
			cipher[:4] == "TLS_" || cipher[:4] == "SSL_", 
			"密码套件名称应该以TLS_或SSL_开头: %s", cipher)
	}

	t.Logf("支持的密码套件总数: %d", len(ciphers))
	
	// 显示前几个密码套件作为示例
	maxShow := 10
	if len(ciphers) < maxShow {
		maxShow = len(ciphers)
	}
	t.Logf("示例密码套件: %v", ciphers[:maxShow])
}

// TestClientRetryMechanism 测试客户端重试机制
// 验证自动模式下的重试逻辑
func TestClientRetryMechanism(t *testing.T) {
	tests := []struct {
		name        string // 测试用例名称
		retries     int    // 重试次数配置
		description string // 测试描述
	}{
		{
			name:        "低重试次数",
			retries:     1,
			description: "低重试次数应该被自动调整为至少3次",
		},
		{
			name:        "正常重试次数",
			retries:     5,
			description: "正常重试次数应该保持不变",
		},
		{
			name:        "高重试次数",
			retries:     10,
			description: "高重试次数应该保持不变",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建自动客户端
			client, err := New(&clients.Options{
				Timeout: 5,
				Retries: tt.retries,
			})
			require.NoError(t, err, "创建自动客户端不应该出错")

			// 验证重试次数配置
			require.Equal(t, tt.retries, client.options.Retries, "重试次数配置应该保持原值")
			
			t.Logf("配置的重试次数: %d", client.options.Retries)
		})
	}
}
