//
// Author: chenjb
// Version: V1.0
// Date: 2025-06-23 19:46:26
// FilePath: /yaml_scan/pkg/tlsx/tlsx/clients/clients.go
// Description: TLS客户端接口和通用功能实现

// Package clients 提供了TLS客户端的通用接口和实用功能
// 包含证书解析、指纹计算、密码套件分类等核心功能
// 支持多种TLS实现的统一接口抽象
package clients

import (
	"bytes"                                      // 字节缓冲区操作
	"crypto/md5"                                 // MD5哈希算法
	"crypto/sha1"                                // SHA1哈希算法
	"crypto/sha256"                              // SHA256哈希算法
	"crypto/x509"                                // X.509证书处理
	"encoding/hex"                               // 十六进制编码
	"encoding/pem"                               // PEM格式编码
	"math"                                       // 数学计算函数
	"strings"                                    // 字符串操作
	"time"                                       // 时间处理
	"yaml_scan/pkg/retryablehttp"               // 可重试的HTTP客户端
	"yaml_scan/pkg/tlsx/assets"                 // TLS资产和根证书
	stringsutil "yaml_scan/utils/strings"       // 字符串工具函数

	"github.com/cloudflare/cfssl/log"            // CloudFlare SSL日志库
	"github.com/cloudflare/cfssl/revoke"         // 证书撤销检查
	"github.com/logrusorgru/aurora"              // 终端颜色输出
	zasn1 "github.com/zmap/zcrypto/encoding/asn1" // ZMap ASN.1编码库
	ztls "github.com/zmap/zcrypto/tls"           // ZMap TLS库
	zpkix "github.com/zmap/zcrypto/x509/pkix"    // ZMap PKIX库
)

// Implementation 定义了所有TLS客户端必须实现的接口
// 提供统一的TLS连接、枚举和查询功能
type Implementation interface {
	// ConnectWithOptions 使用指定选项连接到目标主机
	// 执行TLS握手并返回详细的连接信息
	//
	// 参数:
	//   - hostname: 目标主机名或域名
	//   - ip: 目标IP地址
	//   - port: 目标端口号（字符串格式）
	//   - options: 连接选项，包含SNI、TLS版本、超时等配置
	//
	// 返回值:
	//   - *Response: TLS连接响应，包含证书、版本、密码套件等信息
	//   - error: 连接过程中的错误，成功时为nil
	ConnectWithOptions(hostname, ip, port string, options ConnectOptions) (*Response, error)

	// EnumerateCiphers 枚举目标主机在指定TLS版本下支持的密码套件
	// 通过尝试不同的密码套件来确定服务器的支持情况
	//
	// 参数:
	//   - hostname: 目标主机名或域名
	//   - ip: 目标IP地址
	//   - port: 目标端口号（字符串格式）
	//   - options: 连接选项，包含TLS版本和密码套件级别配置
	//
	// 返回值:
	//   - []string: 服务器支持的密码套件名称列表
	//   - error: 枚举过程中的错误，成功时为nil
	EnumerateCiphers(hostname, ip, port string, options ConnectOptions) ([]string, error)

	// SupportedTLSVersions 返回当前客户端实现支持的TLS版本列表
	// 用于确定可以进行枚举的TLS版本范围
	//
	// 返回值:
	//   - []string: 客户端支持的TLS版本列表（如"1.0", "1.1", "1.2", "1.3"）
	//   - error: 获取版本信息时的错误，成功时为nil
	SupportedTLSVersions() ([]string, error)

	// SupportedTLSCiphers 返回当前客户端实现支持的TLS密码套件列表
	// 用于确定可以进行枚举的密码套件范围
	//
	// 返回值:
	//   - []string: 客户端支持的密码套件名称列表
	//   - error: 获取密码套件信息时的错误，成功时为nil
	SupportedTLSCiphers() ([]string, error)
}

// ColorCode 返回带有彩色字符串的CipherTypes副本
// 为不同安全级别的密码套件应用不同的颜色标识
//
// 参数:
//   - a: Aurora颜色库实例，用于生成彩色文本
//
// 返回值:
//   - CipherTypes: 包含彩色字符串的密码套件类型结构
//
// 颜色方案:
//   - 弱密码套件: 亮黄色
//   - 不安全密码套件: 亮红色
//   - 安全密码套件: 亮绿色
//   - 未知密码套件: 亮紫色
func (c *CipherTypes) ColorCode(a aurora.Aurora) CipherTypes {
	ct := CipherTypes{} // 创建新的密码套件类型结构

	// 为弱密码套件添加亮黄色
	for _, v := range c.Weak {
		ct.Weak = append(ct.Weak, a.BrightYellow(v).String())
	}

	// 为不安全密码套件添加亮红色
	for _, v := range c.Insecure {
		ct.Insecure = append(ct.Insecure, a.BrightRed(v).String())
	}

	// 为安全密码套件添加亮绿色
	for _, v := range c.Secure {
		ct.Secure = append(ct.Secure, a.BrightGreen(v).String())
	}

	// 为未知密码套件添加亮紫色
	for _, v := range c.Unknown {
		ct.Unknown = append(ct.Unknown, a.BrightMagenta(v).String())
	}

	return ct
}

// IdentifyCiphers 从给定的密码套件列表中识别密码套件的安全类型
// 根据密码套件的安全级别将其分类到不同的类别中
//
// 参数:
//   - cipherList: 要分类的密码套件名称列表
//
// 返回值:
//   - CipherTypes: 按安全级别分类的密码套件结构
//
// 分类标准:
//   - Insecure: 不安全的密码套件（如使用弱加密算法）
//   - Secure: 安全的密码套件（如使用强加密算法）
//   - Weak: 弱密码套件（安全性一般）
//   - Unknown: 未知或无法分类的密码套件
func IdentifyCiphers(cipherList []string) CipherTypes {
	ct := CipherTypes{} // 初始化密码套件类型结构

	// 遍历每个密码套件并根据安全级别分类
	for _, v := range cipherList {
		switch GetCipherLevel(v) {
		case Insecure:
			// 添加到不安全密码套件列表
			ct.Insecure = append(ct.Insecure, v)
		case Secure:
			// 添加到安全密码套件列表
			ct.Secure = append(ct.Secure, v)
		case Weak:
			// 添加到弱密码套件列表
			ct.Weak = append(ct.Weak, v)
		default:
			// 添加到未知密码套件列表
			ct.Unknown = append(ct.Unknown, v)
		}
	}
	return ct
}


// CertificateDistinguishedName 表示X.509证书的可分辨名称结构
// 包含证书主体或颁发者的身份信息
type CertificateDistinguishedName struct {
	Country            []string `json:"country,omitempty"`             // 国家代码列表（如"CN", "US"）
	Organization       []string `json:"organization,omitempty"`        // 组织名称列表（如公司名）
	OrganizationalUnit []string `json:"organizational_unit,omitempty"` // 组织单位列表（如部门名）
	Locality           []string `json:"locality,omitempty"`            // 地区名称列表（如城市名）
	Province           []string `json:"province,omitempty"`            // 省份或州名列表
	StreetAddress      []string `json:"street_address,omitempty"`      // 街道地址列表
	CommonName         string   `json:"common_name,omitempty"`         // 通用名称（通常是域名或个人姓名）
}

// MD5Fingerprint 使用MD5哈希算法计算数据的指纹
// 主要用于证书指纹识别，但MD5已不推荐用于安全用途
//
// 参数:
//   - data: 要计算哈希的原始字节数据
//
// 返回值:
//   - string: 十六进制编码的MD5哈希值（32个字符）
//
// 注意: MD5算法存在安全漏洞，仅用于兼容性目的
func MD5Fingerprint(data []byte) string {
	sum := md5.Sum(data)                // 计算MD5哈希值
	return hex.EncodeToString(sum[:])   // 转换为十六进制字符串
}

// SHA1Fingerprint 使用SHA1哈希算法计算数据的指纹
// 常用于证书指纹识别，但SHA1也已不推荐用于新的安全应用
//
// 参数:
//   - data: 要计算哈希的原始字节数据
//
// 返回值:
//   - string: 十六进制编码的SHA1哈希值（40个字符）
//
// 注意: SHA1算法安全性较弱，建议使用SHA256或更强的算法
func SHA1Fingerprint(data []byte) string {
	sum := sha1.Sum(data)               // 计算SHA1哈希值
	return hex.EncodeToString(sum[:])   // 转换为十六进制字符串
}

// SHA256Fingerprint 使用SHA256哈希算法计算数据的指纹
// 这是目前推荐的证书指纹计算方法，安全性较高
//
// 参数:
//   - data: 要计算哈希的原始字节数据
//
// 返回值:
//   - string: 十六进制编码的SHA256哈希值（64个字符）
//
// 推荐: SHA256是目前广泛使用的安全哈希算法
func SHA256Fingerprint(data []byte) string {
	sum := sha256.Sum256(data)          // 计算SHA256哈希值
	return hex.EncodeToString(sum[:])   // 转换为十六进制字符串
}

// IsExpired 检查证书是否已过期
// 通过比较当前时间和证书的有效期结束时间来判断
//
// 参数:
//   - notAfter: 证书的有效期结束时间
//
// 返回值:
//   - bool: 如果证书已过期返回true，否则返回false
//
// 实现逻辑:
//   - 计算当前时间与证书过期时间的差值
//   - 如果差值为正数（当前时间晚于过期时间），则证书已过期
func IsExpired(notAfter time.Time) bool {
	// 计算从证书过期时间到现在的秒数，并四舍五入
	remaining := math.Round(time.Since(notAfter).Seconds())
	// 如果remaining > 0，说明当前时间已经超过了证书的过期时间
	return remaining > 0
}

// IsSelfSigned 检查证书是否为自签名证书
// 自签名证书是指由证书主体自己签发的证书，而不是由CA签发
//
// 参数:
//   - authorityKeyID: 证书中的颁发者密钥标识符
//   - subjectKeyID: 证书中的主体密钥标识符
//
// 返回值:
//   - bool: 如果是自签名证书返回true，否则返回false
//
// 判断逻辑:
//   - 如果颁发者密钥ID为空，通常表示自签名
//   - 如果颁发者密钥ID等于主体密钥ID，表示自己签发自己
//   - 参考: https://security.stackexchange.com/a/162263/250973
func IsSelfSigned(authorityKeyID, subjectKeyID []byte) bool {
	// 检查颁发者密钥ID是否为空，或者是否等于主体密钥ID
	if len(authorityKeyID) == 0 || bytes.Equal(authorityKeyID, subjectKeyID) {
		return true  // 满足自签名条件
	}
	return false     // 不是自签名证书
}

// IsMisMatchedCert 检查证书名称是否与目标主机名不匹配
// 支持普通域名匹配和通配符域名匹配
//
// 参数:
//   - host: 目标主机名（如"www.example.com"）
//   - alternativeNames: 证书中的所有名称列表，包括CN和SAN
//
// 返回值:
//   - bool: 如果证书名称与主机不匹配返回true，匹配则返回false
//
// 匹配规则:
//   - 普通证书: 直接比较名称和主机是否相等（忽略大小写）
//   - 通配符证书: 检查通配符模式是否匹配主机名
//   - 支持左侧通配符（*.example.com）、右侧通配符（www.*）等
func IsMisMatchedCert(host string, alternativeNames []string) bool {
	hostTokens := strings.Split(host, ".") // 将主机名按点分割成标记

	// 遍历证书中的每个备用名称
	for _, alternativeName := range alternativeNames {
		// 检查是否为通配符证书
		if !strings.Contains(alternativeName, "*") {
			// 普通证书：直接比较名称和主机是否相等（忽略大小写）
			if strings.EqualFold(alternativeName, host) {
				return false // 找到匹配，证书有效
			}
		} else {
			// 通配符证书：尝试匹配通配符模式
			nameTokens := strings.Split(alternativeName, ".") // 将证书名称按点分割

			// 只有当主机和证书名称的标记数量相同时才能匹配
			if len(hostTokens) == len(nameTokens) {
				matched := false

				// 逐个比较每个标记
				for i, token := range nameTokens {
					if i == 0 {
						// 匹配最左边的标记（可能包含通配符）
						matched = matchWildCardToken(token, hostTokens[i])
						if !matched {
							break // 第一个标记不匹配，跳出循环
						}
					} else {
						// 匹配其他标记（应该完全相等）
						matched = stringsutil.EqualFoldAny(token, hostTokens[i])
						if !matched {
							break // 标记不匹配，跳出循环
						}
					}
				}

				// 如果所有标记都匹配，则证书有效
				if matched {
					return false // 找到匹配，证书有效
				}
			}
		}
	}

	// 没有找到任何匹配的名称，证书与主机不匹配
	return true
}

// IsUntrustedCA 检查证书链中是否存在不可信的自签名CA证书
// 用于识别使用自签名CA但不在系统根证书列表中的证书链
//
// 参数:
//   - certs: X.509证书列表，通常是完整的证书链
//
// 返回值:
//   - bool: 如果存在不可信的自签名CA返回true，否则返回false
//
// 检查逻辑:
//   - 遍历证书链中的每个证书
//   - 检查证书是否为CA证书（IsCA=true）
//   - 检查是否为自签名证书
//   - 检查是否不在系统根证书列表中
//   - 满足以上条件则认为是不可信CA
func IsUntrustedCA(certs []*x509.Certificate) bool {
	for _, c := range certs {
		// 检查证书是否存在、是CA、自签名且不在根证书列表中
		if c != nil && c.IsCA && IsSelfSigned(c.AuthorityKeyId, c.SubjectKeyId) && !assets.IsRootCert(c) {
			return true // 发现不可信的自签名CA
		}
	}
	return false // 没有发现不可信的CA
}

// IsZTLSUntrustedCA 检查ZMap简化证书链中是否存在不可信的自签名CA证书
// 这是IsUntrustedCA的ZMap版本，处理ztls.SimpleCertificate类型
//
// 参数:
//   - certs: ZMap简化证书列表
//
// 返回值:
//   - bool: 如果存在不可信的自签名CA返回true，否则返回false
//
// 处理流程:
//   - 将ZMap简化证书转换为标准X.509证书
//   - 然后使用与IsUntrustedCA相同的逻辑进行检查
func IsZTLSUntrustedCA(certs []ztls.SimpleCertificate) bool {
	for _, cert := range certs {
		// 将ZMap简化证书转换为标准X.509证书
		parsedCert, _ := x509.ParseCertificate(cert.Raw)
		// 使用与IsUntrustedCA相同的检查逻辑
		if parsedCert != nil && parsedCert.IsCA && IsSelfSigned(parsedCert.AuthorityKeyId, parsedCert.SubjectKeyId) && !assets.IsRootCert(parsedCert) {
			return true // 发现不可信的自签名CA
		}
	}
	return false // 没有发现不可信的CA
}

// matchWildCardToken 匹配通配符名称标记和主机标记
// 支持多种通配符模式的匹配
//
// 参数:
//   - name: 可能包含通配符的名称标记（如"*", "www*", "*api"）
//   - host: 要匹配的主机名标记
//
// 返回值:
//   - bool: 如果通配符模式匹配主机标记返回true，否则返回false
//
// 支持的通配符模式:
//   - 前缀通配符: "*.example" 匹配 "api.example"
//   - 后缀通配符: "www.*" 匹配 "www.api"
//   - 中间通配符: "api*.com" 匹配 "api123.com"
//   - 无通配符: 直接进行大小写不敏感比较
func matchWildCardToken(name, host string) bool {
	if strings.Contains(name, "*") {
		// 按通配符分割名称
		nameSubTokens := strings.Split(name, "*")

		if strings.HasPrefix(name, "*") {
			// 前缀通配符（*.example.com）
			// 检查主机是否以通配符后的部分结尾
			return strings.HasSuffix(host, nameSubTokens[1])
		} else if strings.HasSuffix(name, "*") {
			// 后缀通配符（prefix.*）
			// 检查主机是否以通配符前的部分开头
			return strings.HasPrefix(host, nameSubTokens[0])
		} else {
			// 中间通配符（pre*.suffix）
			// 检查主机是否同时以前缀开头和后缀结尾
			return strings.HasPrefix(host, nameSubTokens[0]) &&
				strings.HasSuffix(host, nameSubTokens[1])
		}
	}
	// 没有通配符，进行大小写不敏感的直接比较
	return strings.EqualFold(name, host)
}

// IsWildCardCert 检查证书是否为通配符证书
// 通配符证书可以匹配多个子域名，提高证书的使用灵活性
//
// 参数:
//   - names: 证书中的所有名称列表（包括CN和SAN）
//
// 返回值:
//   - bool: 如果是通配符证书返回true，否则返回false
//
// 检查逻辑:
//   - 遍历证书中的所有名称
//   - 查找是否包含"*."模式的通配符名称
//   - 通配符证书示例: "*.example.com", "*.api.domain.com"
func IsWildCardCert(names []string) bool {
	for _, name := range names {
		// 检查名称中是否包含通配符模式"*."
		if strings.Contains(name, "*.") {
			return true // 发现通配符，这是通配符证书
		}
	}
	return false // 没有发现通配符，这是普通证书
}

// PemEncode 将原始DER格式的证书编码为PEM格式
// PEM格式是Base64编码的证书，便于文本传输和存储
//
// 参数:
//   - cert: DER格式的原始证书字节数据
//
// 返回值:
//   - string: PEM格式的证书字符串，编码失败时返回空字符串
//
// PEM格式特点:
//   - 以"-----BEGIN CERTIFICATE-----"开头
//   - 以"-----END CERTIFICATE-----"结尾
//   - 中间是Base64编码的证书数据
func PemEncode(cert []byte) string {
	var buf bytes.Buffer
	// 创建PEM块并编码到缓冲区
	if err := pem.Encode(&buf, &pem.Block{Type: "CERTIFICATE", Bytes: cert}); err != nil {
		return "" // 编码失败，返回空字符串
	}
	return buf.String() // 返回PEM格式的证书字符串
}

// ParseASN1DNSequenceWithZpkixOrDefault 解析ASN.1可分辨名称序列，失败时返回默认值
// 这是ParseASN1DNSequenceWithZpkix的安全包装版本
//
// 参数:
//   - data: 要解析的ASN.1编码数据
//   - defaultValue: 解析失败时返回的默认值
//
// 返回值:
//   - string: 解析成功时返回可分辨名称字符串，失败时返回默认值
//
// 使用场景:
//   - 当需要确保总是有返回值时使用
//   - 避免因解析失败导致的空值问题
func ParseASN1DNSequenceWithZpkixOrDefault(data []byte, defaultValue string) string {
	// 尝试解析ASN.1数据
	if value := ParseASN1DNSequenceWithZpkix(data); value != "" {
		return value // 解析成功，返回解析结果
	}
	return defaultValue // 解析失败，返回默认值
}

// ParseASN1DNSequenceWithZpkix 使用ZMap的zpkix和zasn1库解析TLS可分辨名称
// 这些库比标准库提供更详细的ASN.1解析能力
//
// 参数:
//   - data: 要解析的ASN.1编码的可分辨名称数据
//
// 返回值:
//   - string: 解析成功时返回可分辨名称字符串，失败时返回空字符串
//
// 优势:
//   - ZMap库包含标准库未解析的额外信息
//   - 提供更准确的可分辨名称解析
//   - 支持更多的ASN.1结构和扩展
func ParseASN1DNSequenceWithZpkix(data []byte) string {
	var rdnSequence zpkix.RDNSequence // 相对可分辨名称序列
	var name zpkix.Name               // 可分辨名称结构

	// 使用ZMap的ASN.1库解析数据
	if _, err := zasn1.Unmarshal(data, &rdnSequence); err != nil {
		return "" // 解析失败，返回空字符串
	}

	// 从RDN序列填充名称结构
	name.FillFromRDNSequence(&rdnSequence)

	// 转换为字符串格式的可分辨名称
	dnParsedString := name.String()
	return dnParsedString
}

// init 包初始化函数
// 设置第三方库的默认配置
func init() {
	// 配置CloudFlare SSL库的日志级别为错误级别，减少不必要的日志输出
	log.Level = log.LevelError

	// 配置证书撤销检查的HTTP客户端
	revoke.HTTPClient = retryablehttp.DefaultClient()           // 使用可重试的HTTP客户端
	revoke.HTTPClient.Timeout = time.Duration(5) * time.Second // 设置5秒超时
}
