//
// Author: chenjb
// Version: V1.0
// Date: 2025-06-23 20:18:11
// FilePath: /yaml_scan/pkg/tlsx/tlsx/tlsx.go
// Description:: TLS 扫描工具包主文件，用于提供TLS连接和分析功能

// Package tlsx 提供了一个统一的TLS扫描和分析工具包
// 支持多种TLS实现方式：原生TLS、ztls、openssl和自动模式
// 可以进行TLS版本枚举、密码套件枚举、JARM指纹识别等功能
package tlsx

import (
	"strconv"                                    // 字符串转换工具包，用于端口号转换
	"yaml_scan/pkg/fastdialer"                  // 快速拨号器包，提供高效的网络连接
	"yaml_scan/pkg/tlsx/tlsx/auto"              // 自动模式TLS客户端实现
	"yaml_scan/pkg/tlsx/tlsx/clients"           // TLS客户端接口和通用结构定义
	"yaml_scan/pkg/tlsx/tlsx/jarm"              // JARM指纹识别工具
	"yaml_scan/pkg/tlsx/tlsx/openssl"           // OpenSSL TLS客户端实现
	"yaml_scan/pkg/tlsx/tlsx/tls"               // 原生Go TLS客户端实现
	"yaml_scan/pkg/tlsx/tlsx/ztls"              // ztls TLS客户端实现
	errorutil "yaml_scan/utils/errors"          // 错误处理工具包
	sliceutil "yaml_scan/utils/slice"           // 切片操作工具包
)

// Service 是tlsx模块的核心服务结构体
// 封装了TLS扫描的所有功能，包括连接、版本枚举、密码套件枚举等
type Service struct {
	options *clients.Options       // TLS连接的配置选项，包含超时、重试、扫描模式等参数
	client  clients.Implementation // 具体的TLS客户端实现，根据扫描模式选择不同的实现
}

// New 创建一个新的tlsx服务实例
// 根据提供的配置选项初始化相应的TLS客户端实现
//
// 参数:
//   - options: TLS连接的配置选项，包含以下重要字段：
//     * ScanMode: 扫描模式 ("ztls", "ctls", "openssl", "auto")
//     * Timeout: 连接超时时间
//     * Retries: 重试次数
//     * Fastdialer: 快速拨号器实例（可选）
//
// 返回值:
//   - *Service: 创建的tlsx服务实例，包含选定的TLS客户端实现
//   - error: 创建过程中的错误，如果成功则为nil
//
// 功能说明:
//   - 如果未提供Fastdialer，会自动创建一个默认的拨号器
//   - 根据ScanMode选择对应的TLS客户端实现
//   - 支持四种扫描模式：ztls、ctls、openssl、auto
//   - 默认使用ctls（原生Go TLS）模式
func New(options *clients.Options) (*Service, error) {
	// 创建服务实例并保存配置选项
	service := &Service{
		options: options,
	}

	// 检查是否提供了快速拨号器，如果没有则创建默认的拨号器
	if options.Fastdialer == nil {
		var err error
		// 使用默认配置创建快速拨号器
		options.Fastdialer, err = fastdialer.NewDialer(fastdialer.DefaultOptions)
		if err != nil {
			// 拨号器创建失败，返回错误
			return nil, err
		}
	}

	var err error
	// 根据配置的扫描模式选择合适的TLS客户端实现
	switch options.ScanMode {
	case "ztls":
		// 使用ztls实现，提供更好的TLS指纹识别能力
		service.client, err = ztls.New(options)
	case "ctls":
		// 使用Go原生TLS实现，稳定性好
		service.client, err = tls.New(options)
	case "openssl":
		// 使用OpenSSL实现，兼容性强
		service.client, err = openssl.New(options)
	case "auto":
		// 使用自动模式，会根据情况自动选择最佳实现
		service.client, err = auto.New(options)
	default:
		// 默认情况下使用原生TLS实现
		service.client, err = tls.New(options)
		options.ScanMode = "ctls" // 更新扫描模式为ctls
	}

	// 检查客户端创建是否成功
	if err != nil {
		return nil, errorutil.NewWithTag("auto", "could not create tls service").Wrap(err)
	}

	return service, nil
}

// Connect 连接到指定的主机和端口，使用默认连接选项
// 这是ConnectWithOptions的简化版本，使用空的连接选项
//
// 参数:
//   - host: 目标主机名或域名
//   - ip: 目标IP地址
//   - port: 目标端口号（字符串格式）
//
// 返回值:
//   - *clients.Response: TLS连接响应信息，包含证书、版本等详细信息
//   - error: 连接过程中的错误，如果成功则为nil
func (s *Service) Connect(host, ip, port string) (*clients.Response, error) {
	// 调用带选项的连接方法，使用空的连接选项
	return s.ConnectWithOptions(host, ip, port, clients.ConnectOptions{})
}

// ConnectWithOptions 使用自定义选项连接到指定的主机和端口
// 支持TLS版本枚举、密码套件枚举、JARM指纹识别等高级功能
//
// 参数:
//   - host: 目标主机名或域名
//   - ip: 目标IP地址
//   - port: 目标端口号（字符串格式）
//   - options: 连接选项，包含SNI、TLS版本、枚举模式等配置
//
// 返回值:
//   - *clients.Response: TLS连接响应信息，包含以下主要字段：
//     * Host: 主机名
//     * Port: 端口号
//     * Version: TLS版本
//     * Certificate: 证书信息
//     * JarmHash: JARM指纹（如果启用）
//     * VersionEnum: 支持的TLS版本列表（如果启用枚举）
//     * TlsCiphers: 支持的密码套件（如果启用枚举）
//   - error: 连接过程中的错误，如果成功则为nil
func (s *Service) ConnectWithOptions(host, ip, port string, options clients.ConnectOptions) (*clients.Response, error) {
	var resp *clients.Response // TLS连接响应结果
	var err error              // 错误信息

	// 输入参数验证：确保提供了有效的地址和端口
	if (host == "" && ip == "") || port == "" {
		return nil, errorutil.NewWithTag("tlsx", "tlsx requires valid address got port=%v,hostname=%v,ip=%v", port, host, ip)
	}

	// 根据扫描模式决定是否进行重试
	if s.options.ScanMode != "auto" && s.options.ScanMode != "" {
		// 非auto模式需要进行重试，因为auto模式内部已经有fallback机制
		// 这可以被视为重试机制的一部分
		for i := 0; i < s.options.Retries; i++ {
			// 尝试连接，如果获得响应则跳出循环
			if resp, err = s.client.ConnectWithOptions(host, ip, port, options); resp != nil {
				err = nil // 成功获得响应，清除错误
				break
			}
		}
	} else {
		// auto模式或空模式，只尝试一次连接
		if resp, err = s.client.ConnectWithOptions(host, ip, port, options); resp != nil {
			err = nil // 成功获得响应，清除错误
		}
	}

	// 检查连接结果：如果既没有响应也没有错误，说明出现异常情况
	if resp == nil && err == nil {
		return nil, errorutil.NewWithTag("auto", "no response returned for connection")
	}

	// 处理连接错误
	if err != nil {
		wrappedErr := errorutil.NewWithTag("auto", "could not connect to host").Wrap(err)
		// 如果启用了探测状态，即使连接失败也返回基本响应信息
		if s.options.ProbeStatus {
			return &clients.Response{Host: host, Port: port, Error: err.Error(), ProbeStatus: false, ServerName: options.SNI}, wrappedErr
		}
		return nil, wrappedErr
	}

	// 如果启用了JARM指纹识别，计算JARM哈希值
	if s.options.Jarm {
		// 将端口字符串转换为整数（忽略转换错误）
		port, _ := strconv.Atoi(port)
		// 使用快速拨号器计算JARM指纹哈希
		jarmhash, err := jarm.HashWithDialer(s.options.Fastdialer, host, port, s.options.Timeout)
		if err != nil {
			// JARM计算失败，但仍返回基本响应信息
			return resp, err
		}
		// 将JARM哈希添加到响应中
		resp.JarmHash = jarmhash
	}

	// 如果启用了TLS版本枚举，枚举所有支持的TLS版本
	if s.options.TlsVersionsEnum {
		options.EnumMode = clients.Version                        // 设置枚举模式为版本枚举
		supportedTlsVersions := []string{resp.Version}            // 初始化支持的版本列表，包含当前连接的版本
		enumeratedTlsVersions, _ := s.enumTlsVersions(host, ip, port, options) // 枚举其他支持的版本
		supportedTlsVersions = append(supportedTlsVersions, enumeratedTlsVersions...) // 合并版本列表
		resp.VersionEnum = sliceutil.Dedupe(supportedTlsVersions) // 去重并保存到响应中
	}

	// 如果启用了TLS密码套件枚举，为每个支持的TLS版本枚举密码套件
	var supportedTlsCiphers []clients.TlsCiphers
	if s.options.TlsCiphersEnum {
		options.EnumMode = clients.Cipher // 设置枚举模式为密码套件枚举
		// 遍历每个支持的TLS版本
		for _, supportedTlsVersion := range resp.VersionEnum {
			options.VersionTLS = supportedTlsVersion // 设置当前要测试的TLS版本
			// 枚举该版本支持的密码套件
			enumeratedTlsCiphers, _ := s.enumTlsCiphers(host, ip, port, options)
			enumeratedTlsCiphers = sliceutil.Dedupe(enumeratedTlsCiphers) // 去重密码套件列表
			cipherTypes := clients.IdentifyCiphers(enumeratedTlsCiphers)  // 识别密码套件类型（安全级别等）
			// 将该版本的密码套件信息添加到结果中
			supportedTlsCiphers = append(supportedTlsCiphers, clients.TlsCiphers{Version: supportedTlsVersion, Ciphers: cipherTypes})
		}
		resp.TlsCiphers = supportedTlsCiphers // 保存所有密码套件信息到响应中
	}
	return resp, nil
}

// enumTlsVersions 枚举目标主机支持的所有TLS版本
// 通过尝试连接不同的TLS版本来确定服务器的支持情况
//
// 参数:
//   - host: 目标主机名或域名
//   - ip: 目标IP地址
//   - port: 目标端口号（字符串格式）
//   - options: 连接选项，包含枚举相关配置
//
// 返回值:
//   - []string: 服务器支持的TLS版本列表
//   - error: 枚举过程中的错误，如果成功则为nil
//
// 功能说明:
//   - 获取客户端支持的所有TLS版本
//   - 逐一尝试每个版本的连接
//   - 只有连接成功且返回的版本匹配时才认为服务器支持该版本
func (s *Service) enumTlsVersions(host, ip, port string, options clients.ConnectOptions) ([]string, error) {
	var enumeratedTlsVersions []string // 存储枚举到的TLS版本

	// 获取客户端支持的所有TLS版本
	clientSupportedTlsVersions, err := s.client.SupportedTLSVersions()
	if err != nil {
		return nil, err
	}

	// 遍历每个客户端支持的TLS版本
	for _, tlsVersion := range clientSupportedTlsVersions {
		options.VersionTLS = tlsVersion // 设置要测试的TLS版本
		// 尝试使用指定版本连接
		if resp, err := s.client.ConnectWithOptions(host, ip, port, options); err == nil && resp != nil && resp.Version == tlsVersion {
			// 连接成功且返回的版本匹配，说明服务器支持该版本
			enumeratedTlsVersions = append(enumeratedTlsVersions, tlsVersion)
		}
	}
	return enumeratedTlsVersions, nil
}

// enumTlsCiphers 枚举指定TLS版本下服务器支持的密码套件
// 根据配置的密码套件安全级别进行枚举
//
// 参数:
//   - host: 目标主机名或域名
//   - ip: 目标IP地址
//   - port: 目标端口号（字符串格式）
//   - options: 连接选项，包含TLS版本和枚举配置
//
// 返回值:
//   - []string: 服务器支持的密码套件列表
//   - error: 枚举过程中的错误，如果成功则为nil
//
// 功能说明:
//   - 根据配置的密码套件级别设置枚举范围
//   - 支持weak（弱）、secure（安全）、insecure（不安全）、all（全部）四种级别
//   - 调用客户端的密码套件枚举功能
func (s *Service) enumTlsCiphers(host, ip, port string, options clients.ConnectOptions) ([]string, error) {
	options.EnumMode = clients.Cipher // 设置枚举模式为密码套件枚举

	// 根据配置的密码套件级别设置枚举范围
	for _, cipher := range s.options.TLsCipherLevel {
		switch cipher {
		case "weak":
			// 添加弱密码套件到枚举范围
			options.CipherLevel = append(options.CipherLevel, clients.Weak)
		case "secure":
			// 添加安全密码套件到枚举范围
			options.CipherLevel = append(options.CipherLevel, clients.Secure)
		case "insecure":
			// 添加不安全密码套件到枚举范围
			options.CipherLevel = append(options.CipherLevel, clients.Insecure)
		default:
			// 默认添加所有密码套件到枚举范围
			options.CipherLevel = append(options.CipherLevel, clients.All)
		}
	}

	// 调用客户端的密码套件枚举功能
	return s.client.EnumerateCiphers(host, ip, port, options)
}
