//
// Author: chenjb
// Version: V1.0
// Date: 2025-06-24 16:12:42
// FilePath: /yaml_scan/pkg/tlsx/tlsx/auto/auto.go
// Description: 自动回退TLS客户端实现

// Package auto 提供自动回退的TLS客户端实现
// 集成多种TLS实现（ctls、ztls、openssl），自动选择最佳可用的实现
// 当一种实现失败时，自动尝试其他实现，提高连接成功率
package auto

import (
	"sync"                                   // 并发同步原语
	"yaml_scan/pkg/tlsx/output/stats"       // 统计信息收集
	"yaml_scan/pkg/tlsx/tlsx/clients"       // TLS客户端接口定义
	"yaml_scan/pkg/tlsx/tlsx/openssl"       // OpenSSL TLS实现
	"yaml_scan/pkg/tlsx/tlsx/tls"           // 原生Go TLS实现
	"yaml_scan/pkg/tlsx/tlsx/ztls"          // ZMap TLS实现

	errorutils "yaml_scan/utils/errors"     // 错误处理工具
	sliceutil "yaml_scan/utils/slice"       // 切片操作工具

	"go.uber.org/multierr"                  // 多错误组合库
)

// Client 是使用自动回退机制的TLS抓取客户端
// 内部集成了三种不同的TLS实现，根据可用性和成功率自动选择
type Client struct {
	tlsClient     *tls.Client     // 原生Go TLS客户端实现
	ztlsClient    *ztls.Client    // ZMap TLS客户端实现，提供更详细的TLS信息
	opensslClient *openssl.Client // OpenSSL TLS客户端实现，兼容性最好
	options       *clients.Options // 共享的配置选项
}

// New 创建一个使用自动回退机制的TLS抓取客户端
// 尝试初始化所有可用的TLS实现，至少需要一个实现成功才能创建客户端
//
// 参数:
//   - options: TLS连接配置选项，将传递给所有底层实现
//
// 返回值:
//   - *Client: 自动回退TLS客户端实例
//   - error: 如果所有TLS实现都初始化失败则返回组合错误
//
// 初始化策略:
//   - 同时尝试初始化ctls、ztls、openssl三种实现
//   - 允许部分实现失败，只要有一个成功即可
//   - OpenSSL不可用错误被视为正常情况（系统可能未安装OpenSSL）
//   - 只有当所有实现都失败时才返回错误
func New(options *clients.Options) (*Client, error) {
	// 尝试初始化所有三种TLS实现
	tlsClient, tlsErr := tls.New(options)           // 原生Go TLS实现
	ztlsClient, ztlsErr := ztls.New(options)        // ZMap TLS实现
	opensslClient, opensslErr := openssl.New(options) // OpenSSL实现

	// 检查是否至少有一个实现成功初始化
	// OpenSSL不可用错误不算作失败，因为系统可能未安装OpenSSL
	if tlsErr != nil && ztlsErr != nil && (opensslErr != nil && !errorutils.IsAny(opensslErr, openssl.ErrNotAvailable)) {
		// 所有实现都失败，返回组合错误
		return nil, multierr.Combine(tlsErr, ztlsErr, opensslErr)
	}

	// 创建自动回退客户端，即使某些实现为nil也没关系
	return &Client{
		tlsClient:     tlsClient,
		ztlsClient:    ztlsClient,
		opensslClient: opensslClient,
		options:       options,
	}, nil
}

// ConnectWithOptions 使用自动回退机制连接到目标主机
// 按优先级顺序尝试不同的TLS实现，直到成功或达到最大重试次数
//
// 参数:
//   - hostname: 目标主机名或域名
//   - ip: 目标IP地址
//   - port: 目标端口号
//   - options: 连接选项配置
//
// 返回值:
//   - *clients.Response: TLS连接响应，包含使用的实现类型标识
//   - error: 所有实现都失败时返回组合错误
//
// 回退策略:
//   - 优先级: ctls -> ztls -> openssl
//   - 每个实现失败后立即尝试下一个
//   - 最少重试3次，确保有足够的尝试机会
//   - 记录成功连接的统计信息
//   - 在响应中标记使用的TLS实现类型
func (c *Client) ConnectWithOptions(hostname, ip, port string, options clients.ConnectOptions) (*clients.Response, error) {
	var response *clients.Response                    // 存储连接响应
	var err, ztlsErr, opensslErr error               // 各实现的错误信息

	// 确定最大重试次数，至少为3次
	maxRetries := c.options.Retries
	if maxRetries < 3 {
		maxRetries = 3 // 自动模式需要更多重试机会
	}

	retryCounter := 0 // 重试计数器

	// 安全检查：确保至少有一个可用的TLS实现
	if c.tlsClient == nil && c.ztlsClient == nil && c.opensslClient == nil {
		return nil, errorutils.New("no tls client available available for auto mode")
	}

	var errStack error // 累积所有错误信息

	// 重试循环：尝试所有可用的TLS实现
	for retryCounter < maxRetries {
		// 第一优先级：尝试原生Go TLS实现
		if c.tlsClient != nil {
			if response, err = c.tlsClient.ConnectWithOptions(hostname, ip, port, options); err == nil {
				response.TLSConnection = "ctls"           // 标记使用的实现类型
				stats.IncrementCryptoTLSConnections()     // 更新统计信息
				return response, nil                      // 连接成功，立即返回
			}
			retryCounter++ // 增加重试计数
		}

		// 第二优先级：尝试ZMap TLS实现
		if c.ztlsClient != nil {
			if response, ztlsErr = c.ztlsClient.ConnectWithOptions(hostname, ip, port, options); ztlsErr == nil {
				response.TLSConnection = "ztls"           // 标记使用的实现类型
				stats.IncrementZcryptoTLSConnections()    // 更新统计信息
				return response, nil                      // 连接成功，立即返回
			}
			retryCounter++ // 增加重试计数
		}

		// 第三优先级：尝试OpenSSL实现
		if c.opensslClient != nil {
			if response, opensslErr = c.opensslClient.ConnectWithOptions(hostname, ip, port, options); opensslErr == nil {
				response.TLSConnection = "openssl"       // 标记使用的实现类型
				stats.IncrementOpensslTLSConnections()   // 更新统计信息
				return response, nil                     // 连接成功，立即返回
			}

			// 如果OpenSSL不可用，不将其视为错误
			if errorutils.IsAny(opensslErr, openssl.ErrNotAvailable) {
				opensslErr = nil
			}
			retryCounter++ // 增加重试计数
		}

		// 累积本轮的所有错误
		errStack = multierr.Combine(errStack, err, ztlsErr, opensslErr)
	}

	// 所有实现和重试都失败，返回累积的错误信息
	return nil, errStack
}

// EnumerateCiphers 使用所有可用的TLS实现并发枚举密码套件
// 通过并行执行多个实现的密码套件枚举，获得最全面的结果
//
// 参数:
//   - hostname: 目标主机名或域名
//   - ip: 目标IP地址
//   - port: 目标端口号
//   - options: 连接选项，包含TLS版本和密码套件级别配置
//
// 返回值:
//   - []string: 去重后的密码套件名称列表，包含所有实现发现的密码套件
//   - error: 枚举过程中的错误（当前实现总是返回nil）
//
// 并发策略:
//   - 同时启动所有可用TLS实现的密码套件枚举
//   - 使用goroutine并行执行，提高枚举效率
//   - 使用互斥锁保护共享的结果切片
//   - 合并所有实现的结果并去重
//   - 优先级顺序：openssl -> ztls -> ctls（OpenSSL通常发现最多密码套件）
func (c *Client) EnumerateCiphers(hostname, ip, port string, options clients.ConnectOptions) ([]string, error) {
	wg := &sync.WaitGroup{}           // 等待组，用于同步所有goroutine
	ciphersFound := []string{}        // 存储发现的密码套件
	cipherMutex := &sync.Mutex{}      // 互斥锁，保护共享数据
	allClients := []clients.Implementation{} // 可用的TLS实现列表

	// 按优先级顺序收集可用的TLS实现
	// OpenSSL优先，因为它通常能发现最多的密码套件
	if c.opensslClient != nil {
		allClients = append(allClients, c.opensslClient)
	}
	if c.ztlsClient != nil {
		allClients = append(allClients, c.ztlsClient)
	}
	if c.tlsClient != nil {
		allClients = append(allClients, c.tlsClient)
	}

	// 为每个可用的TLS实现启动并发的密码套件枚举
	for _, v := range allClients {
		wg.Add(1) // 增加等待组计数
		go func(clientx clients.Implementation) {
			defer wg.Done() // 确保在goroutine结束时减少等待组计数

			// 调用具体实现的密码套件枚举方法
			if res, _ := clientx.EnumerateCiphers(hostname, ip, port, options); len(res) > 0 {
				// 使用互斥锁保护共享数据的并发访问
				cipherMutex.Lock()
				ciphersFound = append(ciphersFound, res...) // 合并结果
				cipherMutex.Unlock()
			}
		}(v) // 传递当前实现到goroutine
	}

	// 等待所有goroutine完成
	wg.Wait()

	// 去重并返回最终结果
	return sliceutil.Dedupe(ciphersFound), nil
}

// SupportedTLSVersions 返回所有TLS实现支持的TLS版本列表
// 提供统一的TLS版本支持信息，用于版本枚举功能
//
// 返回值:
//   - []string: 支持的TLS版本列表（如["1.0", "1.1", "1.2", "1.3"]）
//   - error: 获取版本信息时的错误（当前实现总是返回nil）
func (c *Client) SupportedTLSVersions() ([]string, error) {
	return supportedTlsVersions, nil
}

// SupportedTLSCiphers 返回所有TLS实现支持的密码套件列表
// 提供统一的密码套件支持信息，用于密码套件枚举功能
//
// 返回值:
//   - []string: 支持的密码套件名称列表
//   - error: 获取密码套件信息时的错误（当前实现总是返回nil）
func (c *Client) SupportedTLSCiphers() ([]string, error) {
	return allCiphersNames, nil
}