//
// Author: chenjb
// Version: V1.0
// Date: 2025-06-24 14:54:25
// FilePath: /yaml_scan/pkg/tlsx/tlsx/ztls/ztls.go
// Description: ZMap TLS库实现的TLS客户端

// Package ztls 提供基于ZMap zcrypto/tls库的TLS客户端实现
// ZMap TLS库提供了比标准库更详细的TLS协议分析能力
// 支持更多的TLS版本（包括SSL 3.0）和更详细的握手信息收集
package ztls

import (
	"context"                           // 上下文控制
	"errors"                            // 错误处理
	"fmt"                               // 格式化输出
	"net"                               // 网络操作
	"os"                                // 操作系统接口
	"time"                              // 时间处理
	"yaml_scan/pkg/fastdialer"          // 快速拨号器
	"yaml_scan/pkg/tlsx/output/stats"   // 统计信息收集
	"yaml_scan/pkg/gologger"            // 日志记录器
	"yaml_scan/pkg/tlsx/tlsx/clients"   // TLS客户端接口
	"yaml_scan/pkg/tlsx/tlsx/ztls/ja3"  // JA3指纹识别
	iputil "yaml_scan/utils/ip"         // IP地址工具
	stringsutil "yaml_scan/utils/strings" // 字符串工具
	"yaml_scan/utils/conn"              // 连接池工具

	"github.com/rs/xid"                 // 唯一ID生成器
	"github.com/zmap/zcrypto/encoding/asn1" // ZMap ASN.1编码库
	"github.com/zmap/zcrypto/tls"       // ZMap TLS库
	"github.com/zmap/zcrypto/x509"      // ZMap X.509证书库
	errorutil "yaml_scan/utils/errors"  // 错误处理工具
)

// init 包初始化函数
// 启用ZMap ASN.1库的宽松解析模式，提高证书解析的兼容性
func init() {
	// 允许宽松的ASN.1解析，可以处理一些格式不严格的证书
	asn1.AllowPermissiveParsing = true
}

// Client 是基于ZMap zcrypto/tls库的TLS客户端实现
// 提供比标准库更详细的TLS协议分析和证书信息提取能力
type Client struct {
	dialer    *fastdialer.Dialer // 用于创建网络连接的快速拨号器
	tlsConfig *tls.Config        // ZMap TLS配置，支持更多选项
	options   *clients.Options   // TLS扫描选项和参数
}

// versionStringToTLSVersion TLS版本字符串到ZMap TLS版本号的映射表
// ZMap库支持更多的TLS版本，包括已弃用的SSL 3.0
var versionStringToTLSVersion = map[string]uint16{
	"ssl30": tls.VersionSSL30, // SSL 3.0版本（已弃用，但仍可用于兼容性测试）
	"tls10": tls.VersionTLS10, // TLS 1.0版本
	"tls11": tls.VersionTLS11, // TLS 1.1版本
	"tls12": tls.VersionTLS12, // TLS 1.2版本
}

// versionToTLSVersionString ZMap TLS版本号到版本字符串的映射表
// 用于将ZMap TLS库的版本常量转换为可读的版本字符串
var versionToTLSVersionString = map[uint16]string{
	tls.VersionSSL30: "ssl30", // SSL 3.0版本（已弃用）
	tls.VersionTLS10: "tls10", // TLS 1.0版本
	tls.VersionTLS11: "tls11", // TLS 1.1版本
	tls.VersionTLS12: "tls12", // TLS 1.2版本
}

// New 创建一个新的基于ZMap zcrypto/tls库的TLS客户端
// ZMap库提供比标准库更详细的TLS分析能力和更广泛的协议支持
//
// 参数:
//   - options: TLS连接配置选项，包含各种扫描和连接参数
//
// 返回值:
//   - *Client: 配置完成的ZMap TLS客户端实例
//   - error: 配置过程中的错误，成功时为nil
//
// ZMap库特点:
//   - 支持SSL 3.0到TLS 1.2的所有版本（包括已弃用版本）
//   - 提供更详细的TLS握手信息
//   - 支持仅获取证书模式（CertsOnly）
//   - 更强的证书解析兼容性
func New(options *clients.Options) (*Client, error) {
	// 创建ZMap TLS客户端结构
	c := &Client{
		dialer: options.Fastdialer, // 使用提供的快速拨号器
		tlsConfig: &tls.Config{
			CertsOnly:          options.CertsOnly,                // 是否仅获取证书（提前终止握手）
			MinVersion:         tls.VersionSSL30,                 // 最小支持SSL 3.0（用于兼容性测试）
			MaxVersion:         tls.VersionTLS12,                 // 最大支持TLS 1.2（ZMap库当前限制）
			InsecureSkipVerify: !options.VerifyServerCertificate, // 根据选项决定是否跳过证书验证
		},
		options: options, // 保存原始配置选项
	}

	// 配置密码套件
	if len(options.Ciphers) > 0 {
		// 用户指定了自定义密码套件，转换为ZMap格式
		if customCiphers, err := toZTLSCiphers(options.Ciphers); err != nil {
			return nil, errorutil.NewWithTag("ztls", "could not get ztls ciphers").Wrap(err)
		} else {
			c.tlsConfig.CipherSuites = customCiphers // 使用自定义密码套件
		}
	} else {
		// 未指定密码套件，使用ZMap库支持的所有密码套件
		// 注意：Go标准库默认只广播安全/默认的密码套件列表
		// ZMap库支持更多的密码套件，包括一些已弃用的套件
		c.tlsConfig.CipherSuites = AllCiphers
	}

	// 配置自定义CA证书（如果提供）
	if options.CACertificate != "" {
		// 读取CA证书文件
		caCert, err := os.ReadFile(options.CACertificate)
		if err != nil {
			return nil, errorutil.NewWithTag("ztls", "could not read ca certificate").Wrap(err)
		}

		// 创建新的证书池并添加自定义CA证书
		certPool := x509.NewCertPool()
		if !certPool.AppendCertsFromPEM(caCert) {
			// CA证书解析失败，记录错误但不中断执行
			gologger.Error().Msgf("Could not append parsed ca-cert to config!")
		}
		c.tlsConfig.RootCAs = certPool
	}
	if options.MinVersion != "" {
		version, ok := versionStringToTLSVersion[options.MinVersion]
		if !ok {
			return nil, fmt.Errorf("invalid min version specified: %s", options.MinVersion)
		} else {
			c.tlsConfig.MinVersion = version
		}
	}
	if options.MaxVersion != "" {
		version, ok := versionStringToTLSVersion[options.MaxVersion]
		if !ok {
			return nil, fmt.Errorf("invalid max version specified: %s", options.MaxVersion)
		} else {
			c.tlsConfig.MaxVersion = version
		}
	}
	return c, nil
}

// ConnectWithOptions 使用指定选项连接到目标主机并获取TLS响应数据
// 通过ZMap zcrypto/tls库执行TLS握手并收集详细的握手信息
//
// 参数:
//   - hostname: 目标主机名或域名
//   - ip: 目标IP地址
//   - port: 目标端口号
//   - options: 连接选项，包含SNI、TLS版本等配置
//
// 返回值:
//   - *clients.Response: TLS连接响应，包含证书信息、握手详情和JA3指纹
//   - error: 连接过程中的错误，成功时为nil
//
// ZMap库特色功能:
//   - 详细的握手日志记录
//   - JA3/JA3S指纹生成
//   - 支持仅获取证书模式
//   - 更强的证书解析兼容性
func (c *Client) ConnectWithOptions(hostname, ip, port string, options clients.ConnectOptions) (*clients.Response, error) {
	// 根据输入参数生成ZMap TLS配置
	// 包括SNI设置、TLS版本选择、密码套件配置等
	config, err := c.getConfig(hostname, ip, port, options)
	if err != nil {
		return nil, errorutil.NewWithErr(err).Msgf("failed to create ztls config")
	}

	// 创建带超时的上下文
	ctx := context.Background()
	if c.options.Timeout != 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, time.Duration(c.options.Timeout)*time.Second)
		defer cancel()
	}

	// 建立TCP连接
	// 使用clients.GetConn统一的连接建立逻辑
	conn, err := clients.GetConn(ctx, hostname, ip, port, c.options)
	if err != nil {
		return nil, errorutil.NewWithErr(err).Msgf("failed to setup connection").WithTag("ztls")
	}
	defer conn.Close() // 内部由conn.Close()处理，这里只是占位符

	// 获取实际解析到的IP地址
	resolvedIP, _, err := net.SplitHostPort(conn.RemoteAddr().String())
	if err != nil {
		return nil, err
	}

	var clientCertRequired bool // 标记服务器是否要求客户端证书

	// 创建ZMap TLS连接并执行握手
	tlsConn := tls.Client(conn, config)
	err = c.tlsHandshakeWithTimeout(tlsConn, ctx)
	if err != nil {
		// 检查错误是否由于缺少客户端证书引起
		if clients.IsClientCertRequiredError(err) {
			clientCertRequired = true
		} else {
			return nil, errorutil.NewWithTag("ztls", "could not do tls handshake").Wrap(err)
		}
	}
	defer tlsConn.Close()

	// 获取ZMap TLS库的详细握手日志
	// 包含ClientHello、ServerHello、证书链等完整信息
	hl := tlsConn.GetHandshakeLog()
	now := time.Now()

	// 构建TLS连接响应结构
	response := &clients.Response{
		Timestamp:     &now,            // 响应生成时间戳
		Host:          hostname,        // 目标主机名
		IP:            resolvedIP,      // 实际连接的IP地址
		ProbeStatus:   true,            // 探测状态：成功
		Port:          port,            // 目标端口号
		TLSConnection: "ztls",          // 标识使用ZMap TLS实现
		ServerName:    config.ServerName, // 使用的SNI
	}

	// 处理服务器证书信息
	if hl != nil && hl.ServerCertificates != nil {
		// 转换服务器叶证书为响应格式
		response.CertificateResponse = ConvertCertificateToResponse(c.options, hostname, ParseSimpleTLSCertificate(hl.ServerCertificates.Certificate))
		if response.CertificateResponse != nil {
			// 检查证书链的信任状态
			response.Untrusted = clients.IsZTLSUntrustedCA(hl.ServerCertificates.Chain)
		}
	}

	// 处理服务器Hello信息
	if hl.ServerHello != nil {
		// 提取协商的TLS版本和密码套件
		response.Version = versionToTLSVersionString[uint16(hl.ServerHello.Version)]
		response.Cipher = hl.ServerHello.CipherSuite.String()
	}

	// 处理完整证书链（如果请求）
	if c.options.TLSChain {
		for _, cert := range hl.ServerCertificates.Chain {
			response.Chain = append(response.Chain, ConvertCertificateToResponse(c.options, hostname, ParseSimpleTLSCertificate(cert)))
		}
	}

	// 生成JA3客户端指纹（如果请求）
	if c.options.Ja3 {
		response.Ja3Hash = ja3.GetJa3Hash(hl.ClientHello)
	}

	// 生成JA3S服务器指纹（如果请求）
	if c.options.Ja3s {
		response.Ja3sHash = ja3.GetJa3sHash(hl.ServerHello)
	}

	// 包含原始ClientHello数据（如果请求）
	if c.options.ClientHello {
		response.ClientHello = hl.ClientHello
	}

	// 包含原始ServerHello数据（如果请求）
	if c.options.ServerHello {
		response.ServerHello = hl.ServerHello
	}

	// crypto/tls allows for completing the handshake without a client certificate being provided even if one is required
	// and doesn't return an error until the underyling connection is actually used. As a result, we will temporarily
	// skip setting ClientCertRequired for TLS 1.3 servers since we don't yet know at this stage whether or not
	// a client certificate is required.
	//
	// Note: ztls currently doesn't support TLS 1.3 but we are adding this here just to be cautious in case it is added
	// at a future date.
	if response.Version != "tls13" {
		response.ClientCertRequired = &clientCertRequired
	}

	return response, nil
}

// EnumerateCiphers 使用ZMap TLS库枚举目标服务器支持的密码套件
// 通过并发连接池逐个测试不同密码套件与目标服务器的兼容性
//
// 参数:
//   - hostname: 目标主机名或域名
//   - ip: 目标IP地址
//   - port: 目标端口号
//   - options: 连接选项，包含TLS版本、安全级别等配置
//
// 返回值:
//   - []string: 目标服务器支持的密码套件名称列表
//   - error: 枚举过程中的错误，成功时为nil
//
// ZMap库枚举特点:
//   - 使用连接池提高枚举效率
//   - 支持并发密码套件测试
//   - 提供详细的握手日志
//   - 支持更多的密码套件（包括已弃用套件）
func (c *Client) EnumerateCiphers(hostname, ip, port string, options clients.ConnectOptions) ([]string, error) {
	// 根据指定的安全级别筛选要测试的密码套件
	// 支持的级别：All（全部）、Secure（安全）、Weak（弱）、Insecure（不安全）
	toEnumerate := clients.GetCiphersWithLevel(AllCiphersNames, options.CipherLevel...)

	enumeratedCiphers := []string{} // 存储枚举成功的密码套件

	// 构建网络连接地址
	// 优先使用IP地址，否则使用hostname
	var address string
	if iputil.IsIP(ip) && (c.options.ScanAllIPs || len(c.options.IPVersion) > 0) {
		address = net.JoinHostPort(ip, port)
	} else {
		address = net.JoinHostPort(hostname, port)
	}

	// 确定并发线程数
	// 不超过要测试的密码套件数量
	threads := c.options.CipherConcurrency
	if len(toEnumerate) < threads {
		threads = len(toEnumerate)
	}

	// 建立一次性连接池以提高枚举效率
	// 连接池允许复用TCP连接，减少连接建立开销
	pool, err := connpool.NewOneTimePool(context.Background(), address, threads)
	if err != nil {
		return enumeratedCiphers, errorutil.NewWithErr(err).Msgf("failed to setup connection pool")
	}
	pool.Dialer = c.dialer // 使用自定义拨号器

	// 启动连接池后台运行
	go func() {
		if err := pool.Run(); err != nil && !errors.Is(err, context.Canceled) {
			gologger.Error().Msgf("tlsx: ztls: failed to run connection pool: %v", err)
		}
	}()
	defer pool.Close()

	// 创建ZMap TLS基础配置
	baseCfg, err := c.getConfig(hostname, ip, port, options)
	if err != nil {
		return enumeratedCiphers, errorutil.NewWithErr(err).Msgf("failed to setup cfg")
	}
	gologger.Debug().Label("ztls").Msgf("Starting cipher enumeration with %v ciphers in %v", len(toEnumerate), options.VersionTLS)

	// 逐个测试每个密码套件
	for _, v := range toEnumerate {
		// 从连接池获取连接
		baseConn, err := pool.Acquire(context.Background())
		if err != nil {
			return enumeratedCiphers, errorutil.NewWithErr(err).WithTag("ztls")
		}
		stats.IncrementZcryptoTLSConnections() // 增加连接统计计数

		// 创建TLS连接并设置当前要测试的密码套件
		conn := tls.Client(baseConn, baseCfg)
		baseCfg.CipherSuites = []uint16{ztlsCiphers[v]}

		// 尝试TLS握手，如果成功则记录支持的密码套件
		if err := c.tlsHandshakeWithTimeout(conn, context.TODO()); err == nil {
			h1 := conn.GetHandshakeLog()
			enumeratedCiphers = append(enumeratedCiphers, h1.ServerHello.CipherSuite.String())
		}
		_ = conn.Close() // 关闭连接，内部也会关闭baseConn
	}
	return enumeratedCiphers, nil
}

// SupportedTLSVersions 返回ZMap TLS库支持的TLS协议版本列表
// 该方法实现了clients.Implementation接口的要求
//
// 返回值:
//   - []string: ZMap TLS库支持的TLS版本字符串列表（包括SSL 3.0到TLS 1.2）
//   - error: 始终为nil，因为版本列表是静态预定义的
//
// ZMap库版本支持:
//   - 支持SSL 3.0（已弃用，但可用于兼容性测试）
//   - 支持TLS 1.0、1.1、1.2
//   - 当前不支持TLS 1.3
func (c *Client) SupportedTLSVersions() ([]string, error) {
	return SupportedTlsVersions, nil
}

// SupportedTLSCiphers 返回ZMap TLS库支持的密码套件列表
// 该方法实现了clients.Implementation接口的要求
//
// 返回值:
//   - []string: ZMap TLS库支持的密码套件名称列表
//   - error: 始终为nil，因为密码套件列表是静态预定义的
//
// ZMap库密码套件特点:
//   - 支持比标准库更多的密码套件
//   - 包括一些已弃用但仍可用于兼容性测试的套件
//   - 提供更全面的TLS扫描能力
func (c *Client) SupportedTLSCiphers() ([]string, error) {
	return AllCiphersNames, nil
}

// getConfig 根据连接参数生成ZMap TLS配置
// 该方法负责将高级连接选项转换为ZMap TLS库可理解的配置
//
// 参数:
//   - hostname: 目标主机名或域名，用于SNI设置
//   - ip: 目标IP地址
//   - port: 目标端口号
//   - options: 连接选项，包含TLS版本、密码套件、SNI等配置
//
// 返回值:
//   - *tls.Config: 生成的ZMap TLS配置结构
//   - error: 配置生成过程中的错误
//
// 配置生成逻辑:
//   - 枚举模式验证：确保指定的TLS版本被支持
//   - SNI智能设置：根据不同情况选择合适的SNI
//   - TLS版本配置：设置最小和最大TLS版本
//   - 密码套件配置：处理自定义密码套件设置
func (c *Client) getConfig(hostname, ip, port string, options clients.ConnectOptions) (*tls.Config, error) {
	// 在枚举模式下验证给定选项是否被支持
	if options.EnumMode == clients.Version && (options.VersionTLS == "" || !stringsutil.EqualFoldAny(options.VersionTLS, SupportedTlsVersions...)) {
		// 版本枚举模式下，TLS版本不被支持
		return nil, errorutil.NewWithTag("ztls", "tlsversion `%v` not supported in ztls", options.VersionTLS)
	}
	if options.EnumMode == clients.Cipher && !stringsutil.EqualFoldAny(options.VersionTLS, SupportedTlsVersions...) {
		// 密码套件枚举模式下，TLS版本不被支持
		return nil, errorutil.NewWithTag("ztls", "cipher enum with version %v not implemented", options.VersionTLS)
	}

	// 从基础配置开始构建
	config := c.tlsConfig

	// 智能设置SNI（服务器名称指示）
	if config.ServerName == "" {
		cfg := config.Clone() // 克隆配置以避免修改原始配置
		if options.SNI != "" {
			// 使用用户指定的SNI
			cfg.ServerName = options.SNI
		} else if iputil.IsIP(hostname) && c.options.RandomForEmptyServerName {
			// 对于IP地址且启用随机SNI选项，使用随机SNI
			// 这将返回服务器的默认证书
			cfg.ServerName = xid.New().String()
		} else {
			// 默认使用hostname作为SNI
			cfg.ServerName = hostname
		}
		config = cfg
	}

	// 配置特定的TLS版本（如果指定）
	if options.VersionTLS != "" {
		version, ok := versionStringToTLSVersion[options.VersionTLS]
		if !ok {
			return nil, errorutil.NewWithTag("ztls", "invalid tls version specified: %s", options.VersionTLS)
		}
		// 设置最小和最大版本相同，强制使用特定版本
		config.MinVersion = version
		config.MaxVersion = version
	}

	// 配置自定义密码套件（非密码套件枚举模式）
	if len(options.Ciphers) > 0 && options.EnumMode != clients.Cipher {
		customCiphers, err := toZTLSCiphers(options.Ciphers)
		if err != nil {
			return nil, errorutil.NewWithTag("ztls", "could not get tls ciphers").Wrap(err)
		}
		c.tlsConfig.CipherSuites = customCiphers
	}
	return config, nil
}

// tlsHandshakeWithTimeout 在指定超时时间内尝试TLS握手
// 该方法提供了带超时控制的TLS握手机制，避免握手过程无限期阻塞
//
// 参数:
//   - tlsConn: ZMap TLS连接对象
//   - ctx: 上下文，用于控制握手超时
//
// 返回值:
//   - error: 握手过程中的错误，成功时为nil
//
// 超时处理:
//   - 使用goroutine和channel实现非阻塞握手
//   - 通过context.Done()检测超时
//   - 超时时返回明确的超时错误信息
//
// 注意:
//   - ZMap库的CertsOnly模式可能返回特殊错误，但当前被注释处理
//   - 该方法确保握手操作不会无限期阻塞程序执行
func (c *Client) tlsHandshakeWithTimeout(tlsConn *tls.Conn, ctx context.Context) error {
	errChan := make(chan error, 1) // 创建缓冲通道接收握手结果
	defer close(errChan)           // 确保通道在函数结束时关闭

	// 使用select语句实现超时控制
	select {
	case <-ctx.Done():
		// 上下文超时或取消，返回超时错误
		return errorutil.NewWithTag("ztls", "timeout while attempting handshake")
	case errChan <- tlsConn.Handshake():
		// 握手完成，无论成功或失败都会发送结果到通道
	}

	// 获取握手结果
	err := <-errChan

	// 注释：ZMap库的CertsOnly模式处理
	// 在仅获取证书模式下，可能返回特殊错误但应视为成功
	// if err == tls.ErrCertsOnly {
	// 	err = nil
	// }

	return err
}