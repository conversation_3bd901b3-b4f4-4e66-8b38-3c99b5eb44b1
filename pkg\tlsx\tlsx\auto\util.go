// Author: chenjb
// Version: V1.0
// Date: 2025-06-24 16:14:45
// FilePath: /yaml_scan/pkg/tlsx/tlsx/auto/util.go
// Description: 自动模式的工具函数和全局变量

// Package auto 的工具模块
// 负责聚合所有TLS实现的支持能力，提供统一的接口
package auto

import (
	"yaml_scan/pkg/tlsx/tlsx/openssl"    // OpenSSL TLS实现
	"yaml_scan/pkg/tlsx/tlsx/tls"        // 原生Go TLS实现
	"yaml_scan/pkg/tlsx/tlsx/ztls"       // ZMap TLS实现
	sliceutil "yaml_scan/utils/slice"    // 切片操作工具
)

var (
	// allCiphersNames 包含所有TLS实现支持的密码套件名称
	// 这是所有实现密码套件的并集，去重后的完整列表
	allCiphersNames []string

	// supportedTlsVersions 包含所有TLS实现支持的TLS版本
	// 这是所有实现TLS版本的并集，去重后的完整列表
	supportedTlsVersions []string
)

// init 包初始化函数
// 在包加载时自动执行，聚合所有TLS实现的支持能力
//
// 功能说明:
//   - 收集所有TLS实现支持的密码套件名称
//   - 收集所有TLS实现支持的TLS版本
//   - 对结果进行去重处理，避免重复项
//   - 为自动模式提供完整的能力集合
//
// 聚合顺序:
//   - 首先添加原生Go TLS的支持能力
//   - 然后添加ZMap TLS的支持能力
//   - 最后添加OpenSSL的支持能力
//   - 最终去重得到所有实现的能力并集
func init() {
	// 聚合所有密码套件名称
	allCiphersNames = append(tls.AllCiphersNames, ztls.AllCiphersNames...)      // 合并tls和ztls的密码套件
	allCiphersNames = append(allCiphersNames, openssl.AllCiphersNames...)       // 添加openssl的密码套件

	// 聚合所有支持的TLS版本
	supportedTlsVersions = append(tls.SupportedTlsVersions, ztls.SupportedTlsVersions...) // 合并tls和ztls的版本
	supportedTlsVersions = append(supportedTlsVersions, openssl.SupportedTLSVersions...)  // 添加openssl的版本

	// 去重处理，确保列表中没有重复项
	allCiphersNames = sliceutil.Dedupe(allCiphersNames)           // 去重密码套件列表
	supportedTlsVersions = sliceutil.Dedupe(supportedTlsVersions) // 去重TLS版本列表
}
