//
// Author: chenjb
// Version: V1.0
// Date: 2025-06-26
// FilePath: /yaml_scan/pkg/tlsx/tlsx/clients/response_test.go
// Description: clients包response.go的单元测试

package clients

import (
	"encoding/json"
	"testing"
	"time"

	ztls "github.com/zmap/zcrypto/tls"
	"github.com/stretchr/testify/require"
)

// TestResponse 测试Response结构的基本功能
// 验证Response结构的字段设置和JSON序列化
func TestResponse(t *testing.T) {
	now := time.Now()
	
	// 创建测试响应
	response := &Response{
		Timestamp:   &now,
		Host:        "www.example.com",
		IP:          "***********",
		Port:        "443",
		ProbeStatus: true,
		Version:     "1.2",
		Cipher:      "TLS_RSA_WITH_AES_256_GCM_SHA384",
		TLSConnection: "ctls",
		JarmHash:    "test_jarm_hash_62_characters_long_string_for_testing_purpose",
		Ja3Hash:     "test_ja3_hash",
		Ja3sHash:    "test_ja3s_hash",
		ServerName:  "www.example.com",
		VersionEnum: []string{"1.0", "1.1", "1.2"},
	}

	// 验证字段设置
	require.Equal(t, &now, response.Timestamp, "时间戳应该正确设置")
	require.Equal(t, "www.example.com", response.Host, "主机名应该正确设置")
	require.Equal(t, "***********", response.IP, "IP地址应该正确设置")
	require.Equal(t, "443", response.Port, "端口应该正确设置")
	require.True(t, response.ProbeStatus, "探测状态应该为true")
	require.Equal(t, "1.2", response.Version, "TLS版本应该正确设置")
	require.Equal(t, "TLS_RSA_WITH_AES_256_GCM_SHA384", response.Cipher, "密码套件应该正确设置")
	require.Equal(t, "ctls", response.TLSConnection, "TLS连接类型应该正确设置")
	require.Len(t, response.JarmHash, 62, "JARM哈希应该是62个字符")
	require.Equal(t, "test_ja3_hash", response.Ja3Hash, "JA3哈希应该正确设置")
	require.Equal(t, "test_ja3s_hash", response.Ja3sHash, "JA3S哈希应该正确设置")
	require.Equal(t, "www.example.com", response.ServerName, "服务器名称应该正确设置")
	require.Len(t, response.VersionEnum, 3, "版本枚举应该有3个元素")

	// 测试JSON序列化
	jsonData, err := json.Marshal(response)
	require.NoError(t, err, "JSON序列化不应该出错")
	require.NotEmpty(t, jsonData, "JSON数据不应该为空")

	// 测试JSON反序列化
	var deserializedResponse Response
	err = json.Unmarshal(jsonData, &deserializedResponse)
	require.NoError(t, err, "JSON反序列化不应该出错")
	require.Equal(t, response.Host, deserializedResponse.Host, "反序列化后主机名应该匹配")
	require.Equal(t, response.Port, deserializedResponse.Port, "反序列化后端口应该匹配")
}

// TestResponseWithError 测试包含错误信息的Response
// 验证错误响应的正确处理
func TestResponseWithError(t *testing.T) {
	// 创建包含错误的响应
	response := &Response{
		Host:        "invalid.example.com",
		Port:        "443",
		ProbeStatus: false,
		Error:       "connection timeout",
	}

	// 验证错误响应的字段
	require.Equal(t, "invalid.example.com", response.Host, "主机名应该正确设置")
	require.Equal(t, "443", response.Port, "端口应该正确设置")
	require.False(t, response.ProbeStatus, "探测状态应该为false")
	require.Equal(t, "connection timeout", response.Error, "错误信息应该正确设置")

	// 测试JSON序列化
	jsonData, err := json.Marshal(response)
	require.NoError(t, err, "错误响应的JSON序列化不应该出错")
	require.Contains(t, string(jsonData), "connection timeout", "JSON应该包含错误信息")
}

// TestTlsCiphers 测试TlsCiphers结构
// 验证TLS密码套件枚举结果的结构
func TestTlsCiphers(t *testing.T) {
	// 创建密码套件类型
	cipherTypes := CipherTypes{
		Secure:   []string{"TLS_RSA_WITH_AES_256_GCM_SHA384"},
		Weak:     []string{"TLS_RSA_WITH_AES_128_CBC_SHA"},
		Insecure: []string{"TLS_RSA_WITH_RC4_128_SHA"},
		Unknown:  []string{"UNKNOWN_CIPHER"},
	}

	// 创建TLS密码套件结构
	tlsCiphers := TlsCiphers{
		Version: "1.2",
		Ciphers: cipherTypes,
	}

	// 验证字段设置
	require.Equal(t, "1.2", tlsCiphers.Version, "TLS版本应该正确设置")
	require.Len(t, tlsCiphers.Ciphers.Secure, 1, "安全密码套件应该有1个")
	require.Len(t, tlsCiphers.Ciphers.Weak, 1, "弱密码套件应该有1个")
	require.Len(t, tlsCiphers.Ciphers.Insecure, 1, "不安全密码套件应该有1个")
	require.Len(t, tlsCiphers.Ciphers.Unknown, 1, "未知密码套件应该有1个")

	// 测试JSON序列化
	jsonData, err := json.Marshal(tlsCiphers)
	require.NoError(t, err, "TlsCiphers的JSON序列化不应该出错")
	require.Contains(t, string(jsonData), "1.2", "JSON应该包含TLS版本")
	require.Contains(t, string(jsonData), "TLS_RSA_WITH_AES_256_GCM_SHA384", "JSON应该包含安全密码套件")
}

// TestCipherTypes 测试CipherTypes结构
// 验证密码套件分类的基本功能
func TestCipherTypes(t *testing.T) {
	// 创建密码套件类型
	cipherTypes := CipherTypes{
		Secure:   []string{"SECURE_CIPHER_1", "SECURE_CIPHER_2"},
		Weak:     []string{"WEAK_CIPHER_1"},
		Insecure: []string{"INSECURE_CIPHER_1", "INSECURE_CIPHER_2", "INSECURE_CIPHER_3"},
		Unknown:  []string{"UNKNOWN_CIPHER_1"},
	}

	// 验证字段设置
	require.Len(t, cipherTypes.Secure, 2, "安全密码套件应该有2个")
	require.Len(t, cipherTypes.Weak, 1, "弱密码套件应该有1个")
	require.Len(t, cipherTypes.Insecure, 3, "不安全密码套件应该有3个")
	require.Len(t, cipherTypes.Unknown, 1, "未知密码套件应该有1个")

	// 验证具体内容
	require.Contains(t, cipherTypes.Secure, "SECURE_CIPHER_1", "应该包含第一个安全密码套件")
	require.Contains(t, cipherTypes.Secure, "SECURE_CIPHER_2", "应该包含第二个安全密码套件")
	require.Contains(t, cipherTypes.Weak, "WEAK_CIPHER_1", "应该包含弱密码套件")
	require.Contains(t, cipherTypes.Insecure, "INSECURE_CIPHER_1", "应该包含第一个不安全密码套件")
	require.Contains(t, cipherTypes.Unknown, "UNKNOWN_CIPHER_1", "应该包含未知密码套件")

	// 测试JSON序列化
	jsonData, err := json.Marshal(cipherTypes)
	require.NoError(t, err, "CipherTypes的JSON序列化不应该出错")
	require.Contains(t, string(jsonData), "SECURE_CIPHER_1", "JSON应该包含安全密码套件")
	require.Contains(t, string(jsonData), "WEAK_CIPHER_1", "JSON应该包含弱密码套件")
}

// TestCertificateResponse 测试CertificateResponse结构
// 验证证书响应信息的结构和字段
func TestCertificateResponse(t *testing.T) {
	now := time.Now()
	
	// 创建证书响应
	certResponse := &CertificateResponse{
		Expired:      false,
		SelfSigned:   false,
		MisMatched:   false,
		Revoked:      false,
		Untrusted:    false,
		NotBefore:    now.Add(-24 * time.Hour),
		NotAfter:     now.Add(365 * 24 * time.Hour),
		SubjectDN:    "CN=www.example.com,O=Example Org,C=US",
		SubjectCN:    "www.example.com",
		SubjectOrg:   []string{"Example Org"},
		SubjectAN:    []string{"www.example.com", "example.com"},
		Domains:      []string{"www.example.com", "example.com"},
		Serial:       "123456789ABCDEF",
		IssuerDN:     "CN=Example CA,O=Example CA Org,C=US",
		IssuerCN:     "Example CA",
		IssuerOrg:    []string{"Example CA Org"},
		Emails:       []string{"<EMAIL>"},
		Certificate:  "-----BEGIN CERTIFICATE-----\ntest\n-----END CERTIFICATE-----",
		WildCardCert: false,
	}

	// 验证字段设置
	require.False(t, certResponse.Expired, "证书不应该过期")
	require.False(t, certResponse.SelfSigned, "证书不应该是自签名")
	require.False(t, certResponse.MisMatched, "证书不应该不匹配")
	require.False(t, certResponse.Revoked, "证书不应该被撤销")
	require.False(t, certResponse.Untrusted, "证书不应该不受信任")
	require.Equal(t, "CN=www.example.com,O=Example Org,C=US", certResponse.SubjectDN, "主体DN应该正确设置")
	require.Equal(t, "www.example.com", certResponse.SubjectCN, "主体CN应该正确设置")
	require.Len(t, certResponse.SubjectOrg, 1, "主体组织应该有1个")
	require.Len(t, certResponse.SubjectAN, 2, "主体备用名称应该有2个")
	require.Len(t, certResponse.Domains, 2, "域名列表应该有2个")
	require.Equal(t, "123456789ABCDEF", certResponse.Serial, "序列号应该正确设置")
	require.Equal(t, "Example CA", certResponse.IssuerCN, "颁发者CN应该正确设置")
	require.Len(t, certResponse.Emails, 1, "邮箱列表应该有1个")
	require.Contains(t, certResponse.Certificate, "BEGIN CERTIFICATE", "证书应该包含PEM标记")
	require.False(t, certResponse.WildCardCert, "不应该是通配符证书")

	// 测试JSON序列化
	jsonData, err := json.Marshal(certResponse)
	require.NoError(t, err, "证书响应的JSON序列化不应该出错")
	require.Contains(t, string(jsonData), "www.example.com", "JSON应该包含主体CN")
}

// TestCertificateResponseFingerprintHash 测试证书指纹哈希结构
// 验证多种哈希算法的指纹存储
func TestCertificateResponseFingerprintHash(t *testing.T) {
	// 创建指纹哈希
	fingerprintHash := CertificateResponseFingerprintHash{
		MD5:    "d41d8cd98f00b204e9800998ecf8427e",
		SHA1:   "da39a3ee5e6b4b0d3255bfef95601890afd80709",
		SHA256: "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855",
	}

	// 验证字段设置
	require.Len(t, fingerprintHash.MD5, 32, "MD5哈希应该是32个字符")
	require.Len(t, fingerprintHash.SHA1, 40, "SHA1哈希应该是40个字符")
	require.Len(t, fingerprintHash.SHA256, 64, "SHA256哈希应该是64个字符")

	// 测试JSON序列化
	jsonData, err := json.Marshal(fingerprintHash)
	require.NoError(t, err, "指纹哈希的JSON序列化不应该出错")
	require.Contains(t, string(jsonData), fingerprintHash.MD5, "JSON应该包含MD5哈希")
	require.Contains(t, string(jsonData), fingerprintHash.SHA1, "JSON应该包含SHA1哈希")
	require.Contains(t, string(jsonData), fingerprintHash.SHA256, "JSON应该包含SHA256哈希")
}

// TestResponseWithZTLSDetails 测试包含ZMap TLS详细信息的Response
// 验证ztls模式下的详细协议信息
func TestResponseWithZTLSDetails(t *testing.T) {
	// 创建包含ZMap TLS详细信息的响应
	response := &Response{
		Host:        "www.example.com",
		Port:        "443",
		ProbeStatus: true,
		Version:     "1.3",
		Cipher:      "TLS_AES_256_GCM_SHA384",
		// 注意：ClientHello和ServerHello是复杂的结构，这里只测试基本的设置
		ClientCertRequired: &[]bool{false}[0], // 使用指针
	}

	// 验证字段设置
	require.Equal(t, "www.example.com", response.Host, "主机名应该正确设置")
	require.Equal(t, "443", response.Port, "端口应该正确设置")
	require.True(t, response.ProbeStatus, "探测状态应该为true")
	require.Equal(t, "1.3", response.Version, "TLS版本应该正确设置")
	require.Equal(t, "TLS_AES_256_GCM_SHA384", response.Cipher, "密码套件应该正确设置")
	require.NotNil(t, response.ClientCertRequired, "客户端证书要求不应该为nil")
	require.False(t, *response.ClientCertRequired, "客户端证书要求应该为false")

	// 测试JSON序列化
	jsonData, err := json.Marshal(response)
	require.NoError(t, err, "包含ZMap详细信息的响应JSON序列化不应该出错")
	require.Contains(t, string(jsonData), "TLS_AES_256_GCM_SHA384", "JSON应该包含密码套件")
}
