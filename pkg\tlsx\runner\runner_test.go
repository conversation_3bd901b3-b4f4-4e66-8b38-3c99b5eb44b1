//
// Author: chenjb
// Version: V1.0
// Date: 2025-06-30
// FilePath: /yaml_scan/pkg/tlsx/runner/runner_test.go
// Description: runner.go的单元测试

package runner

import (
	"os"
	"strings"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"yaml_scan/pkg/tlsx/tlsx/clients"
)

// TestNew 测试New函数
// 验证运行器的创建和初始化
func TestNew(t *testing.T) {
	t.Run("基本运行器创建", func(t *testing.T) {
		options := &clients.Options{
			ScanMode:    "ctls",
			Timeout:     10,
			Retries:     2,
			Concurrency: 5,
			Ports:       []string{"443"},
		}

		runner, err := New(options)
		require.NoError(t, err, "创建运行器应该成功")
		require.NotNil(t, runner, "运行器不应该为nil")
		require.NotNil(t, runner.options, "运行器选项不应该为nil")
		require.NotNil(t, runner.fastDialer, "快速拨号器不应该为nil")
		require.NotNil(t, runner.dnsclient, "DNS客户端不应该为nil")
		require.NotNil(t, runner.outputWriter, "输出写入器不应该为nil")

		// 验证选项设置
		require.Equal(t, "ctls", runner.options.ScanMode, "扫描模式应该匹配")
		require.Equal(t, 10, runner.options.Timeout, "超时时间应该匹配")
		require.Equal(t, 2, runner.options.Retries, "重试次数应该匹配")
		require.Equal(t, 5, runner.options.Concurrency, "并发数应该匹配")

		// 清理资源
		err = runner.Close()
		require.NoError(t, err, "关闭运行器应该成功")
	})

	t.Run("密码套件枚举依赖处理", func(t *testing.T) {
		options := &clients.Options{
			ScanMode:         "ctls",
			Timeout:          10,
			Retries:          2,
			Concurrency:      5,
			Ports:            []string{"443"},
			TlsCiphersEnum:   true,  // 启用密码套件枚举
			TlsVersionsEnum:  false, // 初始不启用版本枚举
		}

		runner, err := New(options)
		require.NoError(t, err, "创建运行器应该成功")
		require.NotNil(t, runner, "运行器不应该为nil")

		// 验证密码套件枚举会自动启用版本枚举
		require.True(t, runner.options.TlsVersionsEnum, "密码套件枚举应该自动启用版本枚举")

		err = runner.Close()
		require.NoError(t, err, "关闭运行器应该成功")
	})

	t.Run("自定义DNS解析器", func(t *testing.T) {
		options := &clients.Options{
			ScanMode:    "ctls",
			Timeout:     10,
			Retries:     2,
			Concurrency: 5,
			Ports:       []string{"443"},
			Resolvers:   []string{"*******", "*******"},
		}

		runner, err := New(options)
		require.NoError(t, err, "创建运行器应该成功")
		require.NotNil(t, runner, "运行器不应该为nil")

		err = runner.Close()
		require.NoError(t, err, "关闭运行器应该成功")
	})

	t.Run("IPv6支持", func(t *testing.T) {
		options := &clients.Options{
			ScanMode:    "ctls",
			Timeout:     10,
			Retries:     2,
			Concurrency: 5,
			Ports:       []string{"443"},
			IPVersion:   []string{"4", "6"},
		}

		runner, err := New(options)
		require.NoError(t, err, "创建运行器应该成功")
		require.NotNil(t, runner, "运行器不应该为nil")

		err = runner.Close()
		require.NoError(t, err, "关闭运行器应该成功")
	})

	t.Run("静默模式", func(t *testing.T) {
		options := &clients.Options{
			ScanMode:    "ctls",
			Timeout:     10,
			Retries:     2,
			Concurrency: 5,
			Ports:       []string{"443"},
			Silent:      true,
		}

		runner, err := New(options)
		require.NoError(t, err, "创建运行器应该成功")
		require.NotNil(t, runner, "运行器不应该为nil")

		err = runner.Close()
		require.NoError(t, err, "关闭运行器应该成功")
	})

	t.Run("无颜色模式", func(t *testing.T) {
		options := &clients.Options{
			ScanMode:    "ctls",
			Timeout:     10,
			Retries:     2,
			Concurrency: 5,
			Ports:       []string{"443"},
			NoColor:     true,
		}

		runner, err := New(options)
		require.NoError(t, err, "创建运行器应该成功")
		require.NotNil(t, runner, "运行器不应该为nil")

		err = runner.Close()
		require.NoError(t, err, "关闭运行器应该成功")
	})
}

// TestTaskInput 测试taskInput结构体
// 验证任务输入的基本功能
func TestTaskInput(t *testing.T) {
	t.Run("taskInput结构体基本功能", func(t *testing.T) {
		task := taskInput{
			host: "example.com",
			ip:   "***********",
			port: "443",
			sni:  "test.example.com",
		}

		require.Equal(t, "example.com", task.host, "主机名应该正确设置")
		require.Equal(t, "***********", task.ip, "IP地址应该正确设置")
		require.Equal(t, "443", task.port, "端口应该正确设置")
		require.Equal(t, "test.example.com", task.sni, "SNI应该正确设置")
	})

	t.Run("taskInput Address方法", func(t *testing.T) {
		task := taskInput{
			host: "example.com",
			port: "443",
		}

		address := task.Address()
		require.Equal(t, "example.com:443", address, "Address方法应该返回正确格式")
	})

	t.Run("taskInput零值", func(t *testing.T) {
		task := taskInput{}

		require.Empty(t, task.host, "默认主机名应该为空")
		require.Empty(t, task.ip, "默认IP应该为空")
		require.Empty(t, task.port, "默认端口应该为空")
		require.Empty(t, task.sni, "默认SNI应该为空")
	})
}

// TestValidateOptions 测试validateOptions方法
// 验证选项验证功能
func TestValidateOptions(t *testing.T) {
	t.Run("基本选项验证", func(t *testing.T) {
		options := &clients.Options{
			ScanMode:    "ctls",
			Timeout:     10,
			Retries:     0, // 测试默认值设置
			Concurrency: 5,
			Ports:       []string{"443"},
			Inputs:      []string{"example.com"},
		}

		runner := &Runner{options: options}
		err := runner.validateOptions()
		require.NoError(t, err, "基本选项验证应该成功")

		// 验证默认值设置
		require.Equal(t, 1, runner.options.Retries, "重试次数应该设置为默认值1")
	})

	t.Run("无输入源验证", func(t *testing.T) {
		options := &clients.Options{
			ScanMode:    "ctls",
			Timeout:     10,
			Retries:     2,
			Concurrency: 5,
			Ports:       []string{"443"},
			// 没有设置任何输入源
		}

		runner := &Runner{options: options}
		err := runner.validateOptions()
		// 这个测试可能会失败，因为没有输入源
		// 具体行为取决于validateOptions的实现
		if err != nil {
			t.Logf("无输入源时验证失败（可能是预期的）: %v", err)
		}
	})

	t.Run("端口默认值设置", func(t *testing.T) {
		options := &clients.Options{
			ScanMode:    "ctls",
			Timeout:     10,
			Retries:     2,
			Concurrency: 5,
			// 没有设置端口
			Inputs: []string{"example.com"},
		}

		runner := &Runner{options: options}
		err := runner.validateOptions()
		require.NoError(t, err, "选项验证应该成功")

		// 验证默认端口设置
		require.NotEmpty(t, runner.options.Ports, "应该设置默认端口")
	})
}

// TestGetHostPortFromInput 测试getHostPortFromInput方法
// 验证主机名和端口解析功能
func TestGetHostPortFromInput(t *testing.T) {
	// 创建一个基本的运行器用于测试
	options := &clients.Options{
		ScanMode:    "ctls",
		Timeout:     10,
		Retries:     2,
		Concurrency: 5,
		Ports:       []string{"443"},
	}
	runner := &Runner{options: options}

	testCases := []struct {
		name         string
		input        string
		expectedHost string
		expectedPort string
	}{
		{
			name:         "域名无端口",
			input:        "example.com",
			expectedHost: "example.com",
			expectedPort: "",
		},
		{
			name:         "域名带端口",
			input:        "example.com:8080",
			expectedHost: "example.com",
			expectedPort: "8080",
		},
		{
			name:         "IP地址无端口",
			input:        "***********",
			expectedHost: "***********",
			expectedPort: "",
		},
		{
			name:         "IP地址带端口",
			input:        "***********:443",
			expectedHost: "***********",
			expectedPort: "443",
		},
		{
			name:         "HTTPS URL",
			input:        "https://example.com",
			expectedHost: "example.com",
			expectedPort: "443",
		},
		{
			name:         "HTTP URL",
			input:        "http://example.com",
			expectedHost: "example.com",
			expectedPort: "80",
		},
		{
			name:         "HTTPS URL带自定义端口",
			input:        "https://example.com:8443",
			expectedHost: "example.com",
			expectedPort: "8443",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			host, port := runner.getHostPortFromInput(tc.input)
			require.Equal(t, tc.expectedHost, host, "主机名应该匹配")
			require.Equal(t, tc.expectedPort, port, "端口应该匹配")
		})
	}
}

// TestProcessInputItem 测试processInputItem方法
// 验证输入项处理功能
func TestProcessInputItem(t *testing.T) {
	// 创建一个基本的运行器用于测试
	options := &clients.Options{
		ScanMode:    "ctls",
		Timeout:     10,
		Retries:     2,
		Concurrency: 5,
		Ports:       []string{"443", "8443"},
	}
	runner := &Runner{options: options}

	t.Run("处理域名输入", func(t *testing.T) {
		inputs := make(chan taskInput, 10)
		defer close(inputs)

		runner.processInputItem("example.com", inputs)

		// 应该为每个端口生成一个任务
		var tasks []taskInput
		for len(inputs) > 0 {
			task := <-inputs
			tasks = append(tasks, task)
		}

		require.Len(t, tasks, 2, "应该为每个端口生成一个任务")
		require.Equal(t, "example.com", tasks[0].host, "主机名应该正确")
		require.Contains(t, []string{"443", "8443"}, tasks[0].port, "端口应该是配置的端口之一")
	})

	t.Run("处理带端口的输入", func(t *testing.T) {
		inputs := make(chan taskInput, 10)
		defer close(inputs)

		runner.processInputItem("example.com:9443", inputs)

		// 应该只生成一个任务，使用指定端口
		var tasks []taskInput
		for len(inputs) > 0 {
			task := <-inputs
			tasks = append(tasks, task)
		}

		require.Len(t, tasks, 1, "应该只生成一个任务")
		require.Equal(t, "example.com", tasks[0].host, "主机名应该正确")
		require.Equal(t, "9443", tasks[0].port, "端口应该是指定的端口")
	})

	t.Run("处理CIDR输入", func(t *testing.T) {
		inputs := make(chan taskInput, 100)
		defer close(inputs)

		// 使用小的CIDR网段进行测试
		runner.processInputItem("***********/30", inputs)

		// 应该为网段中的每个IP和每个端口生成任务
		var tasks []taskInput
		for len(inputs) > 0 {
			task := <-inputs
			tasks = append(tasks, task)
		}

		// /30网段包含4个IP地址，但通常只有2个可用（去除网络地址和广播地址）
		// 每个IP有2个端口，所以期望有多个任务
		require.Greater(t, len(tasks), 0, "应该生成至少一个任务")

		// 验证生成的任务都有有效的IP地址
		for _, task := range tasks {
			require.True(t, strings.HasPrefix(task.host, "192.168.1."), "IP应该在指定网段内")
			require.Contains(t, []string{"443", "8443"}, task.port, "端口应该是配置的端口之一")
		}
	})
}

// TestNormalizeAndQueueInputs 测试normalizeAndQueueInputs方法
// 验证输入规范化和队列功能
func TestNormalizeAndQueueInputs(t *testing.T) {
	t.Run("处理命令行输入", func(t *testing.T) {
		options := &clients.Options{
			ScanMode:    "ctls",
			Timeout:     10,
			Retries:     2,
			Concurrency: 5,
			Ports:       []string{"443"},
			Inputs:      []string{"example.com", "test.com:8080"},
		}
		runner := &Runner{options: options}

		inputs := make(chan taskInput, 10)
		defer close(inputs)

		err := runner.normalizeAndQueueInputs(inputs)
		require.NoError(t, err, "处理命令行输入应该成功")

		// 收集所有任务
		var tasks []taskInput
		for len(inputs) > 0 {
			task := <-inputs
			tasks = append(tasks, task)
		}

		require.Greater(t, len(tasks), 0, "应该生成至少一个任务")

		// 验证任务内容
		hostFound := false
		for _, task := range tasks {
			if task.host == "example.com" || task.host == "test.com" {
				hostFound = true
				break
			}
		}
		require.True(t, hostFound, "应该包含输入的主机名")
	})

	t.Run("处理输入文件", func(t *testing.T) {
		// 创建临时输入文件
		tmpFile, err := os.CreateTemp("", "tlsx_test_input_*.txt")
		require.NoError(t, err, "创建临时文件应该成功")
		defer os.Remove(tmpFile.Name())

		// 写入测试数据
		testData := "example.com\ntest.com:8080\n\n# 这是注释\nanother.com"
		_, err = tmpFile.WriteString(testData)
		require.NoError(t, err, "写入测试数据应该成功")
		tmpFile.Close()

		options := &clients.Options{
			ScanMode:    "ctls",
			Timeout:     10,
			Retries:     2,
			Concurrency: 5,
			Ports:       []string{"443"},
			InputList:   tmpFile.Name(),
		}
		runner := &Runner{options: options}

		inputs := make(chan taskInput, 20)
		defer close(inputs)

		err = runner.normalizeAndQueueInputs(inputs)
		require.NoError(t, err, "处理输入文件应该成功")

		// 收集所有任务
		var tasks []taskInput
		for len(inputs) > 0 {
			task := <-inputs
			tasks = append(tasks, task)
		}

		require.Greater(t, len(tasks), 0, "应该生成至少一个任务")
	})

	t.Run("处理不存在的输入文件", func(t *testing.T) {
		options := &clients.Options{
			ScanMode:    "ctls",
			Timeout:     10,
			Retries:     2,
			Concurrency: 5,
			Ports:       []string{"443"},
			InputList:   "/nonexistent/file.txt",
		}
		runner := &Runner{options: options}

		inputs := make(chan taskInput, 10)
		defer close(inputs)

		err := runner.normalizeAndQueueInputs(inputs)
		require.Error(t, err, "处理不存在的文件应该失败")
		require.Contains(t, err.Error(), "could not open input file", "错误信息应该正确")
	})
}

// TestClose 测试Close方法
// 验证资源清理功能
func TestClose(t *testing.T) {
	t.Run("正常关闭", func(t *testing.T) {
		options := &clients.Options{
			ScanMode:    "ctls",
			Timeout:     10,
			Retries:     2,
			Concurrency: 5,
			Ports:       []string{"443"},
		}

		runner, err := New(options)
		require.NoError(t, err, "创建运行器应该成功")
		require.NotNil(t, runner, "运行器不应该为nil")

		err = runner.Close()
		require.NoError(t, err, "关闭运行器应该成功")
	})

	t.Run("重复关闭", func(t *testing.T) {
		options := &clients.Options{
			ScanMode:    "ctls",
			Timeout:     10,
			Retries:     2,
			Concurrency: 5,
			Ports:       []string{"443"},
		}

		runner, err := New(options)
		require.NoError(t, err, "创建运行器应该成功")

		// 第一次关闭
		err = runner.Close()
		require.NoError(t, err, "第一次关闭应该成功")

		// 第二次关闭
		err = runner.Close()
		require.NoError(t, err, "重复关闭应该成功")
	})
}

// TestResolveFQDN 测试resolveFQDN方法
// 验证DNS解析功能
func TestResolveFQDN(t *testing.T) {
	// 创建一个基本的运行器用于测试
	options := &clients.Options{
		ScanMode:    "ctls",
		Timeout:     10,
		Retries:     2,
		Concurrency: 5,
		Ports:       []string{"443"},
	}
	runner, err := New(options)
	require.NoError(t, err, "创建运行器应该成功")
	defer runner.Close()

	t.Run("解析IP地址", func(t *testing.T) {
		// 测试IP地址直接返回
		ips, err := runner.resolveFQDN("**************")
		require.NoError(t, err, "解析IP地址应该成功")
		require.Len(t, ips, 1, "应该返回一个IP地址")
		require.Equal(t, "**************", ips[0], "应该返回原始IP地址")
	})

	t.Run("解析域名", func(t *testing.T) {
		// 测试域名解析
		ips, err := runner.resolveFQDN("www.baidu.com")
		if err != nil {
			t.Logf("域名解析失败（可能是网络问题）: %v", err)
			t.Skip("跳过域名解析测试，可能是网络问题")
		}
		require.Greater(t, len(ips), 0, "应该返回至少一个IP地址")

		// 验证返回的都是有效IP地址
		for _, ip := range ips {
			require.NotEmpty(t, ip, "IP地址不应该为空")
		}
	})

	t.Run("IPv4版本指定", func(t *testing.T) {
		// 设置只解析IPv4
		runner.options.IPVersion = []string{"4"}

		ips, err := runner.resolveFQDN("www.baidu.com")
		if err != nil {
			t.Logf("IPv4解析失败（可能是网络问题）: %v", err)
			t.Skip("跳过IPv4解析测试，可能是网络问题")
		}
		require.Greater(t, len(ips), 0, "应该返回至少一个IPv4地址")
	})

	t.Run("解析不存在的域名", func(t *testing.T) {
		// 测试不存在的域名
		_, err := runner.resolveFQDN("nonexistent-domain-12345.com")
		if err != nil {
			t.Logf("解析不存在域名失败（预期的）: %v", err)
		}
		// 不存在的域名应该返回错误或空结果
	})
}

// TestProcessInputItemWithSni 测试processInputItemWithSni方法
// 验证SNI处理功能
func TestProcessInputItemWithSni(t *testing.T) {
	// 创建一个基本的运行器用于测试
	options := &clients.Options{
		ScanMode:    "ctls",
		Timeout:     10,
		Retries:     2,
		Concurrency: 5,
		Ports:       []string{"443"},
	}
	runner := &Runner{options: options}

	t.Run("无SNI配置", func(t *testing.T) {
		inputs := make(chan taskInput, 10)
		defer close(inputs)

		task := taskInput{
			host: "example.com",
			port: "443",
		}

		runner.processInputItemWithSni(task, inputs)

		// 应该生成一个任务
		require.Equal(t, 1, len(inputs), "应该生成一个任务")

		resultTask := <-inputs
		require.Equal(t, "example.com", resultTask.host, "主机名应该正确")
		require.Equal(t, "443", resultTask.port, "端口应该正确")
		require.Empty(t, resultTask.sni, "SNI应该为空")
	})

	t.Run("单个SNI配置", func(t *testing.T) {
		runner.options.ServerName = []string{"test.example.com"}

		inputs := make(chan taskInput, 10)
		defer close(inputs)

		task := taskInput{
			host: "example.com",
			port: "443",
		}

		runner.processInputItemWithSni(task, inputs)

		// 应该生成一个任务
		require.Equal(t, 1, len(inputs), "应该生成一个任务")

		resultTask := <-inputs
		require.Equal(t, "example.com", resultTask.host, "主机名应该正确")
		require.Equal(t, "443", resultTask.port, "端口应该正确")
		require.Equal(t, "test.example.com", resultTask.sni, "SNI应该正确设置")
	})

	t.Run("多个SNI配置", func(t *testing.T) {
		runner.options.ServerName = []string{"sni1.example.com", "sni2.example.com", "sni3.example.com"}

		inputs := make(chan taskInput, 10)
		defer close(inputs)

		task := taskInput{
			host: "example.com",
			port: "443",
		}

		runner.processInputItemWithSni(task, inputs)

		// 应该为每个SNI生成一个任务
		require.Equal(t, 3, len(inputs), "应该为每个SNI生成一个任务")

		var sniValues []string
		for len(inputs) > 0 {
			resultTask := <-inputs
			require.Equal(t, "example.com", resultTask.host, "主机名应该正确")
			require.Equal(t, "443", resultTask.port, "端口应该正确")
			sniValues = append(sniValues, resultTask.sni)
		}

		require.Contains(t, sniValues, "sni1.example.com", "应该包含第一个SNI")
		require.Contains(t, sniValues, "sni2.example.com", "应该包含第二个SNI")
		require.Contains(t, sniValues, "sni3.example.com", "应该包含第三个SNI")
	})
}

// TestProcessInputCIDR 测试processInputCIDR方法
// 验证CIDR网段处理功能
func TestProcessInputCIDR(t *testing.T) {
	// 创建一个基本的运行器用于测试
	options := &clients.Options{
		ScanMode:    "ctls",
		Timeout:     10,
		Retries:     2,
		Concurrency: 5,
		Ports:       []string{"443", "8443"},
	}
	runner := &Runner{options: options}

	t.Run("处理小型CIDR网段", func(t *testing.T) {
		inputs := make(chan taskInput, 100)
		defer close(inputs)

		// 使用/30网段，包含4个IP地址
		runner.processInputCIDR("***********/30", inputs)

		// 收集所有任务
		var tasks []taskInput
		for len(inputs) > 0 {
			task := <-inputs
			tasks = append(tasks, task)
		}

		require.Greater(t, len(tasks), 0, "应该生成至少一个任务")

		// 验证生成的任务
		for _, task := range tasks {
			require.True(t, strings.HasPrefix(task.host, "192.168.1."), "IP应该在指定网段内")
			require.Contains(t, []string{"443", "8443"}, task.port, "端口应该是配置的端口之一")
			require.Empty(t, task.ip, "IP字段应该为空（host字段包含IP）")
		}

		// 验证端口分布
		portCounts := make(map[string]int)
		for _, task := range tasks {
			portCounts[task.port]++
		}
		require.Greater(t, portCounts["443"], 0, "应该有443端口的任务")
		require.Greater(t, portCounts["8443"], 0, "应该有8443端口的任务")
	})

	t.Run("处理无效CIDR", func(t *testing.T) {
		inputs := make(chan taskInput, 10)
		defer close(inputs)

		// 这个测试可能不会产生错误，因为processInputCIDR可能不验证CIDR格式
		// 具体行为取决于cidr.IPAddressesAsStream的实现
		runner.processInputCIDR("invalid-cidr", inputs)

		// 收集任务（可能为空）
		var tasks []taskInput
		for len(inputs) > 0 {
			task := <-inputs
			tasks = append(tasks, task)
		}

		// 无效CIDR应该不生成任务或产生错误
		t.Logf("无效CIDR生成了 %d 个任务", len(tasks))
	})
}

// TestProcessInputForMultipleIPs 测试processInputForMultipleIPs方法
// 验证多IP处理功能
func TestProcessInputForMultipleIPs(t *testing.T) {
	// 创建一个基本的运行器用于测试
	options := &clients.Options{
		ScanMode:    "ctls",
		Timeout:     10,
		Retries:     2,
		Concurrency: 5,
		Ports:       []string{"443", "8443"},
		ScanAllIPs:  true, // 启用多IP扫描
	}
	runner, err := New(options)
	require.NoError(t, err, "创建运行器应该成功")
	defer runner.Close()

	t.Run("处理域名多IP", func(t *testing.T) {
		inputs := make(chan taskInput, 100)
		defer close(inputs)

		// 使用一个知名域名进行测试
		runner.processInputForMultipleIPs("www.baidu.com", inputs)

		// 收集所有任务
		var tasks []taskInput
		for len(inputs) > 0 {
			task := <-inputs
			tasks = append(tasks, task)
		}

		if len(tasks) == 0 {
			t.Log("域名解析可能失败，跳过验证")
			return
		}

		// 验证生成的任务
		for _, task := range tasks {
			require.Equal(t, "www.baidu.com", task.host, "主机名应该保持原样")
			require.NotEmpty(t, task.ip, "IP字段应该不为空")
			require.Contains(t, []string{"443", "8443"}, task.port, "端口应该是配置的端口之一")
		}
	})

	t.Run("处理带端口的域名", func(t *testing.T) {
		inputs := make(chan taskInput, 100)
		defer close(inputs)

		runner.processInputForMultipleIPs("www.baidu.com:9443", inputs)

		// 收集所有任务
		var tasks []taskInput
		for len(inputs) > 0 {
			task := <-inputs
			tasks = append(tasks, task)
		}

		if len(tasks) == 0 {
			t.Log("域名解析可能失败，跳过验证")
			return
		}

		// 验证生成的任务都使用指定端口
		for _, task := range tasks {
			require.Equal(t, "www.baidu.com", task.host, "主机名应该正确")
			require.NotEmpty(t, task.ip, "IP字段应该不为空")
			require.Equal(t, "9443", task.port, "端口应该是指定的端口")
		}
	})

	t.Run("处理IP地址", func(t *testing.T) {
		inputs := make(chan taskInput, 10)
		defer close(inputs)

		// 直接使用IP地址
		runner.processInputForMultipleIPs("**************", inputs)

		// 收集所有任务
		var tasks []taskInput
		for len(inputs) > 0 {
			task := <-inputs
			tasks = append(tasks, task)
		}

		require.Greater(t, len(tasks), 0, "应该生成至少一个任务")

		// 验证生成的任务
		for _, task := range tasks {
			require.Equal(t, "**************", task.host, "主机名应该是IP地址")
			require.Equal(t, "**************", task.ip, "IP字段应该是相同的IP地址")
			require.Contains(t, []string{"443", "8443"}, task.port, "端口应该是配置的端口之一")
		}
	})
}

// TestValidateOptionsAdvanced 测试validateOptions方法的高级场景
// 验证复杂选项验证功能
func TestValidateOptionsAdvanced(t *testing.T) {
	t.Run("resp-only与探测标志冲突", func(t *testing.T) {
		options := &clients.Options{
			ScanMode:    "ctls",
			Timeout:     10,
			Retries:     2,
			Concurrency: 5,
			Ports:       []string{"443"},
			Inputs:      []string{"example.com"},
			RespOnly:    true,
			TLSVersion:  true, // 这会与resp-only冲突
		}

		runner := &Runner{options: options}
		err := runner.validateOptions()
		require.Error(t, err, "resp-only与探测标志应该冲突")
		require.Contains(t, err.Error(), "resp-only flag can only be used with san and cn flags", "错误信息应该正确")
	})

	t.Run("san/cn与其他探测标志冲突", func(t *testing.T) {
		options := &clients.Options{
			ScanMode:    "ctls",
			Timeout:     10,
			Retries:     2,
			Concurrency: 5,
			Ports:       []string{"443"},
			Inputs:      []string{"example.com"},
			SAN:         true,
			Cipher:      true, // 这会与san冲突
		}

		runner := &Runner{options: options}
		err := runner.validateOptions()
		require.Error(t, err, "san与其他探测标志应该冲突")
		require.Contains(t, err.Error(), "san or cn flag cannot be used with other probes", "错误信息应该正确")
	})

	t.Run("certs-only扫描模式验证", func(t *testing.T) {
		options := &clients.Options{
			ScanMode:    "ctls", // 不兼容的扫描模式
			Timeout:     10,
			Retries:     2,
			Concurrency: 5,
			Ports:       []string{"443"},
			Inputs:      []string{"example.com"},
			CertsOnly:   true,
		}

		runner := &Runner{options: options}
		err := runner.validateOptions()
		require.Error(t, err, "certs-only应该要求ztls或auto模式")
		require.Contains(t, err.Error(), "scan-mode must be ztls or auto with certs-only option", "错误信息应该正确")
	})

	t.Run("强制ztls模式设置", func(t *testing.T) {
		options := &clients.Options{
			ScanMode:    "ctls",
			Timeout:     10,
			Retries:     2,
			Concurrency: 5,
			Ports:       []string{"443"},
			Inputs:      []string{"example.com"},
			Ja3:         true, // 这会强制设置ztls模式
		}

		runner := &Runner{options: options}
		err := runner.validateOptions()
		require.NoError(t, err, "验证应该成功")
		require.Equal(t, "ztls", runner.options.ScanMode, "应该强制设置为ztls模式")
	})

	t.Run("详细模式设置", func(t *testing.T) {
		options := &clients.Options{
			ScanMode:    "ctls",
			Timeout:     10,
			Retries:     2,
			Concurrency: 5,
			Ports:       []string{"443"},
			Inputs:      []string{"example.com"},
			Verbose:     true,
		}

		runner := &Runner{options: options}
		err := runner.validateOptions()
		require.NoError(t, err, "验证应该成功")
		// 详细模式的验证需要检查日志级别，这里只验证不出错
	})
}

// TestExecuteBasic 测试Execute方法的基本功能
// 验证主执行流程
func TestExecuteBasic(t *testing.T) {
	t.Run("基本执行流程", func(t *testing.T) {
		options := &clients.Options{
			ScanMode:    "ctls",
			Timeout:     5, // 较短的超时时间
			Retries:     1,
			Concurrency: 2, // 较小的并发数
			Ports:       []string{"443"},
			Inputs:      []string{"**************"}, // 使用IP地址避免DNS解析问题
		}

		runner, err := New(options)
		require.NoError(t, err, "创建运行器应该成功")
		defer runner.Close()

		// 执行扫描
		err = runner.Execute()
		require.NoError(t, err, "执行应该成功")
	})

	t.Run("空输入执行", func(t *testing.T) {
		options := &clients.Options{
			ScanMode:    "ctls",
			Timeout:     5,
			Retries:     1,
			Concurrency: 2,
			Ports:       []string{"443"},
			Inputs:      []string{}, // 空输入
		}

		runner, err := New(options)
		if err != nil {
			// 如果创建时就失败，说明验证正常工作
			require.Contains(t, err.Error(), "no input provided", "应该提示没有输入")
			return
		}
		defer runner.Close()

		// 如果创建成功，执行应该也能正常处理空输入
		err = runner.Execute()
		require.NoError(t, err, "空输入执行应该成功")
	})

	t.Run("多输入执行", func(t *testing.T) {
		options := &clients.Options{
			ScanMode:    "ctls",
			Timeout:     5,
			Retries:     1,
			Concurrency: 2,
			Ports:       []string{"443"},
			Inputs:      []string{"**************", "127.0.0.1"}, // 多个IP
		}

		runner, err := New(options)
		require.NoError(t, err, "创建运行器应该成功")
		defer runner.Close()

		// 执行扫描
		err = runner.Execute()
		require.NoError(t, err, "多输入执行应该成功")
	})
}

// TestExecuteWithDelay 测试带延迟的执行
// 验证延迟功能
func TestExecuteWithDelay(t *testing.T) {
	t.Run("有效延迟设置", func(t *testing.T) {
		options := &clients.Options{
			ScanMode:    "ctls",
			Timeout:     5,
			Retries:     1,
			Concurrency: 1, // 单线程以便测试延迟
			Ports:       []string{"443"},
			Inputs:      []string{"127.0.0.1"},
			Delay:       "100ms", // 100毫秒延迟
		}

		runner, err := New(options)
		require.NoError(t, err, "创建运行器应该成功")
		defer runner.Close()

		// 记录开始时间
		start := time.Now()

		// 执行扫描
		err = runner.Execute()
		require.NoError(t, err, "带延迟执行应该成功")

		// 验证执行时间（应该包含延迟时间）
		elapsed := time.Since(start)
		t.Logf("执行时间: %v", elapsed)
		// 注意：由于网络延迟等因素，这里不做严格的时间验证
	})

	t.Run("无效延迟格式", func(t *testing.T) {
		options := &clients.Options{
			ScanMode:    "ctls",
			Timeout:     5,
			Retries:     1,
			Concurrency: 1,
			Ports:       []string{"443"},
			Inputs:      []string{"127.0.0.1"},
			Delay:       "invalid-delay", // 无效延迟格式
		}

		runner, err := New(options)
		require.NoError(t, err, "创建运行器应该成功")
		defer runner.Close()

		// 执行应该成功，但会记录延迟解析错误
		err = runner.Execute()
		require.NoError(t, err, "无效延迟格式执行应该成功（忽略延迟）")
	})
}

// TestEdgeCases 测试边界情况
// 验证各种边界条件的处理
func TestEdgeCases(t *testing.T) {
	t.Run("零并发数", func(t *testing.T) {
		options := &clients.Options{
			ScanMode:    "ctls",
			Timeout:     5,
			Retries:     1,
			Concurrency: 0, // 零并发数
			Ports:       []string{"443"},
			Inputs:      []string{"127.0.0.1"},
		}

		runner, err := New(options)
		require.NoError(t, err, "创建运行器应该成功")
		defer runner.Close()

		// 执行可能会有问题，因为没有工作协程
		err = runner.Execute()
		// 这里不强制要求成功或失败，取决于具体实现
		t.Logf("零并发数执行结果: %v", err)
	})

	t.Run("大量端口", func(t *testing.T) {
		// 生成大量端口
		var ports []string
		for i := 8000; i < 8100; i++ {
			ports = append(ports, string(rune(i)))
		}

		options := &clients.Options{
			ScanMode:    "ctls",
			Timeout:     1, // 很短的超时
			Retries:     1,
			Concurrency: 5,
			Ports:       ports,
			Inputs:      []string{"127.0.0.1"},
		}

		runner, err := New(options)
		require.NoError(t, err, "创建运行器应该成功")
		defer runner.Close()

		// 执行应该能处理大量端口
		err = runner.Execute()
		require.NoError(t, err, "大量端口执行应该成功")
	})

	t.Run("空字符串输入", func(t *testing.T) {
		options := &clients.Options{
			ScanMode:    "ctls",
			Timeout:     5,
			Retries:     1,
			Concurrency: 2,
			Ports:       []string{"443"},
			Inputs:      []string{"", "   ", "127.0.0.1"}, // 包含空字符串和空白字符串
		}

		runner, err := New(options)
		require.NoError(t, err, "创建运行器应该成功")
		defer runner.Close()

		// 执行应该能正确处理空字符串
		err = runner.Execute()
		require.NoError(t, err, "包含空字符串的执行应该成功")
	})

	t.Run("特殊字符输入", func(t *testing.T) {
		options := &clients.Options{
			ScanMode:    "ctls",
			Timeout:     5,
			Retries:     1,
			Concurrency: 2,
			Ports:       []string{"443"},
			Inputs:      []string{"测试.com", "example.com/path?query=1", "127.0.0.1"}, // 特殊字符
		}

		runner, err := New(options)
		require.NoError(t, err, "创建运行器应该成功")
		defer runner.Close()

		// 执行应该能处理特殊字符
		err = runner.Execute()
		require.NoError(t, err, "特殊字符输入执行应该成功")
	})
}

// TestConcurrentExecution 测试并发执行
// 验证多线程安全性
func TestConcurrentExecution(t *testing.T) {
	t.Run("高并发执行", func(t *testing.T) {
		options := &clients.Options{
			ScanMode:    "ctls",
			Timeout:     5,
			Retries:     1,
			Concurrency: 10, // 高并发
			Ports:       []string{"443", "80", "8080", "8443"},
			Inputs:      []string{"127.0.0.1", "**************"},
		}

		runner, err := New(options)
		require.NoError(t, err, "创建运行器应该成功")
		defer runner.Close()

		// 执行高并发扫描
		err = runner.Execute()
		require.NoError(t, err, "高并发执行应该成功")
	})

	t.Run("并发创建多个运行器", func(t *testing.T) {
		var wg sync.WaitGroup
		errors := make(chan error, 5)

		// 并发创建多个运行器
		for i := 0; i < 5; i++ {
			wg.Add(1)
			go func(id int) {
				defer wg.Done()

				options := &clients.Options{
					ScanMode:    "ctls",
					Timeout:     5,
					Retries:     1,
					Concurrency: 2,
					Ports:       []string{"443"},
					Inputs:      []string{"127.0.0.1"},
				}

				runner, err := New(options)
				if err != nil {
					errors <- err
					return
				}
				defer runner.Close()

				err = runner.Execute()
				if err != nil {
					errors <- err
				}
			}(i)
		}

		wg.Wait()
		close(errors)

		// 检查是否有错误
		for err := range errors {
			require.NoError(t, err, "并发创建运行器应该成功")
		}
	})
}
