//
// Author: chenjb
// Version: V1.0
// Date: 2025-06-26
// FilePath: /yaml_scan/pkg/tlsx/tlsx/ztls/ja3/ja3_test.go
// Description: ja3包的单元测试

package ja3

import (
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"strings"
	"sync"
	"testing"
	"time"

	"github.com/zmap/zcrypto/tls"
	"github.com/stretchr/testify/require"
)

// TestConstants 测试常量定义
// 验证包中定义的常量值是否正确
func TestConstants(t *testing.T) {
	t.Run("字节常量验证", func(t *testing.T) {
		require.Equal(t, byte(45), dashByte, "dashByte应该是ASCII码45（字符'-'）")
		require.Equal(t, byte(44), commaByte, "commaByte应该是ASCII码44（字符','）")

		// 验证字符对应关系
		require.Equal(t, '-', rune(dashByte), "dashByte应该对应字符'-'")
		require.Equal(t, ',', rune(commaByte), "commaByte应该对应字符','")
	})

	t.Run("GREASE位掩码验证", func(t *testing.T) {
		require.Equal(t, uint16(0x0F0F), greaseBitmask, "GREASE位掩码应该是0x0F0F")

		// 验证GREASE检测逻辑
		greaseValues := []uint16{0x0A0A, 0x1A1A, 0x2A2A, 0x3A3A, 0x4A4A, 0x5A5A}
		for _, val := range greaseValues {
			require.Equal(t, uint16(0x0A0A), val&greaseBitmask, "GREASE值应该符合位掩码模式: 0x%04X", val)
		}

		// 验证非GREASE值
		nonGreaseValues := []uint16{0x0000, 0x0001, 0x1234, 0xFFFF}
		for _, val := range nonGreaseValues {
			require.NotEqual(t, uint16(0x0A0A), val&greaseBitmask, "非GREASE值不应该符合位掩码模式: 0x%04X", val)
		}
	})

	t.Run("TLS扩展常量验证", func(t *testing.T) {
		// 验证IANA分配的标准扩展
		require.Equal(t, uint16(0), extensionServerName, "服务器名称扩展应该是0")
		require.Equal(t, uint16(5), extensionStatusRequest, "状态请求扩展应该是5")
		require.Equal(t, uint16(10), extensionSupportedCurves, "支持的曲线扩展应该是10")
		require.Equal(t, uint16(11), extensionSupportedPoints, "支持的点格式扩展应该是11")
		require.Equal(t, uint16(13), extensionSignatureAlgorithms, "签名算法扩展应该是13")
		require.Equal(t, uint16(15), extensionHeartbeat, "心跳扩展应该是15")
		require.Equal(t, uint16(16), extensionALPN, "ALPN扩展应该是16")
		require.Equal(t, uint16(18), extensionSCT, "SCT扩展应该是18")
		require.Equal(t, uint16(23), extensionExtendedMasterSecret, "扩展主密钥扩展应该是23")
		require.Equal(t, uint16(35), extensionSessionTicket, "会话票据扩展应该是35")

		// 验证非标准扩展
		require.Equal(t, uint16(13172), extensionNextProtoNeg, "下一协议协商扩展应该是13172")
		require.Equal(t, uint16(0xff01), extensionRenegotiationInfo, "重新协商信息扩展应该是0xff01")
		require.Equal(t, uint16(0x0028), extensionExtendedRandom, "扩展随机数扩展应该是0x0028")
	})

	t.Log("所有常量验证通过")
}

// TestMD5Hashing 测试MD5哈希计算功能
// 验证JA3和JA3S使用的MD5哈希计算的正确性
func TestMD5Hashing(t *testing.T) {
	// 辅助函数：计算MD5哈希
	calculateMD5 := func(data []byte) string {
		h := md5.Sum(data)
		return hex.EncodeToString(h[:])
	}

	tests := []struct {
		name     string // 测试用例名称
		input    []byte // 输入数据
		expected string // 期望的MD5哈希值
	}{
		{
			name:     "空数据",
			input:    []byte{},
			expected: "d41d8cd98f00b204e9800998ecf8427e",
		},
		{
			name:     "简单字符串",
			input:    []byte("hello"),
			expected: "5d41402abc4b2a76b9719d911017c592",
		},
		{
			name:     "JA3测试字符串",
			input:    []byte("771,4865-4866-4867,0-23-65281,29-23-24,0"),
			expected: "cd08e31494f9531f560d64c695473da9",
		},
		{
			name:     "JA3S测试字符串",
			input:    []byte("771,4865,0-23"),
			expected: "b32309a26951912be7dba376398abc3b",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := calculateMD5(tt.input)
			require.Equal(t, tt.expected, result, "MD5哈希值应该匹配")
			require.Len(t, result, 32, "MD5哈希值应该是32个字符")

			// 验证哈希只包含十六进制字符
			for _, char := range result {
				require.True(t,
					(char >= '0' && char <= '9') ||
					(char >= 'a' && char <= 'f'),
					"MD5哈希应该只包含小写十六进制字符: %c", char)
			}
		})
	}
}

// TestAppendExtension 测试扩展添加功能
// 验证appendExtension函数的GREASE过滤和扩展添加逻辑
func TestAppendExtension(t *testing.T) {
	t.Run("添加非GREASE扩展", func(t *testing.T) {
		tests := []struct {
			name      string
			initial   []byte
			extension uint16
			expected  string
		}{
			{
				name:      "添加服务器名称扩展",
				initial:   []byte{},
				extension: extensionServerName,
				expected:  "0-",
			},
			{
				name:      "添加ALPN扩展",
				initial:   []byte("0-"),
				extension: extensionALPN,
				expected:  "0-16-",
			},
			{
				name:      "添加会话票据扩展",
				initial:   []byte("0-16-"),
				extension: extensionSessionTicket,
				expected:  "0-16-35-",
			},
		}

		for _, tt := range tests {
			t.Run(tt.name, func(t *testing.T) {
				result := appendExtension(tt.initial, tt.extension)
				require.Equal(t, tt.expected, string(result), "扩展添加结果应该匹配")
			})
		}
	})

	t.Run("过滤GREASE扩展", func(t *testing.T) {
		greaseValues := []uint16{0x0A0A, 0x1A1A, 0x2A2A, 0x3A3A, 0x4A4A, 0x5A5A}
		initial := []byte("0-")

		for _, greaseVal := range greaseValues {
			result := appendExtension(initial, greaseVal)
			require.Equal(t, string(initial), string(result),
				"GREASE值应该被过滤: 0x%04X", greaseVal)
		}
	})

	t.Run("混合GREASE和非GREASE扩展", func(t *testing.T) {
		initial := []byte{}

		// 添加非GREASE扩展
		result := appendExtension(initial, extensionServerName)
		require.Equal(t, "0-", string(result))

		// 添加GREASE扩展（应该被过滤）
		result = appendExtension(result, 0x0A0A)
		require.Equal(t, "0-", string(result))

		// 添加另一个非GREASE扩展
		result = appendExtension(result, extensionALPN)
		require.Equal(t, "0-16-", string(result))
	})
}

// TestGetJa3Hash 测试JA3客户端指纹生成功能
// 验证GetJa3Hash函数的正确性
func TestGetJa3Hash(t *testing.T) {
	t.Run("基本JA3指纹生成", func(t *testing.T) {
		// 创建测试用的ClientHello
		clientHello := &tls.ClientHello{
			Version: 0x0303, // TLS 1.2
			CipherSuites: []uint16{
				0x1301, // TLS_AES_128_GCM_SHA256
				0x1302, // TLS_AES_256_GCM_SHA384
				0x1303, // TLS_CHACHA20_POLY1305_SHA256
			},
			ServerName: "example.com", // 触发SNI扩展
			SupportedCurves: []tls.CurveID{
				tls.CurveP256,
				tls.CurveP384,
			},
			SupportedPoints: []uint8{0, 1}, // uncompressed, ansiX962_compressed_prime
			ExtendedMasterSecret: true,     // 触发扩展主密钥扩展
		}

		// 生成JA3指纹
		ja3Hash := GetJa3Hash(clientHello)

		// 验证JA3哈希
		require.NotEmpty(t, ja3Hash, "JA3哈希不应该为空")
		require.Len(t, ja3Hash, 32, "JA3哈希应该是32个字符")

		// 验证哈希只包含十六进制字符
		for _, char := range ja3Hash {
			require.True(t,
				(char >= '0' && char <= '9') ||
				(char >= 'a' && char <= 'f'),
				"JA3哈希应该只包含小写十六进制字符: %c", char)
		}

		t.Logf("生成的JA3指纹: %s", ja3Hash)
	})

	t.Run("空ClientHello处理", func(t *testing.T) {
		// 创建最小的ClientHello
		clientHello := &tls.ClientHello{
			Version:      0x0303,
			CipherSuites: []uint16{},
		}

		// 生成JA3指纹
		ja3Hash := GetJa3Hash(clientHello)

		// 验证JA3哈希
		require.NotEmpty(t, ja3Hash, "即使是空ClientHello也应该生成JA3哈希")
		require.Len(t, ja3Hash, 32, "JA3哈希应该是32个字符")

		t.Logf("空ClientHello的JA3指纹: %s", ja3Hash)
	})

	t.Run("包含GREASE值的ClientHello", func(t *testing.T) {
		// 创建包含GREASE值的ClientHello
		clientHello := &tls.ClientHello{
			Version: 0x0303, // TLS 1.2
			CipherSuites: []uint16{
				0x0A0A, // GREASE值，应该被包含（密码套件不过滤GREASE）
				0x1301, // TLS_AES_128_GCM_SHA256
				0x1A1A, // GREASE值，应该被包含
				0x1302, // TLS_AES_256_GCM_SHA384
			},
			ServerName: "example.com",
			SupportedCurves: []tls.CurveID{
				tls.CurveID(0x0A0A), // GREASE曲线，应该被包含（曲线不过滤GREASE）
				tls.CurveP256,
				tls.CurveP384,
			},
			SupportedPoints: []uint8{0, 1},
			UnknownExtensions: [][]byte{
				{0x0A, 0x0A, 0x00, 0x00}, // GREASE扩展，应该被过滤
				{0x00, 0x10, 0x00, 0x00}, // 非GREASE扩展，应该被包含
			},
		}

		// 生成JA3指纹
		ja3Hash := GetJa3Hash(clientHello)

		// 验证JA3哈希
		require.NotEmpty(t, ja3Hash, "包含GREASE值的ClientHello应该生成JA3哈希")
		require.Len(t, ja3Hash, 32, "JA3哈希应该是32个字符")

		t.Logf("包含GREASE值的JA3指纹: %s", ja3Hash)
	})

	t.Run("所有扩展类型测试", func(t *testing.T) {
		// 创建包含各种扩展的ClientHello
		clientHello := &tls.ClientHello{
			Version:               0x0303,
			CipherSuites:          []uint16{0x1301},
			ServerName:            "test.com",
			OcspStapling:          true,
			SupportedCurves:       []tls.CurveID{tls.CurveP256},
			SupportedPoints:       []uint8{0},
			TicketSupported:       true,
			SignatureAndHashes:    []tls.SignatureAndHash{{Hash: 4, Signature: 1}},
			SecureRenegotiation:   true,
			AlpnProtocols:         []string{"h2", "http/1.1"},
			HeartbeatSupported:    true,
			ExtendedRandom:        []byte{1, 2, 3, 4},
			ExtendedMasterSecret:  true,
			SctEnabled:            true,
		}

		// 生成JA3指纹
		ja3Hash := GetJa3Hash(clientHello)

		// 验证JA3哈希
		require.NotEmpty(t, ja3Hash, "包含所有扩展的ClientHello应该生成JA3哈希")
		require.Len(t, ja3Hash, 32, "JA3哈希应该是32个字符")

		t.Logf("包含所有扩展的JA3指纹: %s", ja3Hash)
	})
}

// TestGetJa3sHash 测试JA3S服务器指纹生成功能
// 验证GetJa3sHash函数的正确性
func TestGetJa3sHash(t *testing.T) {
	t.Run("基本JA3S指纹生成", func(t *testing.T) {
		// 创建测试用的ServerHello
		serverHello := &tls.ServerHello{
			Version:     0x0303, // TLS 1.2
			CipherSuite: 0x1301, // TLS_AES_128_GCM_SHA256
			OcspStapling:         true,
			TicketSupported:      true,
			SecureRenegotiation:  true,
			ExtendedMasterSecret: true,
		}

		// 生成JA3S指纹
		ja3sHash := GetJa3sHash(serverHello)

		// 验证JA3S哈希
		require.NotEmpty(t, ja3sHash, "JA3S哈希不应该为空")
		require.Len(t, ja3sHash, 32, "JA3S哈希应该是32个字符")

		// 验证哈希只包含十六进制字符
		for _, char := range ja3sHash {
			require.True(t,
				(char >= '0' && char <= '9') ||
				(char >= 'a' && char <= 'f'),
				"JA3S哈希应该只包含小写十六进制字符: %c", char)
		}

		t.Logf("生成的JA3S指纹: %s", ja3sHash)
	})

	t.Run("空ServerHello处理", func(t *testing.T) {
		// 创建最小的ServerHello
		serverHello := &tls.ServerHello{
			Version:     0x0303,
			CipherSuite: 0x0000,
		}

		// 生成JA3S指纹
		ja3sHash := GetJa3sHash(serverHello)

		// 验证JA3S哈希
		require.NotEmpty(t, ja3sHash, "即使是空ServerHello也应该生成JA3S哈希")
		require.Len(t, ja3sHash, 32, "JA3S哈希应该是32个字符")

		t.Logf("空ServerHello的JA3S指纹: %s", ja3sHash)
	})

	t.Run("包含未知扩展的ServerHello", func(t *testing.T) {
		// 创建包含未知扩展的ServerHello
		serverHello := &tls.ServerHello{
			Version:     0x0303,
			CipherSuite: 0x1301,
			UnknownExtensions: [][]byte{
				{0x0A, 0x0A, 0x00, 0x00}, // GREASE扩展，应该被过滤
				{0x00, 0x10, 0x00, 0x00}, // 非GREASE扩展，应该被包含
				{0x12, 0x34, 0x00, 0x00}, // 自定义扩展，应该被包含
			},
		}

		// 生成JA3S指纹
		ja3sHash := GetJa3sHash(serverHello)

		// 验证JA3S哈希
		require.NotEmpty(t, ja3sHash, "包含未知扩展的ServerHello应该生成JA3S哈希")
		require.Len(t, ja3sHash, 32, "JA3S哈希应该是32个字符")

		t.Logf("包含未知扩展的JA3S指纹: %s", ja3sHash)
	})

	t.Run("所有扩展类型测试", func(t *testing.T) {
		// 创建包含各种扩展的ServerHello
		serverHello := &tls.ServerHello{
			Version:              0x0303,
			CipherSuite:          0x1301,
			OcspStapling:         true,
			TicketSupported:      true,
			SecureRenegotiation:  true,
			HeartbeatSupported:   true,
			ExtendedRandom:       []byte{1, 2, 3, 4},
			ExtendedMasterSecret: true,
		}

		// 生成JA3S指纹
		ja3sHash := GetJa3sHash(serverHello)

		// 验证JA3S哈希
		require.NotEmpty(t, ja3sHash, "包含所有扩展的ServerHello应该生成JA3S哈希")
		require.Len(t, ja3sHash, 32, "JA3S哈希应该是32个字符")

		t.Logf("包含所有扩展的JA3S指纹: %s", ja3sHash)
	})
}

// TestJa3sFromServerHello 测试从ServerHello生成JA3S指纹功能
// 验证JA3S指纹的正确生成
func TestJa3sFromServerHello(t *testing.T) {
	// 创建测试用的ServerHello
	serverHello := &tls.ServerHello{
		Version:     0x0303, // TLS 1.2
		CipherSuite: 0x1301, // TLS_AES_128_GCM_SHA256
		Extensions: []tls.Extension{
			{Type: extensionExtendedMasterSecret, Data: []byte{}},
			{Type: extensionSessionTicket, Data: []byte{}},
		},
	}

	// 生成JA3S指纹
	ja3sHash := Ja3sFromServerHello(serverHello)
	
	// 验证JA3S哈希
	require.NotEmpty(t, ja3sHash, "JA3S哈希不应该为空")
	require.Len(t, ja3sHash, 32, "JA3S哈希应该是32个字符")
	
	// 验证哈希只包含十六进制字符
	for _, char := range ja3sHash {
		require.True(t, 
			(char >= '0' && char <= '9') || 
			(char >= 'a' && char <= 'f'), 
			"JA3S哈希应该只包含小写十六进制字符: %c", char)
	}
	
	t.Logf("生成的JA3S指纹: %s", ja3sHash)
}

// TestJa3FromClientHelloWithGrease 测试包含GREASE值的ClientHello
// 验证GREASE值被正确过滤
func TestJa3FromClientHelloWithGrease(t *testing.T) {
	// 创建包含GREASE值的ClientHello
	clientHello := &tls.ClientHello{
		Version: 0x0303, // TLS 1.2
		CipherSuites: []uint16{
			0x0A0A, // GREASE值，应该被过滤
			0x1301, // TLS_AES_128_GCM_SHA256
			0x1A1A, // GREASE值，应该被过滤
			0x1302, // TLS_AES_256_GCM_SHA384
		},
		Extensions: []tls.Extension{
			{Type: 0x0A0A, Data: []byte{}}, // GREASE扩展，应该被过滤
			{Type: extensionServerName, Data: []byte("example.com")},
			{Type: extensionExtendedMasterSecret, Data: []byte{}},
		},
		SupportedCurves: []tls.CurveID{
			tls.CurveID(0x0A0A), // GREASE曲线，应该被过滤
			tls.CurveP256,
			tls.CurveP384,
		},
		SupportedPoints: []uint8{0, 1},
	}

	// 生成JA3指纹
	ja3Hash := Ja3FromClientHello(clientHello)
	
	// 验证JA3哈希
	require.NotEmpty(t, ja3Hash, "JA3哈希不应该为空")
	require.Len(t, ja3Hash, 32, "JA3哈希应该是32个字符")
	
	t.Logf("过滤GREASE后的JA3指纹: %s", ja3Hash)
}

// TestJa3Consistency 测试JA3指纹的一致性
// 验证相同的ClientHello生成相同的JA3指纹
func TestJa3Consistency(t *testing.T) {
	// 创建测试用的ClientHello
	clientHello := &tls.ClientHello{
		Version: 0x0303,
		CipherSuites: []uint16{0x1301, 0x1302},
		Extensions: []tls.Extension{
			{Type: extensionServerName, Data: []byte("test.com")},
		},
		SupportedCurves: []tls.CurveID{tls.CurveP256},
		SupportedPoints: []uint8{0},
	}

	// 多次生成JA3指纹
	ja3Hash1 := Ja3FromClientHello(clientHello)
	ja3Hash2 := Ja3FromClientHello(clientHello)
	ja3Hash3 := Ja3FromClientHello(clientHello)
	
	// 验证一致性
	require.Equal(t, ja3Hash1, ja3Hash2, "相同ClientHello的JA3指纹应该一致")
	require.Equal(t, ja3Hash2, ja3Hash3, "相同ClientHello的JA3指纹应该一致")
	
	t.Logf("JA3指纹一致性验证通过: %s", ja3Hash1)
}

// TestJa3sConsistency 测试JA3S指纹的一致性
// 验证相同的ServerHello生成相同的JA3S指纹
func TestJa3sConsistency(t *testing.T) {
	// 创建测试用的ServerHello
	serverHello := &tls.ServerHello{
		Version:     0x0303,
		CipherSuite: 0x1301,
		Extensions: []tls.Extension{
			{Type: extensionExtendedMasterSecret, Data: []byte{}},
		},
	}

	// 多次生成JA3S指纹
	ja3sHash1 := Ja3sFromServerHello(serverHello)
	ja3sHash2 := Ja3sFromServerHello(serverHello)
	ja3sHash3 := Ja3sFromServerHello(serverHello)
	
	// 验证一致性
	require.Equal(t, ja3sHash1, ja3sHash2, "相同ServerHello的JA3S指纹应该一致")
	require.Equal(t, ja3sHash2, ja3sHash3, "相同ServerHello的JA3S指纹应该一致")
	
	t.Logf("JA3S指纹一致性验证通过: %s", ja3sHash1)
}

// TestEmptyClientHello 测试空ClientHello的处理
// 验证空或最小ClientHello的JA3指纹生成
func TestEmptyClientHello(t *testing.T) {
	// 创建最小的ClientHello
	clientHello := &tls.ClientHello{
		Version:      0x0303,
		CipherSuites: []uint16{},
		Extensions:   []tls.Extension{},
	}

	// 生成JA3指纹
	ja3Hash := Ja3FromClientHello(clientHello)
	
	// 验证JA3哈希
	require.NotEmpty(t, ja3Hash, "即使是空ClientHello也应该生成JA3哈希")
	require.Len(t, ja3Hash, 32, "JA3哈希应该是32个字符")
	
	t.Logf("空ClientHello的JA3指纹: %s", ja3Hash)
}

// TestEmptyServerHello 测试空ServerHello的处理
// 验证空或最小ServerHello的JA3S指纹生成
func TestEmptyServerHello(t *testing.T) {
	// 创建最小的ServerHello
	serverHello := &tls.ServerHello{
		Version:     0x0303,
		CipherSuite: 0x0000,
		Extensions:  []tls.Extension{},
	}

	// 生成JA3S指纹
	ja3sHash := Ja3sFromServerHello(serverHello)
	
	// 验证JA3S哈希
	require.NotEmpty(t, ja3sHash, "即使是空ServerHello也应该生成JA3S哈希")
	require.Len(t, ja3sHash, 32, "JA3S哈希应该是32个字符")
	
	t.Logf("空ServerHello的JA3S指纹: %s", ja3sHash)
}
