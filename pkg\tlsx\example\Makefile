# TLS扫描库示例 Makefile
# Author: chenjb
# Version: V1.0
# Date: 2025-07-01

.PHONY: all run-all run-basic run-quick run-advanced run-config clean help

# 默认目标
all: help

# 运行所有示例
run-all: run-quick run-basic run-advanced run-config

# 运行基本示例（完整功能演示）
run-basic:
	@echo "=== 运行基本示例 ==="
	go run main.go

# 运行快速开始示例
run-quick:
	@echo "=== 运行快速开始示例 ==="
	go run quickstart.go

# 运行高级用法示例
run-advanced:
	@echo "=== 运行高级用法示例 ==="
	go run advanced.go

# 运行配置示例
run-config:
	@echo "=== 运行配置示例 ==="
	go run config_example.go

# 构建所有示例
build:
	@echo "=== 构建示例程序 ==="
	mkdir -p bin
	go build -o bin/main main.go
	go build -o bin/quickstart quickstart.go
	go build -o bin/advanced advanced.go
	go build -o bin/config_example config_example.go

# 运行测试
test:
	@echo "=== 运行测试 ==="
	go test -v ../...

# 格式化代码
fmt:
	@echo "=== 格式化代码 ==="
	go fmt ./...

# 检查代码
vet:
	@echo "=== 检查代码 ==="
	go vet ./...

# 清理生成的文件
clean:
	@echo "=== 清理文件 ==="
	rm -rf bin/
	rm -f *.json
	rm -f *.txt
	rm -f *.log

# 显示帮助信息
help:
	@echo "TLS扫描库示例 Makefile"
	@echo ""
	@echo "可用目标:"
	@echo "  run-all      - 运行所有示例"
	@echo "  run-basic    - 运行基本示例（main.go）"
	@echo "  run-quick    - 运行快速开始示例（quickstart.go）"
	@echo "  run-advanced - 运行高级用法示例（advanced.go）"
	@echo "  run-config   - 运行配置示例（config_example.go）"
	@echo "  build        - 构建所有示例程序"
	@echo "  test         - 运行测试"
	@echo "  fmt          - 格式化代码"
	@echo "  vet          - 检查代码"
	@echo "  clean        - 清理生成的文件"
	@echo "  help         - 显示此帮助信息"
	@echo ""
	@echo "示例用法:"
	@echo "  make run-quick    # 快速开始"
	@echo "  make run-all      # 运行所有示例"
	@echo "  make clean        # 清理文件"
