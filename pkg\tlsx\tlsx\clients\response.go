// Author: chenjb
// Version: V1.0
// Date: 2025-06-24 17:31:40
// FilePath: /yaml_scan/pkg/tlsx/tlsx/clients/response.go
// Description: TLS扫描响应结构定义

// Package clients 定义TLS扫描的响应数据结构
// 包含连接信息、证书详情、指纹哈希等完整的TLS扫描结果
package clients

import (
	ztls "github.com/zmap/zcrypto/tls" // ZMap TLS库，用于详细的TLS协议分析
	"time"                            // 时间处理
)

// Response 表示单次TLS扫描的完整响应结果
// 包含连接状态、证书信息、协议详情和各种指纹哈希
type Response struct {
	// === 基本连接信息 ===
	Timestamp *time.Time `json:"timestamp,omitempty"` // 扫描时间戳，记录响应生成时间
	Host      string     `json:"host"`                // 目标主机名或域名
	IP        string     `json:"ip,omitempty"`        // 目标IP地址
	Port      string     `json:"port"`                // 目标端口号

	// === 连接状态 ===
	ProbeStatus bool   `json:"probe_status"`     // TLS探测是否成功（true=成功，false=失败）
	Error       string `json:"error,omitempty"`  // 连接失败时的错误信息

	// === TLS协议信息 ===
	Version string `json:"tls_version,omitempty"` // 协商使用的TLS版本（如"1.2", "1.3"）
	Cipher  string `json:"cipher,omitempty"`      // 协商使用的密码套件名称

	// === 证书信息 ===
	*CertificateResponse `json:",inline"` // 内联的叶证书详细信息

	// === 客户端信息 ===
	TLSConnection string `json:"tls_connection,omitempty"` // 使用的TLS客户端类型（auto模式时显示）

	// === 证书链 ===
	Chain []*CertificateResponse `json:"chain,omitempty"` // 完整的证书链（从叶证书到根证书）

	// === 指纹哈希 ===
	JarmHash string `json:"jarm_hash,omitempty"` // JARM TLS服务器指纹哈希
	Ja3Hash  string `json:"ja3_hash,omitempty"`  // JA3客户端指纹哈希
	Ja3sHash string `json:"ja3s_hash,omitempty"` // JA3S服务器指纹哈希

	// === 服务器名称指示 ===
	ServerName string `json:"sni,omitempty"` // 使用的SNI（Server Name Indication）

	// === 枚举结果 ===
	VersionEnum []string      `json:"version_enum,omitempty"` // 服务器支持的所有TLS版本列表
	TlsCiphers  []TlsCiphers  `json:"cipher_enum,omitempty"`  // 按TLS版本分组的密码套件支持情况

	// === 协议详细信息（仅ztls模式） ===
	ClientHello *ztls.ClientHello `json:"client_hello,omitempty"` // 客户端Hello消息的详细内容
	ServerHello *ztls.ServerHello `json:"servers_hello,omitempty"` // 服务器Hello消息的详细内容

	// === 客户端认证 ===
	ClientCertRequired *bool `json:"client_cert_required,omitempty"` // 服务器是否要求客户端证书认证
}

// TlsCiphers 表示特定TLS版本下服务器支持的密码套件集合
// 用于密码套件枚举功能的结果展示
type TlsCiphers struct {
	Version string      `json:"version,omitempty"` // TLS版本标识（如"1.2", "1.3"）
	Ciphers CipherTypes `json:"ciphers,omitempty"` // 该版本下按安全级别分类的密码套件
}

// CipherTypes 按安全级别分类的密码套件集合
// 根据密码套件的加密强度和安全性进行分类
type CipherTypes struct {
	Weak     []string `json:"weak,omitempty"`     // 弱密码套件：安全性一般，不推荐使用
	Insecure []string `json:"insecure,omitempty"` // 不安全密码套件：存在已知安全漏洞，应避免使用
	Secure   []string `json:"secure,omitempty"`   // 安全密码套件：加密强度高，推荐使用
	Unknown  []string `json:"unknown,omitempty"`  // 未知类型密码套件：tlsx无法识别安全级别的密码套件
}

// CertificateResponse 包含X.509证书的完整解析信息
// 涵盖证书状态、主体信息、颁发者信息、有效期等所有重要字段
type CertificateResponse struct {
	// === 证书状态检查 ===
	Expired    bool `json:"expired,omitempty"`     // 证书是否已过期
	SelfSigned bool `json:"self_signed,omitempty"` // 证书是否为自签名
	MisMatched bool `json:"mismatched,omitempty"`  // 证书是否与目标主机名不匹配
	Revoked    bool `json:"revoked,omitempty"`     // 证书是否已被撤销
	Untrusted  bool `json:"untrusted,omitempty"`   // 证书是否不受信任（如使用未知CA）

	// === 证书有效期 ===
	NotBefore time.Time `json:"not_before,omitempty"` // 证书生效时间
	NotAfter  time.Time `json:"not_after,omitempty"`  // 证书过期时间

	// === 证书主体信息 ===
	SubjectDN  string   `json:"subject_dn,omitempty"`  // 主体可分辨名称（完整DN字符串）
	SubjectCN  string   `json:"subject_cn,omitempty"`  // 主体通用名称（通常是域名）
	SubjectOrg []string `json:"subject_org,omitempty"` // 主体组织名称列表
	SubjectAN  []string `json:"subject_an,omitempty"`  // 主体备用名称列表（SAN）

	// === 域名信息 ===
	Domains []string `json:"domains,omitempty"` // 去重后的所有域名（CN + SAN）

	// === 证书标识 ===
	Serial string `json:"serial,omitempty"` // 证书序列号（十六进制格式）

	// === 颁发者信息 ===
	IssuerDN  string   `json:"issuer_dn,omitempty"`  // 颁发者可分辨名称（完整DN字符串）
	IssuerCN  string   `json:"issuer_cn,omitempty"`  // 颁发者通用名称（CA名称）
	IssuerOrg []string `json:"issuer_org,omitempty"` // 颁发者组织名称列表

	// === 联系信息 ===
	Emails []string `json:"emails,omitempty"` // 证书中包含的电子邮件地址列表

	// === 证书指纹 ===
	FingerprintHash CertificateResponseFingerprintHash `json:"fingerprint_hash,omitempty"` // 多种哈希算法的指纹

	// === 原始证书数据 ===
	Certificate string `json:"certificate,omitempty"` // PEM格式的完整证书内容

	// === 证书类型 ===
	WildCardCert bool `json:"wildcard_certificate,omitempty"` // 是否为通配符证书（支持*.domain.com）
}

// CertificateResponseFingerprintHash 包含证书的多种哈希指纹
// 提供不同强度的哈希算法用于证书识别和验证
type CertificateResponseFingerprintHash struct {
	MD5    string `json:"md5,omitempty"`    // MD5哈希指纹（128位，已不推荐用于安全用途）
	SHA1   string `json:"sha1,omitempty"`   // SHA1哈希指纹（160位，安全性较弱）
	SHA256 string `json:"sha256,omitempty"` // SHA256哈希指纹（256位，推荐使用）
}
