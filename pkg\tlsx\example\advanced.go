//
// Author: chenjb
// Version: V1.0
// Date: 2025-07-01
// FilePath: /yaml_scan/pkg/tlsx/example/advanced.go
// Description: TLS扫描库高级用法示例

package main

import (
	"fmt"
	"log"
	"os"
	"time"

	"yaml_scan/pkg/tlsx/output"
	"yaml_scan/pkg/tlsx/runner"
	"yaml_scan/pkg/tlsx/tlsx"
	"yaml_scan/pkg/tlsx/tlsx/clients"
)

// advancedTLSScanning 高级TLS扫描示例
// 演示完整的TLS安全评估功能
func advancedTLSScanning() {
	fmt.Println("=== 高级TLS安全评估 ===")

	// 创建高级扫描配置
	options := &clients.Options{
		ScanMode:          "ztls",     // 使用ztls获得最佳功能支持
		Timeout:           15,         // 较长超时时间
		Retries:           3,
		Concurrency:       1,
		TlsVersionsEnum:   true,       // TLS版本枚举
		TlsCiphersEnum:    true,       // 密码套件枚举
		Jarm:              true,       // JARM指纹
		Ja3:               true,       // JA3指纹
		Ja3s:              true,       // JA3S指纹
		Expired:           true,       // 检查过期证书
		SelfSigned:        true,       // 检查自签名证书
		MisMatched:        true,       // 检查主机名不匹配
		Revoked:           true,       // 检查撤销证书
		WildcardCertCheck: true,       // 检查通配符证书
		Hash:              "md5,sha1,sha256", // 多种哈希算法
	}

	// 创建TLS服务
	service, err := tlsx.New(options)
	if err != nil {
		log.Fatalf("创建TLS服务失败: %v", err)
	}

	// 使用ConnectWithOptions进行高级连接
	connectOptions := clients.ConnectOptions{
		SNI: "www.baidu.com", // 指定SNI
	}

	response, err := service.ConnectWithOptions("www.baidu.com", "", "443", connectOptions)
	if err != nil {
		log.Printf("连接失败: %v", err)
		return
	}

	// 输出详细的安全评估结果
	fmt.Printf("=== TLS连接信息 ===\n")
	fmt.Printf("主机: %s:%s\n", response.Host, response.Port)
	fmt.Printf("SNI: %s\n", response.ServerName)
	fmt.Printf("TLS版本: %s\n", response.Version)
	fmt.Printf("密码套件: %s\n", response.Cipher)
	fmt.Printf("支持的TLS版本: %v\n", response.VersionEnum)

	// 输出指纹信息
	fmt.Printf("\n=== 指纹信息 ===\n")
	fmt.Printf("JARM指纹: %s\n", response.JarmHash)
	fmt.Printf("JA3指纹: %s\n", response.Ja3Hash)
	fmt.Printf("JA3S指纹: %s\n", response.Ja3sHash)

	// 输出证书详细信息
	if response.CertificateResponse != nil {
		cert := response.CertificateResponse
		fmt.Printf("\n=== 证书信息 ===\n")
		fmt.Printf("主题CN: %s\n", cert.SubjectCN)
		fmt.Printf("主题组织: %v\n", cert.SubjectOrg)
		fmt.Printf("主题备用名称: %v\n", cert.SubjectAN)
		fmt.Printf("证书序列号: %s\n", cert.Serial)
		
		// 输出证书指纹
		fmt.Printf("\n=== 证书指纹 ===\n")
		fmt.Printf("MD5: %s\n", cert.FingerprintHash.MD5)
		fmt.Printf("SHA1: %s\n", cert.FingerprintHash.SHA1)
		fmt.Printf("SHA256: %s\n", cert.FingerprintHash.SHA256)

		// 输出安全状态
		fmt.Printf("\n=== 安全状态 ===\n")
		fmt.Printf("证书过期: %t\n", cert.Expired)
		fmt.Printf("自签名证书: %t\n", cert.SelfSigned)
		fmt.Printf("主机名不匹配: %t\n", cert.MisMatched)
		fmt.Printf("证书已撤销: %t\n", cert.Revoked)
		fmt.Printf("不受信任: %t\n", cert.Untrusted)
		fmt.Printf("通配符证书: %t\n", cert.WildCardCert)
	}

	// 输出密码套件分析
	if len(response.TlsCiphers) > 0 {
		fmt.Printf("\n=== 密码套件分析 ===\n")
		for _, tlsCipher := range response.TlsCiphers {
			fmt.Printf("TLS版本 %s:\n", tlsCipher.Version)
			if len(tlsCipher.Ciphers.Secure) > 0 {
				fmt.Printf("  ✓ 安全密码套件 (%d个): %v\n", len(tlsCipher.Ciphers.Secure), tlsCipher.Ciphers.Secure)
			}
			if len(tlsCipher.Ciphers.Weak) > 0 {
				fmt.Printf("  ⚠ 弱密码套件 (%d个): %v\n", len(tlsCipher.Ciphers.Weak), tlsCipher.Ciphers.Weak)
			}
			if len(tlsCipher.Ciphers.Insecure) > 0 {
				fmt.Printf("  ✗ 不安全密码套件 (%d个): %v\n", len(tlsCipher.Ciphers.Insecure), tlsCipher.Ciphers.Insecure)
			}
		}
	}
	fmt.Println()
}

// batchScanningWithOutput 带输出文件的批量扫描
func batchScanningWithOutput() {
	fmt.Println("=== 批量扫描并输出到文件 ===")

	// 创建临时输出文件
	outputFile := "tls_scan_results.json"

	// 创建批量扫描配置
	options := &clients.Options{
		ScanMode:    "ctls",
		Timeout:     10,
		Retries:     2,
		Concurrency: 3,
		Ports:       []string{"443"},
		Inputs:      []string{"www.baidu.com", "github.com", "google.com"},
		JSON:        true,           // JSON格式输出
		OutputFile:  outputFile,     // 输出到文件
		TLSVersion:  true,
		Cipher:      true,
		SAN:         true,
	}

	// 创建运行器
	tlsRunner, err := runner.New(options)
	if err != nil {
		log.Fatalf("创建运行器失败: %v", err)
	}
	defer tlsRunner.Close()

	// 执行批量扫描
	fmt.Printf("开始批量扫描，结果将保存到: %s\n", outputFile)
	start := time.Now()
	
	err = tlsRunner.Execute()
	if err != nil {
		log.Printf("批量扫描失败: %v", err)
		return
	}

	elapsed := time.Since(start)
	fmt.Printf("批量扫描完成，耗时: %v\n", elapsed)

	// 检查输出文件
	if _, err := os.Stat(outputFile); err == nil {
		fmt.Printf("扫描结果已保存到: %s\n", outputFile)
		
		// 读取并显示文件大小
		fileInfo, _ := os.Stat(outputFile)
		fmt.Printf("文件大小: %d 字节\n", fileInfo.Size())
	}
	fmt.Println()
}

// customConnectOptions 自定义连接选项示例
func customConnectOptions() {
	fmt.Println("=== 自定义连接选项 ===")

	options := &clients.Options{
		ScanMode: "ctls",
		Timeout:  10,
		Retries:  2,
	}

	service, err := tlsx.New(options)
	if err != nil {
		log.Fatalf("创建TLS服务失败: %v", err)
	}

	// 使用不同的连接选项
	connectOptions := clients.ConnectOptions{
		SNI:     "api.github.com",  // 自定义SNI
		// 可以添加更多自定义选项
	}

	response, err := service.ConnectWithOptions("github.com", "", "443", connectOptions)
	if err != nil {
		log.Printf("连接失败: %v", err)
		return
	}

	fmt.Printf("主机: %s:%s\n", response.Host, response.Port)
	fmt.Printf("SNI: %s\n", response.ServerName)
	fmt.Printf("TLS版本: %s\n", response.Version)
	
	if response.CertificateResponse != nil {
		fmt.Printf("证书CN: %s\n", response.CertificateResponse.SubjectCN)
		fmt.Printf("证书SAN: %v\n", response.CertificateResponse.SubjectAN)
	}
	fmt.Println()
}

// 主函数
func main() {
	fmt.Println("TLS扫描库高级用法示例")
	fmt.Println("=====================")
	fmt.Println()

	// 运行高级示例
	advancedTLSScanning()
	
	time.Sleep(2 * time.Second)
	batchScanningWithOutput()
	
	time.Sleep(1 * time.Second)
	customConnectOptions()

	fmt.Println("高级示例运行完成！")
}
