<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TLS扫描库 - API参考文档</title>
    <style>
        :root {
            --primary: #3b82f6;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --dark: #1f2937;
            --light: #f8fafc;
            --border: #e5e7eb;
        }

        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Segoe UI', sans-serif;
            line-height: 1.6;
            color: var(--dark);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
            backdrop-filter: blur(10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            color: var(--primary);
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .nav {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
        }

        .nav ul {
            list-style: none;
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 20px;
        }

        .nav a {
            color: var(--dark);
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 10px;
            transition: all 0.3s;
            font-weight: 500;
        }

        .nav a:hover {
            background: var(--primary);
            color: white;
        }

        .section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .section h2 {
            color: var(--primary);
            font-size: 2rem;
            margin-bottom: 20px;
            border-bottom: 3px solid var(--primary);
            padding-bottom: 10px;
        }

        .section h3 {
            color: var(--primary);
            font-size: 1.5rem;
            margin: 25px 0 15px 0;
        }

        .api-method {
            background: #f8fafc;
            border-left: 4px solid var(--primary);
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 10px 10px 0;
        }

        .method-signature {
            font-family: 'Courier New', monospace;
            background: var(--dark);
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            overflow-x: auto;
        }

        .param-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .param-table th {
            background: var(--primary);
            color: white;
            padding: 12px;
            text-align: left;
            font-weight: 600;
        }

        .param-table td {
            padding: 12px;
            border-bottom: 1px solid var(--border);
        }

        .param-table tr:hover {
            background: #f8fafc;
        }

        .type-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 600;
            color: white;
            margin-right: 5px;
        }

        .type-string { background: var(--success); }
        .type-int { background: var(--warning); }
        .type-bool { background: var(--danger); }
        .type-slice { background: var(--primary); }
        .type-struct { background: #6b7280; }

        .example {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }

        .example h4 {
            color: #0369a1;
            margin-bottom: 10px;
        }

        .code {
            background: var(--dark);
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            overflow-x: auto;
            margin: 10px 0;
        }

        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-stable { background: var(--success); }
        .status-beta { background: var(--warning); }
        .status-experimental { background: var(--danger); }

        @media (max-width: 768px) {
            .container { padding: 10px; }
            .section { padding: 20px; }
            .nav ul { flex-direction: column; text-align: center; }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <header class="header">
            <h1>🔧 TLS扫描库 API参考</h1>
            <p>完整的API接口文档和使用指南</p>
        </header>

        <!-- 导航 -->
        <nav class="nav">
            <ul>
                <li><a href="#core-api">核心API</a></li>
                <li><a href="#options">配置选项</a></li>
                <li><a href="#responses">响应结构</a></li>
                <li><a href="#clients">客户端接口</a></li>
                <li><a href="#examples">使用示例</a></li>
                <li><a href="index.html">返回主文档</a></li>
            </ul>
        </nav>

        <!-- 核心API -->
        <section id="core-api" class="section">
            <h2>核心API接口</h2>
            
            <div class="api-method">
                <h3>tlsx.New()</h3>
                <p>创建新的TLS服务实例</p>
                
                <div class="method-signature">
func New(options *clients.Options) (*Service, error)
                </div>
                
                <table class="param-table">
                    <thead>
                        <tr>
                            <th>参数</th>
                            <th>类型</th>
                            <th>描述</th>
                            <th>必需</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>options</td>
                            <td><span class="type-badge type-struct">*clients.Options</span></td>
                            <td>TLS扫描配置选项</td>
                            <td>是</td>
                        </tr>
                    </tbody>
                </table>
                
                <p><strong>返回值:</strong></p>
                <ul>
                    <li><code>*Service</code> - TLS服务实例</li>
                    <li><code>error</code> - 创建过程中的错误</li>
                </ul>
                
                <div class="example">
                    <h4>示例</h4>
                    <div class="code">options := &clients.Options{
    ScanMode: "ctls",
    Timeout:  10,
    Retries:  3,
}

service, err := tlsx.New(options)
if err != nil {
    log.Fatal(err)
}</div>
                </div>
            </div>

            <div class="api-method">
                <h3>Service.Connect()</h3>
                <p>连接到指定的TLS服务器</p>
                
                <div class="method-signature">
func (s *Service) Connect(hostname, ip, port string) (*Response, error)
                </div>
                
                <table class="param-table">
                    <thead>
                        <tr>
                            <th>参数</th>
                            <th>类型</th>
                            <th>描述</th>
                            <th>必需</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>hostname</td>
                            <td><span class="type-badge type-string">string</span></td>
                            <td>目标主机名或域名</td>
                            <td>是</td>
                        </tr>
                        <tr>
                            <td>ip</td>
                            <td><span class="type-badge type-string">string</span></td>
                            <td>目标IP地址（可选）</td>
                            <td>否</td>
                        </tr>
                        <tr>
                            <td>port</td>
                            <td><span class="type-badge type-string">string</span></td>
                            <td>目标端口号</td>
                            <td>是</td>
                        </tr>
                    </tbody>
                </table>
                
                <div class="example">
                    <h4>示例</h4>
                    <div class="code">response, err := service.Connect("www.example.com", "", "443")
if err != nil {
    log.Printf("连接失败: %v", err)
    return
}

fmt.Printf("TLS版本: %s\n", response.Version)
fmt.Printf("密码套件: %s\n", response.Cipher)</div>
                </div>
            </div>

            <div class="api-method">
                <h3>Service.ConnectWithOptions()</h3>
                <p>使用自定义选项连接到TLS服务器</p>
                
                <div class="method-signature">
func (s *Service) ConnectWithOptions(hostname, ip, port string, options ConnectOptions) (*Response, error)
                </div>
                
                <table class="param-table">
                    <thead>
                        <tr>
                            <th>参数</th>
                            <th>类型</th>
                            <th>描述</th>
                            <th>必需</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>hostname</td>
                            <td><span class="type-badge type-string">string</span></td>
                            <td>目标主机名或域名</td>
                            <td>是</td>
                        </tr>
                        <tr>
                            <td>ip</td>
                            <td><span class="type-badge type-string">string</span></td>
                            <td>目标IP地址（可选）</td>
                            <td>否</td>
                        </tr>
                        <tr>
                            <td>port</td>
                            <td><span class="type-badge type-string">string</span></td>
                            <td>目标端口号</td>
                            <td>是</td>
                        </tr>
                        <tr>
                            <td>options</td>
                            <td><span class="type-badge type-struct">ConnectOptions</span></td>
                            <td>连接选项配置</td>
                            <td>是</td>
                        </tr>
                    </tbody>
                </table>
                
                <div class="example">
                    <h4>示例</h4>
                    <div class="code">connectOptions := clients.ConnectOptions{
    SNI:        "api.example.com",
    VersionTLS: "1.3",
}

response, err := service.ConnectWithOptions(
    "example.com", "", "443", connectOptions)
if err != nil {
    log.Printf("连接失败: %v", err)
    return
}</div>
                </div>
            </div>
        </section>

        <!-- 配置选项 -->
        <section id="options" class="section">
            <h2>配置选项详解</h2>

            <h3>clients.Options 结构体</h3>
            <p>包含所有TLS扫描的配置参数</p>

            <table class="param-table">
                <thead>
                    <tr>
                        <th>字段名</th>
                        <th>类型</th>
                        <th>默认值</th>
                        <th>描述</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>ScanMode</td>
                        <td><span class="type-badge type-string">string</span></td>
                        <td>"auto"</td>
                        <td>扫描模式: ctls, ztls, openssl, auto</td>
                    </tr>
                    <tr>
                        <td>Timeout</td>
                        <td><span class="type-badge type-int">int</span></td>
                        <td>10</td>
                        <td>连接超时时间（秒）</td>
                    </tr>
                    <tr>
                        <td>Retries</td>
                        <td><span class="type-badge type-int">int</span></td>
                        <td>3</td>
                        <td>连接失败重试次数</td>
                    </tr>
                    <tr>
                        <td>Concurrency</td>
                        <td><span class="type-badge type-int">int</span></td>
                        <td>25</td>
                        <td>并发扫描线程数</td>
                    </tr>
                    <tr>
                        <td>Ports</td>
                        <td><span class="type-badge type-slice">[]string</span></td>
                        <td>["443"]</td>
                        <td>要扫描的端口列表</td>
                    </tr>
                    <tr>
                        <td>Inputs</td>
                        <td><span class="type-badge type-slice">[]string</span></td>
                        <td>nil</td>
                        <td>目标主机列表</td>
                    </tr>
                    <tr>
                        <td>JSON</td>
                        <td><span class="type-badge type-bool">bool</span></td>
                        <td>false</td>
                        <td>是否使用JSON格式输出</td>
                    </tr>
                    <tr>
                        <td>Verbose</td>
                        <td><span class="type-badge type-bool">bool</span></td>
                        <td>false</td>
                        <td>是否启用详细输出</td>
                    </tr>
                    <tr>
                        <td>TlsVersionsEnum</td>
                        <td><span class="type-badge type-bool">bool</span></td>
                        <td>false</td>
                        <td>是否枚举TLS版本</td>
                    </tr>
                    <tr>
                        <td>TlsCiphersEnum</td>
                        <td><span class="type-badge type-bool">bool</span></td>
                        <td>false</td>
                        <td>是否枚举密码套件</td>
                    </tr>
                    <tr>
                        <td>Jarm</td>
                        <td><span class="type-badge type-bool">bool</span></td>
                        <td>false</td>
                        <td>是否计算JARM指纹</td>
                    </tr>
                    <tr>
                        <td>Ja3</td>
                        <td><span class="type-badge type-bool">bool</span></td>
                        <td>false</td>
                        <td>是否计算JA3指纹</td>
                    </tr>
                    <tr>
                        <td>Ja3s</td>
                        <td><span class="type-badge type-bool">bool</span></td>
                        <td>false</td>
                        <td>是否计算JA3S指纹</td>
                    </tr>
                    <tr>
                        <td>Expired</td>
                        <td><span class="type-badge type-bool">bool</span></td>
                        <td>false</td>
                        <td>是否检查证书过期</td>
                    </tr>
                    <tr>
                        <td>SelfSigned</td>
                        <td><span class="type-badge type-bool">bool</span></td>
                        <td>false</td>
                        <td>是否检查自签名证书</td>
                    </tr>
                    <tr>
                        <td>MisMatched</td>
                        <td><span class="type-badge type-bool">bool</span></td>
                        <td>false</td>
                        <td>是否检查主机名不匹配</td>
                    </tr>
                    <tr>
                        <td>Revoked</td>
                        <td><span class="type-badge type-bool">bool</span></td>
                        <td>false</td>
                        <td>是否检查证书撤销</td>
                    </tr>
                </tbody>
            </table>

            <h3>ConnectOptions 结构体</h3>
            <p>单次连接的特定选项</p>

            <table class="param-table">
                <thead>
                    <tr>
                        <th>字段名</th>
                        <th>类型</th>
                        <th>描述</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>SNI</td>
                        <td><span class="type-badge type-string">string</span></td>
                        <td>TLS服务器名称指示</td>
                    </tr>
                    <tr>
                        <td>VersionTLS</td>
                        <td><span class="type-badge type-string">string</span></td>
                        <td>指定的TLS版本</td>
                    </tr>
                    <tr>
                        <td>Ciphers</td>
                        <td><span class="type-badge type-slice">[]string</span></td>
                        <td>要尝试的密码套件列表</td>
                    </tr>
                    <tr>
                        <td>EnumMode</td>
                        <td><span class="type-badge type-struct">EnumMode</span></td>
                        <td>枚举模式（None/Version/Cipher）</td>
                    </tr>
                </tbody>
            </table>
        </section>

        <!-- 响应结构 -->
        <section id="responses" class="section">
            <h2>响应结构详解</h2>

            <h3>Response 结构体</h3>
            <p>TLS连接的完整响应信息</p>

            <div class="method-signature">
type Response struct {
    Timestamp           time.Time            `json:"timestamp"`
    Host                string               `json:"host"`
    Port                string               `json:"port"`
    IP                  string               `json:"ip,omitempty"`
    ProbeStatus         bool                 `json:"probe_status"`
    Version             string               `json:"version,omitempty"`
    Cipher              string               `json:"cipher,omitempty"`
    ServerName          string               `json:"server_name,omitempty"`
    JarmHash            string               `json:"jarm_hash,omitempty"`
    Ja3Hash             string               `json:"ja3_hash,omitempty"`
    Ja3sHash            string               `json:"ja3s_hash,omitempty"`
    CertificateResponse *CertificateResponse `json:"certificate_response,omitempty"`
    VersionEnum         []string             `json:"version_enum,omitempty"`
    TlsCiphers          []TlsCipher          `json:"tls_ciphers,omitempty"`
}
            </div>

            <h3>CertificateResponse 结构体</h3>
            <p>证书相关的详细信息</p>

            <div class="method-signature">
type CertificateResponse struct {
    SubjectCN       string          `json:"subject_cn,omitempty"`
    SubjectAN       []string        `json:"subject_an,omitempty"`
    SubjectOrg      []string        `json:"subject_org,omitempty"`
    Domains         []string        `json:"domains,omitempty"`
    Expired         bool            `json:"expired"`
    SelfSigned      bool            `json:"self_signed"`
    MisMatched      bool            `json:"mismatched"`
    Revoked         bool            `json:"revoked"`
    Untrusted       bool            `json:"untrusted"`
    WildCardCert    bool            `json:"wildcard_cert"`
    Serial          string          `json:"serial,omitempty"`
    FingerprintHash FingerprintHash `json:"fingerprint_hash,omitempty"`
    Certificate     string          `json:"certificate,omitempty"`
}
            </div>

            <h3>TlsCipher 结构体</h3>
            <p>TLS密码套件信息</p>

            <div class="method-signature">
type TlsCipher struct {
    Version string      `json:"version"`
    Ciphers CipherEnum  `json:"ciphers"`
}

type CipherEnum struct {
    Secure   []string `json:"secure,omitempty"`
    Weak     []string `json:"weak,omitempty"`
    Insecure []string `json:"insecure,omitempty"`
}
            </div>
        </section>

        <!-- 客户端接口 -->
        <section id="clients" class="section">
            <h2>客户端接口</h2>

            <h3>Implementation 接口</h3>
            <p>所有TLS客户端实现必须遵循的接口</p>

            <div class="method-signature">
type Implementation interface {
    ConnectWithOptions(hostname, ip, port string, options ConnectOptions) (*Response, error)
    EnumerateCiphers(hostname, ip, port string, options ConnectOptions) ([]string, error)
    SupportedTLSVersions() ([]string, error)
    SupportedTLSCiphers() ([]string, error)
}
            </div>

            <h3>Runner 批量扫描器</h3>
            <div class="api-method">
                <h4>runner.New()</h4>
                <p>创建批量扫描器实例</p>

                <div class="method-signature">
func New(options *clients.Options) (*Runner, error)
                </div>

                <div class="example">
                    <h4>示例</h4>
                    <div class="code">options := &clients.Options{
    ScanMode:    "ctls",
    Concurrency: 10,
    Ports:       []string{"443", "8443"},
    Inputs:      []string{"example.com", "test.com"},
    JSON:        true,
}

runner, err := runner.New(options)
if err != nil {
    log.Fatal(err)
}
defer runner.Close()

err = runner.Execute()
if err != nil {
    log.Printf("扫描失败: %v", err)
}</div>
                </div>
            </div>

            <h3>Output 输出处理器</h3>
            <div class="api-method">
                <h4>output.New()</h4>
                <p>创建输出处理器实例</p>

                <div class="method-signature">
func New(options *clients.Options) (Writer, error)
                </div>

                <div class="example">
                    <h4>示例</h4>
                    <div class="code">options := &clients.Options{
    JSON:       true,
    OutputFile: "results.json",
    NoColor:    true,
}

writer, err := output.New(options)
if err != nil {
    log.Fatal(err)
}
defer writer.Close()

// 写入扫描结果
err = writer.Write(response)
if err != nil {
    log.Printf("写入失败: %v", err)
}</div>
                </div>
            </div>
        </section>

        <!-- 使用示例 -->
        <section id="examples" class="section">
            <h2>完整使用示例</h2>

            <h3>基础扫描示例</h3>
            <div class="code">package main

import (
    "fmt"
    "log"
    "yaml_scan/pkg/tlsx/tlsx"
    "yaml_scan/pkg/tlsx/tlsx/clients"
)

func main() {
    // 创建配置
    options := &clients.Options{
        ScanMode:    "ctls",
        Timeout:     10,
        Retries:     3,
        Concurrency: 1,
    }

    // 创建服务
    service, err := tlsx.New(options)
    if err != nil {
        log.Fatalf("创建服务失败: %v", err)
    }

    // 连接目标
    response, err := service.Connect("www.example.com", "", "443")
    if err != nil {
        log.Fatalf("连接失败: %v", err)
    }

    // 输出结果
    fmt.Printf("主机: %s:%s\n", response.Host, response.Port)
    fmt.Printf("TLS版本: %s\n", response.Version)
    fmt.Printf("密码套件: %s\n", response.Cipher)

    if response.CertificateResponse != nil {
        cert := response.CertificateResponse
        fmt.Printf("证书CN: %s\n", cert.SubjectCN)
        fmt.Printf("证书过期: %t\n", cert.Expired)
        fmt.Printf("自签名: %t\n", cert.SelfSigned)
    }
}</div>

            <h3>高级功能示例</h3>
            <div class="code">package main

import (
    "fmt"
    "log"
    "yaml_scan/pkg/tlsx/tlsx"
    "yaml_scan/pkg/tlsx/tlsx/clients"
)

func main() {
    // 高级配置
    options := &clients.Options{
        ScanMode:          "ztls",
        Timeout:           15,
        Retries:           2,
        TlsVersionsEnum:   true,  // 枚举TLS版本
        TlsCiphersEnum:    true,  // 枚举密码套件
        Jarm:              true,  // JARM指纹
        Ja3:               true,  // JA3指纹
        Expired:           true,  // 检查过期证书
        SelfSigned:        true,  // 检查自签名证书
        Hash:              "md5,sha1,sha256", // 证书指纹
    }

    service, err := tlsx.New(options)
    if err != nil {
        log.Fatalf("创建服务失败: %v", err)
    }

    // 使用自定义连接选项
    connectOptions := clients.ConnectOptions{
        SNI: "api.example.com",
    }

    response, err := service.ConnectWithOptions(
        "example.com", "", "443", connectOptions)
    if err != nil {
        log.Fatalf("连接失败: %v", err)
    }

    // 输出详细信息
    fmt.Printf("=== 基本信息 ===\n")
    fmt.Printf("主机: %s:%s\n", response.Host, response.Port)
    fmt.Printf("SNI: %s\n", response.ServerName)
    fmt.Printf("TLS版本: %s\n", response.Version)
    fmt.Printf("密码套件: %s\n", response.Cipher)

    fmt.Printf("\n=== 指纹信息 ===\n")
    fmt.Printf("JARM: %s\n", response.JarmHash)
    fmt.Printf("JA3: %s\n", response.Ja3Hash)
    fmt.Printf("JA3S: %s\n", response.Ja3sHash)

    fmt.Printf("\n=== 版本支持 ===\n")
    fmt.Printf("支持的TLS版本: %v\n", response.VersionEnum)

    if response.CertificateResponse != nil {
        cert := response.CertificateResponse
        fmt.Printf("\n=== 证书信息 ===\n")
        fmt.Printf("主题CN: %s\n", cert.SubjectCN)
        fmt.Printf("主题组织: %v\n", cert.SubjectOrg)
        fmt.Printf("域名列表: %v\n", cert.SubjectAN)
        fmt.Printf("证书序列号: %s\n", cert.Serial)

        fmt.Printf("\n=== 安全状态 ===\n")
        fmt.Printf("过期: %t\n", cert.Expired)
        fmt.Printf("自签名: %t\n", cert.SelfSigned)
        fmt.Printf("主机名不匹配: %t\n", cert.MisMatched)
        fmt.Printf("已撤销: %t\n", cert.Revoked)
        fmt.Printf("通配符证书: %t\n", cert.WildCardCert)

        fmt.Printf("\n=== 证书指纹 ===\n")
        fmt.Printf("MD5: %s\n", cert.FingerprintHash.MD5)
        fmt.Printf("SHA1: %s\n", cert.FingerprintHash.SHA1)
        fmt.Printf("SHA256: %s\n", cert.FingerprintHash.SHA256)
    }

    // 密码套件分析
    if len(response.TlsCiphers) > 0 {
        fmt.Printf("\n=== 密码套件分析 ===\n")
        for _, tlsCipher := range response.TlsCiphers {
            fmt.Printf("TLS %s:\n", tlsCipher.Version)
            if len(tlsCipher.Ciphers.Secure) > 0 {
                fmt.Printf("  安全: %v\n", tlsCipher.Ciphers.Secure)
            }
            if len(tlsCipher.Ciphers.Weak) > 0 {
                fmt.Printf("  弱: %v\n", tlsCipher.Ciphers.Weak)
            }
            if len(tlsCipher.Ciphers.Insecure) > 0 {
                fmt.Printf("  不安全: %v\n", tlsCipher.Ciphers.Insecure)
            }
        }
    }
}</div>
        </section>

        <!-- 页脚 -->
        <footer style="text-align: center; margin-top: 30px; padding: 20px; background: rgba(255, 255, 255, 0.9); border-radius: 15px;">
            <p>📚 <strong>TLS扫描库 API参考文档</strong></p>
            <p>更多信息请查看 <a href="index.html" style="color: var(--primary);">完整文档</a> 或 <a href="../example/" style="color: var(--primary);">示例代码</a></p>
        </footer>
    </div>
</body>
</html>
