# TLS扫描库使用示例

本目录包含了 `yaml_scan/pkg/tlsx` 包的完整使用示例，演示了如何使用该库进行TLS扫描、证书分析和指纹识别。

## 功能特性

- **基本TLS连接**: 连接到目标主机并获取证书信息
- **版本和密码套件枚举**: 枚举服务器支持的TLS版本和密码套件
- **JARM指纹识别**: 生成TLS服务器的JARM指纹
- **批量扫描**: 同时扫描多个目标和端口
- **自定义输出**: 支持JSON和标准格式输出

## 运行示例

```bash
# 进入示例目录
cd pkg/tlsx/example

# 运行完整示例程序
go run main.go

# 运行快速开始示例
go run quickstart.go

# 运行高级用法示例
go run advanced.go

# 或者使用Makefile
make run-all      # 运行所有示例
make run-basic    # 运行基本示例
make run-quick    # 运行快速开始示例
make run-advanced # 运行高级示例
make clean        # 清理生成的文件
```

## 示例说明

### 示例1：基本TLS连接

演示如何创建TLS服务并连接到目标主机，获取基本的证书信息：

```go
// 创建TLS扫描选项
options := &clients.Options{
    ScanMode:    "ctls",    // 使用Go原生TLS实现
    Timeout:     10,        // 连接超时10秒
    Retries:     3,         // 重试3次
    Concurrency: 1,         // 单线程
}

// 创建TLS服务实例
service, err := tlsx.New(options)
if err != nil {
    log.Fatalf("创建TLS服务失败: %v", err)
}

// 连接到目标主机
response, err := service.Connect("www.baidu.com", "", "443")
```

### 示例2：TLS版本和密码套件枚举

演示如何枚举服务器支持的TLS版本和密码套件：

```go
options := &clients.Options{
    ScanMode:         "ztls",  // 使用ztls实现
    TlsVersionsEnum:  true,    // 启用TLS版本枚举
    TlsCiphersEnum:   true,    // 启用密码套件枚举
}
```

### 示例3：JARM指纹识别

演示如何生成TLS服务器的JARM指纹：

```go
options := &clients.Options{
    ScanMode: "auto",  // 使用自动模式
    Jarm:     true,    // 启用JARM指纹识别
}
```

### 示例4：批量扫描

演示如何使用Runner进行批量扫描：

```go
options := &clients.Options{
    Concurrency: 3,                                    // 3个并发线程
    Ports:       []string{"443", "8443"},              // 扫描多个端口
    Inputs:      []string{"www.baidu.com", "github.com"}, // 多个目标
}

// 创建运行器
tlsRunner, err := runner.New(options)
if err != nil {
    log.Fatalf("创建运行器失败: %v", err)
}
defer tlsRunner.Close()

// 执行批量扫描
err = tlsRunner.Execute()
```

### 示例5：自定义输出格式

演示如何使用自定义输出格式：

```go
options := &clients.Options{
    JSON:    true,    // 启用JSON输出
    NoColor: true,
}

// 创建输出写入器
writer, err := output.New(options)
if err != nil {
    log.Fatalf("创建输出写入器失败: %v", err)
}
defer writer.Close()

// 输出结果
err = writer.Write(response)
```

## 配置选项说明

### 扫描模式 (ScanMode)

- `"ctls"`: 使用Go原生TLS实现，稳定性好
- `"ztls"`: 使用ztls实现，提供更好的TLS指纹识别能力
- `"openssl"`: 使用OpenSSL实现，兼容性强
- `"auto"`: 自动模式，根据情况选择最佳实现

### 主要选项

- `Timeout`: 连接超时时间（秒）
- `Retries`: 重试次数
- `Concurrency`: 并发线程数
- `Ports`: 要扫描的端口列表
- `Inputs`: 目标主机列表

### 功能开关

- `TlsVersionsEnum`: 启用TLS版本枚举
- `TlsCiphersEnum`: 启用密码套件枚举
- `Jarm`: 启用JARM指纹识别
- `SAN`: 显示证书主题备用名称
- `CN`: 显示证书通用名称
- `TLSVersion`: 显示TLS版本
- `Cipher`: 显示密码套件

### 输出选项

- `JSON`: 启用JSON格式输出
- `NoColor`: 禁用颜色输出
- `OutputFile`: 输出到文件
- `RespOnly`: 仅输出响应内容

## 注意事项

1. **网络连接**: 示例需要网络连接来访问目标主机
2. **超时设置**: JARM指纹识别需要较长的超时时间
3. **并发控制**: 批量扫描时注意控制并发数，避免对目标服务器造成压力
4. **错误处理**: 实际使用时应该添加适当的错误处理逻辑

## 扩展使用

可以基于这些示例创建更复杂的TLS扫描工具，例如：

- 添加IP范围扫描
- 集成到CI/CD流程中进行安全检查
- 创建TLS配置合规性检查工具
- 实现TLS证书监控系统

## 文件说明

- `main.go`: 完整功能演示，包含所有主要特性
- `quickstart.go`: 快速开始示例，最简单的用法
- `advanced.go`: 高级用法示例，展示复杂场景
- `example_test.go`: 示例测试文件，验证功能正确性
- `go.mod`: Go模块文件
- `Makefile`: 构建和运行脚本
- `README.md`: 本文档

## 测试

运行测试来验证示例的正确性：

```bash
# 运行所有测试
go test -v

# 运行特定测试
go test -v -run TestBasicTLSConnection

# 使用Makefile运行测试
make test
```

## 故障排除

### 常见问题

1. **网络连接失败**
   - 检查网络连接
   - 确认目标主机可访问
   - 调整超时时间

2. **依赖问题**
   - 确保Go版本 >= 1.24
   - 运行 `go mod tidy` 更新依赖

3. **权限问题**
   - 某些功能可能需要管理员权限
   - 检查防火墙设置

### 调试技巧

- 启用详细日志输出
- 增加超时时间
- 减少并发数
- 使用不同的扫描模式

## 性能优化

- 合理设置并发数（建议不超过10）
- 根据网络情况调整超时时间
- 使用适当的扫描模式
- 避免对同一目标频繁扫描

## 相关文档

- [TLS扫描库API文档](../tlsx/tlsx.go)
- [Runner批量扫描文档](../runner/runner.go)
- [输出格式文档](../output/output.go)
- [客户端接口文档](../tlsx/clients/clients.go)
