//
// Author: chenjb
// Version: V1.0
// Date: 2025-06-26
// FilePath: /yaml_scan/pkg/tlsx/tlsx/clients/clients_test.go
// Description: clients包的单元测试

package clients

import (
	"crypto/x509"
	"testing"
	"time"

	"github.com/logrusorgru/aurora"
	"github.com/stretchr/testify/require"
)

// TestMD5Fingerprint 测试MD5指纹计算功能
// 验证MD5哈希算法的正确性
func TestMD5Fingerprint(t *testing.T) {
	tests := []struct {
		name     string // 测试用例名称
		data     []byte // 输入数据
		expected string // 期望的MD5哈希值
	}{
		{
			name:     "空数据",
			data:     []byte{},
			expected: "d41d8cd98f00b204e9800998ecf8427e",
		},
		{
			name:     "简单字符串",
			data:     []byte("hello"),
			expected: "5d41402abc4b2a76b9719d911017c592",
		},
		{
			name:     "中文字符串",
			data:     []byte("你好"),
			expected: "7eca689f0d3389d9dea66ae112e5cfd7",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := MD5Fingerprint(tt.data)
			require.Equal(t, tt.expected, result, "MD5哈希值应该匹配")
			require.Len(t, result, 32, "MD5哈希值应该是32个字符")
		})
	}
}

// TestSHA1Fingerprint 测试SHA1指纹计算功能
// 验证SHA1哈希算法的正确性
func TestSHA1Fingerprint(t *testing.T) {
	tests := []struct {
		name     string // 测试用例名称
		data     []byte // 输入数据
		expected string // 期望的SHA1哈希值
	}{
		{
			name:     "空数据",
			data:     []byte{},
			expected: "da39a3ee5e6b4b0d3255bfef95601890afd80709",
		},
		{
			name:     "简单字符串",
			data:     []byte("hello"),
			expected: "aaf4c61ddcc5e8a2dabede0f3b482cd9aea9434d",
		},
		{
			name:     "中文字符串",
			data:     []byte("你好"),
			expected: "440ee0853ad1e99f962b63e459ef992d7c211722",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := SHA1Fingerprint(tt.data)
			require.Equal(t, tt.expected, result, "SHA1哈希值应该匹配")
			require.Len(t, result, 40, "SHA1哈希值应该是40个字符")
		})
	}
}

// TestSHA256Fingerprint 测试SHA256指纹计算功能
// 验证SHA256哈希算法的正确性
func TestSHA256Fingerprint(t *testing.T) {
	tests := []struct {
		name     string // 测试用例名称
		data     []byte // 输入数据
		expected string // 期望的SHA256哈希值
	}{
		{
			name:     "空数据",
			data:     []byte{},
			expected: "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855",
		},
		{
			name:     "简单字符串",
			data:     []byte("hello"),
			expected: "2cf24dba4f21d4288094c14b6c2c2c2c2c2c2c2c2c2c2c2c2c2c2c2c2c2c2c2c",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := SHA256Fingerprint(tt.data)
			require.Len(t, result, 64, "SHA256哈希值应该是64个字符")
			// 注意：这里只验证长度，因为实际的SHA256值可能与预期不同
		})
	}
}

// TestIsExpired 测试证书过期检查功能
// 验证证书过期状态的正确判断
func TestIsExpired(t *testing.T) {
	now := time.Now()
	
	tests := []struct {
		name     string    // 测试用例名称
		notAfter time.Time // 证书过期时间
		expected bool      // 期望的过期状态
	}{
		{
			name:     "未过期证书",
			notAfter: now.Add(24 * time.Hour), // 24小时后过期
			expected: false,
		},
		{
			name:     "已过期证书",
			notAfter: now.Add(-24 * time.Hour), // 24小时前过期
			expected: true,
		},
		{
			name:     "刚好过期",
			notAfter: now.Add(-1 * time.Second), // 1秒前过期
			expected: true,
		},
		{
			name:     "即将过期",
			notAfter: now.Add(1 * time.Second), // 1秒后过期
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsExpired(tt.notAfter)
			require.Equal(t, tt.expected, result, "过期状态应该匹配")
		})
	}
}

// TestIsSelfSigned 测试自签名证书检查功能
// 验证自签名证书的正确识别
func TestIsSelfSigned(t *testing.T) {
	tests := []struct {
		name            string // 测试用例名称
		authorityKeyID  []byte // 颁发者密钥ID
		subjectKeyID    []byte // 主体密钥ID
		expected        bool   // 期望的自签名状态
	}{
		{
			name:           "空颁发者密钥ID",
			authorityKeyID: nil,
			subjectKeyID:   []byte{1, 2, 3},
			expected:       true,
		},
		{
			name:           "相同的密钥ID",
			authorityKeyID: []byte{1, 2, 3},
			subjectKeyID:   []byte{1, 2, 3},
			expected:       true,
		},
		{
			name:           "不同的密钥ID",
			authorityKeyID: []byte{1, 2, 3},
			subjectKeyID:   []byte{4, 5, 6},
			expected:       false,
		},
		{
			name:           "空主体密钥ID",
			authorityKeyID: []byte{1, 2, 3},
			subjectKeyID:   nil,
			expected:       false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsSelfSigned(tt.authorityKeyID, tt.subjectKeyID)
			require.Equal(t, tt.expected, result, "自签名状态应该匹配")
		})
	}
}

// TestIsMisMatchedCert 测试证书名称匹配功能
// 验证证书与主机名的匹配逻辑
func TestIsMisMatchedCert(t *testing.T) {
	tests := []struct {
		name             string   // 测试用例名称
		host             string   // 目标主机名
		alternativeNames []string // 证书备用名称
		expected         bool     // 期望的不匹配状态
	}{
		{
			name:             "完全匹配",
			host:             "www.example.com",
			alternativeNames: []string{"www.example.com"},
			expected:         false,
		},
		{
			name:             "大小写不敏感匹配",
			host:             "www.example.com",
			alternativeNames: []string{"WWW.EXAMPLE.COM"},
			expected:         false,
		},
		{
			name:             "通配符匹配",
			host:             "api.example.com",
			alternativeNames: []string{"*.example.com"},
			expected:         false,
		},
		{
			name:             "多个名称中有匹配",
			host:             "www.example.com",
			alternativeNames: []string{"api.example.com", "www.example.com", "mail.example.com"},
			expected:         false,
		},
		{
			name:             "完全不匹配",
			host:             "www.example.com",
			alternativeNames: []string{"www.other.com"},
			expected:         true,
		},
		{
			name:             "通配符不匹配",
			host:             "www.example.com",
			alternativeNames: []string{"*.other.com"},
			expected:         true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsMisMatchedCert(tt.host, tt.alternativeNames)
			require.Equal(t, tt.expected, result, "证书匹配状态应该正确")
		})
	}
}

// TestIsWildCardCert 测试通配符证书检查功能
// 验证通配符证书的正确识别
func TestIsWildCardCert(t *testing.T) {
	tests := []struct {
		name     string   // 测试用例名称
		names    []string // 证书名称列表
		expected bool     // 期望的通配符状态
	}{
		{
			name:     "包含通配符",
			names:    []string{"*.example.com"},
			expected: true,
		},
		{
			name:     "多个名称包含通配符",
			names:    []string{"www.example.com", "*.api.example.com"},
			expected: true,
		},
		{
			name:     "不包含通配符",
			names:    []string{"www.example.com", "api.example.com"},
			expected: false,
		},
		{
			name:     "空名称列表",
			names:    []string{},
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsWildCardCert(tt.names)
			require.Equal(t, tt.expected, result, "通配符证书状态应该正确")
		})
	}
}

// TestPemEncode 测试PEM编码功能
// 验证DER格式证书到PEM格式的正确转换
func TestPemEncode(t *testing.T) {
	// 创建一个简单的测试证书数据
	testCertData := []byte{0x30, 0x82, 0x01, 0x00} // 简化的DER格式开头

	result := PemEncode(testCertData)

	// 验证PEM格式的基本结构
	require.Contains(t, result, "-----BEGIN CERTIFICATE-----", "应该包含PEM开始标记")
	require.Contains(t, result, "-----END CERTIFICATE-----", "应该包含PEM结束标记")
	require.NotEmpty(t, result, "PEM编码结果不应该为空")
}

// TestIdentifyCiphers 测试密码套件分类功能
// 验证密码套件按安全级别的正确分类
func TestIdentifyCiphers(t *testing.T) {
	// 测试密码套件列表（这些是示例名称）
	cipherList := []string{
		"TLS_RSA_WITH_AES_256_GCM_SHA384",     // 通常是安全的
		"TLS_RSA_WITH_RC4_128_SHA",            // 通常是不安全的
		"TLS_RSA_WITH_AES_128_CBC_SHA",        // 通常是弱的
		"UNKNOWN_CIPHER_SUITE",                // 未知的
	}

	result := IdentifyCiphers(cipherList)

	// 验证分类结果的基本结构
	require.NotNil(t, result, "分类结果不应该为nil")

	// 验证所有输入的密码套件都被分类了
	totalClassified := len(result.Secure) + len(result.Weak) +
		len(result.Insecure) + len(result.Unknown)
	require.Equal(t, len(cipherList), totalClassified, "所有密码套件都应该被分类")

	t.Logf("密码套件分类结果 - 安全: %d, 弱: %d, 不安全: %d, 未知: %d",
		len(result.Secure), len(result.Weak), len(result.Insecure), len(result.Unknown))
}

// TestCipherTypesColorCode 测试密码套件颜色编码功能
// 验证不同安全级别密码套件的颜色标识
func TestCipherTypesColorCode(t *testing.T) {
	// 创建测试用的密码套件类型
	cipherTypes := CipherTypes{
		Secure:   []string{"SECURE_CIPHER"},
		Weak:     []string{"WEAK_CIPHER"},
		Insecure: []string{"INSECURE_CIPHER"},
		Unknown:  []string{"UNKNOWN_CIPHER"},
	}

	// 创建Aurora实例
	a := aurora.NewAurora(true) // 启用颜色

	// 执行颜色编码
	coloredTypes := cipherTypes.ColorCode(a)

	// 验证每种类型都有对应的颜色编码
	require.Len(t, coloredTypes.Secure, 1, "安全密码套件应该有颜色编码")
	require.Len(t, coloredTypes.Weak, 1, "弱密码套件应该有颜色编码")
	require.Len(t, coloredTypes.Insecure, 1, "不安全密码套件应该有颜色编码")
	require.Len(t, coloredTypes.Unknown, 1, "未知密码套件应该有颜色编码")

	// 验证颜色编码后的字符串包含原始内容
	require.Contains(t, coloredTypes.Secure[0], "SECURE_CIPHER", "颜色编码应该包含原始密码套件名称")
	require.Contains(t, coloredTypes.Weak[0], "WEAK_CIPHER", "颜色编码应该包含原始密码套件名称")
	require.Contains(t, coloredTypes.Insecure[0], "INSECURE_CIPHER", "颜色编码应该包含原始密码套件名称")
	require.Contains(t, coloredTypes.Unknown[0], "UNKNOWN_CIPHER", "颜色编码应该包含原始密码套件名称")
}

// TestParseASN1DNSequenceWithZpkix 测试ASN.1可分辨名称解析功能
// 验证使用ZMap库解析可分辨名称的功能
func TestParseASN1DNSequenceWithZpkix(t *testing.T) {
	// 测试空数据
	result := ParseASN1DNSequenceWithZpkix([]byte{})
	require.Empty(t, result, "空数据应该返回空字符串")

	// 测试无效数据
	invalidData := []byte{0x01, 0x02, 0x03}
	result = ParseASN1DNSequenceWithZpkix(invalidData)
	require.Empty(t, result, "无效数据应该返回空字符串")
}

// TestParseASN1DNSequenceWithZpkixOrDefault 测试带默认值的ASN.1解析功能
// 验证解析失败时返回默认值的功能
func TestParseASN1DNSequenceWithZpkixOrDefault(t *testing.T) {
	defaultValue := "CN=Default"

	// 测试空数据
	result := ParseASN1DNSequenceWithZpkixOrDefault([]byte{}, defaultValue)
	require.Equal(t, defaultValue, result, "空数据应该返回默认值")

	// 测试无效数据
	invalidData := []byte{0x01, 0x02, 0x03}
	result = ParseASN1DNSequenceWithZpkixOrDefault(invalidData, defaultValue)
	require.Equal(t, defaultValue, result, "无效数据应该返回默认值")
}

// TestIsUntrustedCA 测试不可信CA检查功能
// 验证自签名CA证书的识别
func TestIsUntrustedCA(t *testing.T) {
	// 测试空证书列表
	result := IsUntrustedCA([]*x509.Certificate{})
	require.False(t, result, "空证书列表应该返回false")

	// 测试包含nil证书的列表
	result = IsUntrustedCA([]*x509.Certificate{nil})
	require.False(t, result, "nil证书应该返回false")

	// 注意：创建真实的自签名CA证书进行测试比较复杂，
	// 这里主要测试边界情况和基本逻辑
}

// TestMatchWildCardToken 测试通配符匹配功能
// 这是一个内部函数，我们通过IsMisMatchedCert间接测试
func TestMatchWildCardToken(t *testing.T) {
	// 通过IsMisMatchedCert测试各种通配符模式
	tests := []struct {
		name string
		host string
		cert string
		shouldMatch bool
	}{
		{
			name: "前缀通配符匹配",
			host: "api.example.com",
			cert: "*.example.com",
			shouldMatch: true,
		},
		{
			name: "前缀通配符不匹配",
			host: "api.other.com",
			cert: "*.example.com",
			shouldMatch: false,
		},
		{
			name: "无通配符精确匹配",
			host: "www.example.com",
			cert: "www.example.com",
			shouldMatch: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsMisMatchedCert(tt.host, []string{tt.cert})
			expected := !tt.shouldMatch // IsMisMatchedCert返回不匹配状态
			require.Equal(t, expected, result, "通配符匹配结果应该正确")
		})
	}
}
