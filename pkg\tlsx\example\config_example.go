//
// Author: chenjb
// Version: V1.0
// Date: 2025-07-01
// FilePath: /yaml_scan/pkg/tlsx/example/config_example.go
// Description: TLS扫描库配置示例

package main

import (
	"fmt"
	"log"

	"yaml_scan/pkg/tlsx/tlsx"
	"yaml_scan/pkg/tlsx/tlsx/clients"
)

// 预定义的配置模板
var (
	// 基础配置：适用于简单的TLS连接测试
	BasicConfig = &clients.Options{
		ScanMode:    "ctls",
		Timeout:     10,
		Retries:     3,
		Concurrency: 1,
	}

	// 快速扫描配置：适用于大规模扫描
	FastScanConfig = &clients.Options{
		ScanMode:    "ctls",
		Timeout:     5,
		Retries:     1,
		Concurrency: 10,
		JSON:        true,
		NoColor:     true,
	}

	// 深度分析配置：适用于详细的安全评估
	DeepAnalysisConfig = &clients.Options{
		ScanMode:          "ztls",
		Timeout:           20,
		Retries:           3,
		Concurrency:       2,
		TlsVersionsEnum:   true,
		TlsCiphersEnum:    true,
		Jarm:              true,
		Ja3:               true,
		Ja3s:              true,
		Expired:           true,
		SelfSigned:        true,
		MisMatched:        true,
		Revoked:           true,
		WildcardCertCheck: true,
		Hash:              "md5,sha1,sha256",
		SAN:               true,
		CN:                true,
		TLSVersion:        true,
		Cipher:            true,
		Serial:            true,
	}

	// 合规性检查配置：适用于安全合规性检查
	ComplianceConfig = &clients.Options{
		ScanMode:          "ztls",
		Timeout:           15,
		Retries:           2,
		Concurrency:       3,
		TlsVersionsEnum:   true,
		TlsCiphersEnum:    true,
		Expired:           true,
		SelfSigned:        true,
		MisMatched:        true,
		Revoked:           true,
		Untrusted:         true,
		WildcardCertCheck: true,
		ProbeStatus:       true,
		JSON:              true,
	}

	// 监控配置：适用于持续监控
	MonitoringConfig = &clients.Options{
		ScanMode:    "ctls",
		Timeout:     8,
		Retries:     2,
		Concurrency: 5,
		Expired:     true,
		Revoked:     true,
		ProbeStatus: true,
		JSON:        true,
		NoColor:     true,
	}
)

// demonstrateConfig 演示特定配置的使用
func demonstrateConfig(name string, config *clients.Options, target string) {
	fmt.Printf("=== %s 配置演示 ===\n", name)

	// 创建TLS服务
	service, err := tlsx.New(config)
	if err != nil {
		log.Printf("创建TLS服务失败: %v", err)
		return
	}

	// 连接目标
	response, err := service.Connect(target, "", "443")
	if err != nil {
		log.Printf("连接失败: %v", err)
		return
	}

	// 输出关键信息
	fmt.Printf("目标: %s:%s\n", response.Host, response.Port)
	fmt.Printf("TLS版本: %s\n", response.Version)
	fmt.Printf("探测状态: %t\n", response.ProbeStatus)

	// 根据配置显示不同的信息
	if config.TlsVersionsEnum && len(response.VersionEnum) > 0 {
		fmt.Printf("支持的TLS版本: %v\n", response.VersionEnum)
	}

	if config.Jarm && response.JarmHash != "" {
		fmt.Printf("JARM指纹: %s\n", response.JarmHash)
	}

	if response.CertificateResponse != nil {
		cert := response.CertificateResponse
		if config.Expired {
			fmt.Printf("证书过期: %t\n", cert.Expired)
		}
		if config.SelfSigned {
			fmt.Printf("自签名: %t\n", cert.SelfSigned)
		}
		if config.MisMatched {
			fmt.Printf("主机名不匹配: %t\n", cert.MisMatched)
		}
	}

	fmt.Println()
}

// createCustomConfig 创建自定义配置示例
func createCustomConfig() *clients.Options {
	fmt.Println("=== 创建自定义配置 ===")

	// 基于基础配置创建自定义配置
	customConfig := *BasicConfig // 复制基础配置

	// 自定义修改
	customConfig.ScanMode = "auto"
	customConfig.Timeout = 12
	customConfig.Concurrency = 3
	customConfig.TLSVersion = true
	customConfig.Cipher = true
	customConfig.SAN = true

	fmt.Printf("自定义配置:\n")
	fmt.Printf("  扫描模式: %s\n", customConfig.ScanMode)
	fmt.Printf("  超时时间: %d秒\n", customConfig.Timeout)
	fmt.Printf("  并发数: %d\n", customConfig.Concurrency)
	fmt.Printf("  显示TLS版本: %t\n", customConfig.TLSVersion)
	fmt.Printf("  显示密码套件: %t\n", customConfig.Cipher)
	fmt.Printf("  显示SAN: %t\n", customConfig.SAN)
	fmt.Println()

	return &customConfig
}

// configComparison 配置对比示例
func configComparison() {
	fmt.Println("=== 配置对比演示 ===")

	target := "www.baidu.com"
	configs := map[string]*clients.Options{
		"基础配置": BasicConfig,
		"快速扫描": FastScanConfig,
		"深度分析": DeepAnalysisConfig,
	}

	for name, config := range configs {
		fmt.Printf("--- %s ---\n", name)
		fmt.Printf("扫描模式: %s, 超时: %ds, 并发: %d\n",
			config.ScanMode, config.Timeout, config.Concurrency)

		service, err := tlsx.New(config)
		if err != nil {
			log.Printf("创建服务失败: %v", err)
			continue
		}

		response, err := service.Connect(target, "", "443")
		if err != nil {
			log.Printf("连接失败: %v", err)
			continue
		}

		fmt.Printf("结果: %s:%s, TLS: %s\n",
			response.Host, response.Port, response.Version)

		if config.TlsVersionsEnum && len(response.VersionEnum) > 0 {
			fmt.Printf("版本枚举: %v\n", response.VersionEnum)
		}

		if config.Jarm && response.JarmHash != "" {
			fmt.Printf("JARM: %s\n", response.JarmHash)
		}

		fmt.Println()
	}
}

// 主函数
func main() {
	fmt.Println("TLS扫描库配置示例")
	fmt.Println("=================")
	fmt.Println()

	target := "www.baidu.com"

	// 演示不同的预定义配置
	demonstrateConfig("基础", BasicConfig, target)
	demonstrateConfig("快速扫描", FastScanConfig, target)
	demonstrateConfig("深度分析", DeepAnalysisConfig, target)
	demonstrateConfig("合规性检查", ComplianceConfig, target)

	// 创建和使用自定义配置
	customConfig := createCustomConfig()
	demonstrateConfig("自定义", customConfig, target)

	// 配置对比
	configComparison()

	fmt.Println("配置示例演示完成！")
}
