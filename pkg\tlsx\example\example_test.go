//
// Author: chenjb
// Version: V1.0
// Date: 2025-07-01
// FilePath: /yaml_scan/pkg/tlsx/example/example_test.go
// Description: TLS扫描库示例测试

package main

import (
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"yaml_scan/pkg/tlsx/tlsx"
	"yaml_scan/pkg/tlsx/tlsx/clients"
)

// TestBasicTLSConnection 测试基本TLS连接功能
func TestBasicTLSConnection(t *testing.T) {
	// 创建基本配置
	options := &clients.Options{
		ScanMode:    "ctls",
		Timeout:     10,
		Retries:     2,
		Concurrency: 1,
	}

	// 创建TLS服务
	service, err := tlsx.New(options)
	require.NoError(t, err, "创建TLS服务应该成功")

	// 连接到测试主机
	response, err := service.Connect("www.baidu.com", "", "443")
	require.NoError(t, err, "连接应该成功")
	require.NotNil(t, response, "响应不应该为nil")

	// 验证基本响应字段
	require.Equal(t, "www.baidu.com", response.Host, "主机名应该匹配")
	require.Equal(t, "443", response.Port, "端口应该匹配")
	require.NotEmpty(t, response.Version, "TLS版本不应该为空")
	require.NotEmpty(t, response.Cipher, "密码套件不应该为空")
	require.True(t, response.ProbeStatus, "探测状态应该为成功")

	// 验证证书信息
	require.NotNil(t, response.CertificateResponse, "证书响应不应该为nil")
	require.NotEmpty(t, response.CertificateResponse.SubjectCN, "证书CN不应该为空")
}

// TestTLSServiceCreation 测试TLS服务创建
func TestTLSServiceCreation(t *testing.T) {
	testCases := []struct {
		name     string
		scanMode string
		wantErr  bool
	}{
		{
			name:     "ctls模式",
			scanMode: "ctls",
			wantErr:  false,
		},
		{
			name:     "auto模式",
			scanMode: "auto",
			wantErr:  false,
		},
		{
			name:     "默认模式",
			scanMode: "",
			wantErr:  false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			options := &clients.Options{
				ScanMode: tc.scanMode,
				Timeout:  10,
				Retries:  2,
			}

			service, err := tlsx.New(options)
			if tc.wantErr {
				require.Error(t, err, "应该返回错误")
				require.Nil(t, service, "服务应该为nil")
			} else {
				require.NoError(t, err, "不应该返回错误")
				require.NotNil(t, service, "服务不应该为nil")
			}
		})
	}
}

// TestConnectWithOptions 测试带选项的连接
func TestConnectWithOptions(t *testing.T) {
	options := &clients.Options{
		ScanMode: "ctls",
		Timeout:  10,
		Retries:  2,
	}

	service, err := tlsx.New(options)
	require.NoError(t, err, "创建TLS服务应该成功")

	// 测试带SNI的连接
	connectOptions := clients.ConnectOptions{
		SNI: "www.baidu.com",
	}

	response, err := service.ConnectWithOptions("www.baidu.com", "", "443", connectOptions)
	require.NoError(t, err, "带选项连接应该成功")
	require.NotNil(t, response, "响应不应该为nil")
	require.Equal(t, "www.baidu.com", response.ServerName, "SNI应该匹配")
}

// TestInvalidConnection 测试无效连接
func TestInvalidConnection(t *testing.T) {
	options := &clients.Options{
		ScanMode: "ctls",
		Timeout:  5, // 较短超时
		Retries:  1,
	}

	service, err := tlsx.New(options)
	require.NoError(t, err, "创建TLS服务应该成功")

	// 测试无效主机
	response, err := service.Connect("", "", "443")
	require.Error(t, err, "空主机名应该返回错误")
	require.Nil(t, response, "响应应该为nil")

	// 测试无效端口
	response, err = service.Connect("www.baidu.com", "", "")
	require.Error(t, err, "空端口应该返回错误")
	require.Nil(t, response, "响应应该为nil")
}

// TestTimeout 测试超时处理
func TestTimeout(t *testing.T) {
	options := &clients.Options{
		ScanMode: "ctls",
		Timeout:  1, // 很短的超时时间
		Retries:  1,
	}

	service, err := tlsx.New(options)
	require.NoError(t, err, "创建TLS服务应该成功")

	// 连接到一个可能超时的地址
	start := time.Now()
	response, err := service.Connect("192.168.255.255", "", "443") // 不可达地址
	elapsed := time.Since(start)

	// 验证超时行为
	if err != nil {
		// 如果出错，应该在合理时间内返回
		require.Less(t, elapsed, 10*time.Second, "超时应该在合理时间内")
		require.Nil(t, response, "超时时响应应该为nil")
	}
}

// TestConcurrentConnections 测试并发连接
func TestConcurrentConnections(t *testing.T) {
	options := &clients.Options{
		ScanMode:    "ctls",
		Timeout:     10,
		Retries:     2,
		Concurrency: 3,
	}

	service, err := tlsx.New(options)
	require.NoError(t, err, "创建TLS服务应该成功")

	// 并发连接测试
	hosts := []string{"www.baidu.com", "github.com"}
	results := make(chan *clients.Response, len(hosts))
	errors := make(chan error, len(hosts))

	for _, host := range hosts {
		go func(h string) {
			response, err := service.Connect(h, "", "443")
			if err != nil {
				errors <- err
			} else {
				results <- response
			}
		}(host)
	}

	// 收集结果
	successCount := 0
	errorCount := 0
	timeout := time.After(30 * time.Second)

	for i := 0; i < len(hosts); i++ {
		select {
		case response := <-results:
			require.NotNil(t, response, "响应不应该为nil")
			successCount++
		case err := <-errors:
			t.Logf("连接错误: %v", err)
			errorCount++
		case <-timeout:
			t.Fatal("并发连接测试超时")
		}
	}

	t.Logf("并发连接结果: 成功=%d, 失败=%d", successCount, errorCount)
	require.Greater(t, successCount, 0, "至少应该有一个成功的连接")
}
