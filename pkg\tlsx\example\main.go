//
// Author: chenjb
// Version: V1.0
// Date: 2025-07-01
// FilePath: /yaml_scan/pkg/tlsx/example/main.go
// Description: TLS扫描库使用示例程序

// Package main 提供了yaml_scan/pkg/tlsx包的使用示例
// 演示如何使用tlsx库进行TLS扫描、证书分析和指纹识别
package main

import (
	"fmt"
	"log"
	"time"

	"yaml_scan/pkg/tlsx/output"
	"yaml_scan/pkg/tlsx/runner"
	"yaml_scan/pkg/tlsx/tlsx"
	"yaml_scan/pkg/tlsx/tlsx/clients"
)

// 示例1：基本TLS连接和证书信息获取
func basicTLSConnection() {
	fmt.Println("=== 示例1：基本TLS连接 ===")

	// 创建TLS扫描选项
	options := &clients.Options{
		ScanMode:    "ctls",    // 使用Go原生TLS实现
		Timeout:     10,        // 连接超时10秒
		Retries:     3,         // 重试3次
		Concurrency: 1,         // 单线程
	}

	// 创建TLS服务实例
	service, err := tlsx.New(options)
	if err != nil {
		log.Fatalf("创建TLS服务失败: %v", err)
	}

	// 连接到目标主机
	response, err := service.Connect("www.baidu.com", "", "443")
	if err != nil {
		log.Printf("连接失败: %v", err)
		return
	}

	// 输出基本信息
	fmt.Printf("主机: %s:%s\n", response.Host, response.Port)
	fmt.Printf("TLS版本: %s\n", response.Version)
	fmt.Printf("密码套件: %s\n", response.Cipher)
	fmt.Printf("探测状态: %t\n", response.ProbeStatus)

	// 输出证书信息
	if response.CertificateResponse != nil {
		cert := response.CertificateResponse
		fmt.Printf("证书主题CN: %s\n", cert.SubjectCN)
		fmt.Printf("证书组织: %v\n", cert.SubjectOrg)
		fmt.Printf("证书域名: %v\n", cert.SubjectAN)
		fmt.Printf("证书过期: %t\n", cert.Expired)
		fmt.Printf("自签名证书: %t\n", cert.SelfSigned)
		fmt.Printf("证书序列号: %s\n", cert.Serial)
	}
	fmt.Println()
}

// 示例2：TLS版本和密码套件枚举
func tlsEnumeration() {
	fmt.Println("=== 示例2：TLS版本和密码套件枚举 ===")

	// 创建带枚举功能的TLS扫描选项
	options := &clients.Options{
		ScanMode:         "ztls",  // 使用ztls实现，支持更好的枚举功能
		Timeout:          10,
		Retries:          2,
		Concurrency:      1,
		TlsVersionsEnum:  true,    // 启用TLS版本枚举
		TlsCiphersEnum:   true,    // 启用密码套件枚举
	}

	// 创建TLS服务实例
	service, err := tlsx.New(options)
	if err != nil {
		log.Fatalf("创建TLS服务失败: %v", err)
	}

	// 连接并进行枚举
	response, err := service.Connect("www.baidu.com", "", "443")
	if err != nil {
		log.Printf("连接失败: %v", err)
		return
	}

	// 输出枚举结果
	fmt.Printf("主机: %s:%s\n", response.Host, response.Port)
	fmt.Printf("支持的TLS版本: %v\n", response.VersionEnum)

	// 输出密码套件信息
	if len(response.TlsCiphers) > 0 {
		fmt.Println("支持的密码套件:")
		for _, tlsCipher := range response.TlsCiphers {
			fmt.Printf("  TLS版本 %s:\n", tlsCipher.Version)
			if len(tlsCipher.Ciphers.Secure) > 0 {
				fmt.Printf("    安全密码套件: %v\n", tlsCipher.Ciphers.Secure)
			}
			if len(tlsCipher.Ciphers.Weak) > 0 {
				fmt.Printf("    弱密码套件: %v\n", tlsCipher.Ciphers.Weak)
			}
			if len(tlsCipher.Ciphers.Insecure) > 0 {
				fmt.Printf("    不安全密码套件: %v\n", tlsCipher.Ciphers.Insecure)
			}
		}
	}
	fmt.Println()
}

// 示例3：JARM指纹识别
func jarmFingerprinting() {
	fmt.Println("=== 示例3：JARM指纹识别 ===")

	// 创建带JARM指纹识别的TLS扫描选项
	options := &clients.Options{
		ScanMode:    "auto",  // 使用自动模式
		Timeout:     15,      // JARM需要更长的超时时间
		Retries:     2,
		Concurrency: 1,
		Jarm:        true,    // 启用JARM指纹识别
	}

	// 创建TLS服务实例
	service, err := tlsx.New(options)
	if err != nil {
		log.Fatalf("创建TLS服务失败: %v", err)
	}

	// 连接并获取JARM指纹
	response, err := service.Connect("www.baidu.com", "", "443")
	if err != nil {
		log.Printf("连接失败: %v", err)
		return
	}

	// 输出JARM指纹
	fmt.Printf("主机: %s:%s\n", response.Host, response.Port)
	fmt.Printf("JARM指纹: %s\n", response.JarmHash)
	fmt.Println()
}

// 示例4：使用Runner进行批量扫描
func batchScanning() {
	fmt.Println("=== 示例4：批量扫描 ===")

	// 创建批量扫描选项
	options := &clients.Options{
		ScanMode:    "ctls",
		Timeout:     10,
		Retries:     2,
		Concurrency: 3,                                    // 3个并发线程
		Ports:       []string{"443", "8443"},              // 扫描多个端口
		Inputs:      []string{"www.baidu.com", "github.com"}, // 多个目标
		JSON:        false,                                 // 使用标准输出格式
		NoColor:     true,                                  // 禁用颜色
		SAN:         true,                                  // 显示证书SAN信息
		TLSVersion:  true,                                  // 显示TLS版本
		Cipher:      true,                                  // 显示密码套件
	}

	// 创建运行器
	tlsRunner, err := runner.New(options)
	if err != nil {
		log.Fatalf("创建运行器失败: %v", err)
	}
	defer tlsRunner.Close()

	// 执行批量扫描
	fmt.Println("开始批量扫描...")
	err = tlsRunner.Execute()
	if err != nil {
		log.Printf("批量扫描失败: %v", err)
		return
	}
	fmt.Println("批量扫描完成")
	fmt.Println()
}

// 示例5：自定义输出格式
func customOutput() {
	fmt.Println("=== 示例5：自定义输出格式 ===")

	// 创建JSON输出选项
	options := &clients.Options{
		ScanMode:    "ctls",
		Timeout:     10,
		Retries:     2,
		Concurrency: 1,
		JSON:        true,    // 启用JSON输出
		NoColor:     true,
	}

	// 创建输出写入器
	writer, err := output.New(options)
	if err != nil {
		log.Fatalf("创建输出写入器失败: %v", err)
	}
	defer writer.Close()

	// 创建TLS服务
	service, err := tlsx.New(options)
	if err != nil {
		log.Fatalf("创建TLS服务失败: %v", err)
	}

	// 连接并获取响应
	response, err := service.Connect("www.baidu.com", "", "443")
	if err != nil {
		log.Printf("连接失败: %v", err)
		return
	}

	// 使用自定义输出写入器输出结果
	fmt.Println("JSON格式输出:")
	err = writer.Write(response)
	if err != nil {
		log.Printf("输出失败: %v", err)
	}
	fmt.Println()
}

// 主函数：运行所有示例
func main() {
	fmt.Println("TLS扫描库使用示例")
	fmt.Println("==================")
	fmt.Println()

	// 运行各个示例
	basicTLSConnection()
	
	time.Sleep(1 * time.Second) // 避免请求过于频繁
	tlsEnumeration()
	
	time.Sleep(1 * time.Second)
	jarmFingerprinting()
	
	time.Sleep(1 * time.Second)
	batchScanning()
	
	time.Sleep(1 * time.Second)
	customOutput()

	fmt.Println("所有示例运行完成！")
}
