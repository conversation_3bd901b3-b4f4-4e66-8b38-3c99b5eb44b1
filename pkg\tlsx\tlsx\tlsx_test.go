//
// Author: chenjb
// Version: V1.0
// Date: 2025-06-26
// FilePath: /yaml_scan/pkg/tlsx/tlsx/tlsx_test.go
// Description: tlsx包主文件的单元测试

package tlsx

import (
	"fmt"
	"testing"
	"time"
	"yaml_scan/pkg/fastdialer"
	"yaml_scan/pkg/tlsx/tlsx/clients"

	"github.com/stretchr/testify/require"
)

// TestNew 测试Service的创建功能
// 验证不同扫描模式下Service实例的正确创建
func TestNew(t *testing.T) {
	tests := []struct {
		name        string                // 测试用例名称
		options     *clients.Options      // 输入的配置选项
		expectError bool                  // 是否期望出现错误
		description string                // 测试用例描述
	}{
		{
			name: "默认配置创建服务",
			options: &clients.Options{
				ScanMode: "ctls",
				Timeout:  5,
				Retries:  3,
			},
			expectError: false,
			description: "使用默认ctls模式创建服务应该成功",
		},
		{
			name: "ztls模式创建服务",
			options: &clients.Options{
				ScanMode: "ztls",
				Timeout:  5,
				Retries:  3,
			},
			expectError: false,
			description: "使用ztls模式创建服务应该成功",
		},
		{
			name: "auto模式创建服务",
			options: &clients.Options{
				ScanMode: "auto",
				Timeout:  5,
				Retries:  3,
			},
			expectError: false,
			description: "使用auto模式创建服务应该成功",
		},
		{
			name: "openssl模式创建服务",
			options: &clients.Options{
				ScanMode: "openssl",
				Timeout:  5,
				Retries:  3,
			},
			expectError: false,
			description: "使用openssl模式创建服务，可能因为系统未安装openssl而失败",
		},
		{
			name: "未知模式默认为ctls",
			options: &clients.Options{
				ScanMode: "unknown",
				Timeout:  5,
				Retries:  3,
			},
			expectError: false,
			description: "未知扫描模式应该默认使用ctls模式",
		},
		{
			name: "空扫描模式默认为ctls",
			options: &clients.Options{
				ScanMode: "",
				Timeout:  5,
				Retries:  3,
			},
			expectError: false,
			description: "空扫描模式应该默认使用ctls模式",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 执行Service创建
			service, err := New(tt.options)

			if tt.expectError {
				// 期望出现错误的情况
				require.Error(t, err, "应该返回错误: %s", tt.description)
				require.Nil(t, service, "出错时service应该为nil")
			} else {
				// 期望成功的情况
				if err != nil {
					// 对于openssl模式，如果系统未安装openssl，允许失败
					if tt.options.ScanMode == "openssl" {
						t.Logf("OpenSSL模式创建失败（可能系统未安装OpenSSL）: %v", err)
						return
					}
					require.NoError(t, err, "不应该返回错误: %s", tt.description)
				}
				require.NotNil(t, service, "service不应该为nil")
				require.NotNil(t, service.options, "service.options不应该为nil")
				require.NotNil(t, service.client, "service.client不应该为nil")
				require.NotNil(t, service.options.Fastdialer, "Fastdialer应该被自动创建")

				// 验证扫描模式是否正确设置
				if tt.options.ScanMode == "unknown" || tt.options.ScanMode == "" {
					require.Equal(t, "ctls", service.options.ScanMode, "未知或空模式应该设置为ctls")
				} else {
					require.Equal(t, tt.options.ScanMode, service.options.ScanMode, "扫描模式应该匹配")
				}
			}
		})
	}
}

// TestNewWithCustomDialer 测试使用自定义拨号器创建Service
// 验证当提供自定义Fastdialer时不会被覆盖
func TestNewWithCustomDialer(t *testing.T) {
	// 创建自定义拨号器
	customDialer, err := fastdialer.NewDialer(fastdialer.DefaultOptions)
	require.NoError(t, err, "创建自定义拨号器不应该出错")

	options := &clients.Options{
		ScanMode:   "ctls",
		Timeout:    5,
		Retries:    3,
		Fastdialer: customDialer,
	}

	// 创建服务
	service, err := New(options)
	require.NoError(t, err, "使用自定义拨号器创建服务不应该出错")
	require.NotNil(t, service, "service不应该为nil")

	// 验证拨号器没有被替换
	require.Equal(t, customDialer, service.options.Fastdialer, "自定义拨号器不应该被替换")
}

// TestConnect 测试基本连接功能
// 使用真实的网络地址进行连接测试
func TestConnect(t *testing.T) {
	// 创建服务实例
	service, err := New(&clients.Options{
		ScanMode: "ctls",
		Timeout:  10,
		Retries:  2,
	})
	require.NoError(t, err, "创建服务不应该出错")

	tests := []struct {
		name        string // 测试用例名称
		host        string // 目标主机
		ip          string // 目标IP
		port        string // 目标端口
		expectError bool   // 是否期望出现错误
		description string // 测试用例描述
	}{
		{
			name:        "连接百度HTTPS",
			host:        "www.baidu.com",
			ip:          "**************",
			port:        "443",
			expectError: false,
			description: "连接百度的HTTPS服务应该成功",
		},
		{
			name:        "连接指定IP的HTTPS",
			host:        "",
			ip:          "**************",
			port:        "443",
			expectError: false,
			description: "直接连接IP地址的HTTPS服务应该成功",
		},
		{
			name:        "无效的主机和IP",
			host:        "",
			ip:          "",
			port:        "443",
			expectError: true,
			description: "主机和IP都为空应该返回错误",
		},
		{
			name:        "无效的端口",
			host:        "www.baidu.com",
			ip:          "**************",
			port:        "",
			expectError: true,
			description: "端口为空应该返回错误",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 执行连接
			response, err := service.Connect(tt.host, tt.ip, tt.port)

			if tt.expectError {
				// 期望出现错误的情况
				require.Error(t, err, "应该返回错误: %s", tt.description)
			} else {
				// 期望成功的情况
				require.NoError(t, err, "不应该返回错误: %s", tt.description)
				require.NotNil(t, response, "响应不应该为nil")
				require.Equal(t, tt.port, response.Port, "端口应该匹配")

				if tt.host != "" {
					require.Equal(t, tt.host, response.Host, "主机名应该匹配")
				}

				// 验证TLS版本不为空
				require.NotEmpty(t, response.Version, "TLS版本不应该为空")

				// 验证密码套件不为空
				require.NotEmpty(t, response.Cipher, "密码套件不应该为空")

				t.Logf("连接成功 - TLS版本: %s, 密码套件: %s", response.Version, response.Cipher)
			}
		})
	}
}

// TestConnectWithOptions 测试带选项的连接功能
// 验证各种连接选项的正确处理
func TestConnectWithOptions(t *testing.T) {
	// 创建服务实例
	service, err := New(&clients.Options{
		ScanMode: "ctls",
		Timeout:  10,
		Retries:  2,
	})
	require.NoError(t, err, "创建服务不应该出错")

	tests := []struct {
		name        string                  // 测试用例名称
		host        string                  // 目标主机
		ip          string                  // 目标IP
		port        string                  // 目标端口
		options     clients.ConnectOptions  // 连接选项
		expectError bool                    // 是否期望出现错误
		description string                  // 测试用例描述
	}{
		{
			name: "使用SNI连接",
			host: "www.baidu.com",
			ip:   "**************",
			port: "443",
			options: clients.ConnectOptions{
				SNI: "www.baidu.com",
			},
			expectError: false,
			description: "使用SNI连接应该成功",
		},
		{
			name: "指定TLS版本连接",
			host: "www.baidu.com",
			ip:   "**************",
			port: "443",
			options: clients.ConnectOptions{
				VersionTLS: "1.2",
			},
			expectError: false,
			description: "指定TLS 1.2版本连接应该成功",
		},
		{
			name: "空选项连接",
			host: "www.baidu.com",
			ip:   "**************",
			port: "443",
			options: clients.ConnectOptions{},
			expectError: false,
			description: "使用空选项连接应该成功",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 执行带选项的连接
			response, err := service.ConnectWithOptions(tt.host, tt.ip, tt.port, tt.options)

			if tt.expectError {
				// 期望出现错误的情况
				require.Error(t, err, "应该返回错误: %s", tt.description)
			} else {
				// 期望成功的情况
				require.NoError(t, err, "不应该返回错误: %s", tt.description)
				require.NotNil(t, response, "响应不应该为nil")
				require.Equal(t, tt.port, response.Port, "端口应该匹配")
				require.Equal(t, tt.host, response.Host, "主机名应该匹配")

				// 如果指定了TLS版本，验证返回的版本
				if tt.options.VersionTLS != "" {
					require.Equal(t, tt.options.VersionTLS, response.Version, "TLS版本应该匹配")
				}

				// 如果指定了SNI，验证返回的SNI
				if tt.options.SNI != "" {
					require.Equal(t, tt.options.SNI, response.ServerName, "SNI应该匹配")
				}

				t.Logf("连接成功 - TLS版本: %s, 密码套件: %s, SNI: %s",
					response.Version, response.Cipher, response.ServerName)
			}
		})
	}
}

// TestConnectWithJARM 测试JARM指纹识别功能
// 验证JARM哈希的正确计算
func TestConnectWithJARM(t *testing.T) {
	// 创建启用JARM的服务实例
	service, err := New(&clients.Options{
		ScanMode: "ctls",
		Timeout:  15,
		Retries:  2,
		Jarm:     true, // 启用JARM指纹识别
	})
	require.NoError(t, err, "创建服务不应该出错")

	// 测试JARM指纹识别
	response, err := service.Connect("www.baidu.com", "**************", "443")
	require.NoError(t, err, "JARM连接不应该出错")
	require.NotNil(t, response, "响应不应该为nil")

	// 验证JARM哈希
	require.NotEmpty(t, response.JarmHash, "JARM哈希不应该为空")
	require.Len(t, response.JarmHash, 62, "JARM哈希应该是62个字符")

	t.Logf("JARM哈希: %s", response.JarmHash)
}

// TestConnectWithVersionEnum 测试TLS版本枚举功能
// 验证服务器支持的TLS版本的正确枚举
func TestConnectWithVersionEnum(t *testing.T) {
	// 创建启用版本枚举的服务实例
	service, err := New(&clients.Options{
		ScanMode:        "ctls",
		Timeout:         15,
		Retries:         2,
		TlsVersionsEnum: true, // 启用TLS版本枚举
	})
	require.NoError(t, err, "创建服务不应该出错")

	// 测试TLS版本枚举
	response, err := service.Connect("www.baidu.com", "**************", "443")
	require.NoError(t, err, "版本枚举连接不应该出错")
	require.NotNil(t, response, "响应不应该为nil")

	// 验证版本枚举结果
	require.NotEmpty(t, response.VersionEnum, "版本枚举结果不应该为空")
	require.Contains(t, response.VersionEnum, response.Version, "枚举结果应该包含当前连接的版本")

	t.Logf("支持的TLS版本: %v", response.VersionEnum)
}

// TestConnectWithCipherEnum 测试密码套件枚举功能
// 验证服务器支持的密码套件的正确枚举
func TestConnectWithCipherEnum(t *testing.T) {
	// 创建启用密码套件枚举的服务实例
	service, err := New(&clients.Options{
		ScanMode:         "ctls",
		Timeout:          20,
		Retries:          2,
		TlsVersionsEnum:  true,  // 需要先枚举版本
		TlsCiphersEnum:   true,  // 启用密码套件枚举
		TLsCipherLevel:   []string{"all"}, // 枚举所有级别的密码套件
	})
	require.NoError(t, err, "创建服务不应该出错")

	// 测试密码套件枚举
	response, err := service.Connect("www.baidu.com", "**************", "443")
	require.NoError(t, err, "密码套件枚举连接不应该出错")
	require.NotNil(t, response, "响应不应该为nil")

	// 验证密码套件枚举结果
	require.NotEmpty(t, response.TlsCiphers, "密码套件枚举结果不应该为空")

	// 验证每个版本的密码套件
	for _, tlsCipher := range response.TlsCiphers {
		require.NotEmpty(t, tlsCipher.Version, "TLS版本不应该为空")

		// 至少应该有一种类型的密码套件
		totalCiphers := len(tlsCipher.Ciphers.Secure) +
			len(tlsCipher.Ciphers.Weak) +
			len(tlsCipher.Ciphers.Insecure) +
			len(tlsCipher.Ciphers.Unknown)
		require.Greater(t, totalCiphers, 0, "每个版本至少应该有一个密码套件")

		t.Logf("TLS %s 支持的密码套件 - 安全: %d, 弱: %d, 不安全: %d, 未知: %d",
			tlsCipher.Version,
			len(tlsCipher.Ciphers.Secure),
			len(tlsCipher.Ciphers.Weak),
			len(tlsCipher.Ciphers.Insecure),
			len(tlsCipher.Ciphers.Unknown))
	}
}

// TestConnectWithProbeStatus 测试探测状态功能
// 验证连接失败时的探测状态处理
func TestConnectWithProbeStatus(t *testing.T) {
	// 创建启用探测状态的服务实例
	service, err := New(&clients.Options{
		ScanMode:    "ctls",
		Timeout:     5,
		Retries:     1,
		ProbeStatus: true, // 启用探测状态
	})
	require.NoError(t, err, "创建服务不应该出错")

	// 测试连接到不存在的端口
	response, err := service.Connect("www.baidu.com", "**************", "9999")

	// 应该返回错误，但也应该有响应（因为启用了ProbeStatus）
	require.Error(t, err, "连接不存在的端口应该返回错误")
	require.NotNil(t, response, "启用ProbeStatus时即使出错也应该有响应")
	require.False(t, response.ProbeStatus, "探测状态应该为false")
	require.NotEmpty(t, response.Error, "错误信息不应该为空")
	require.Equal(t, "www.baidu.com", response.Host, "主机名应该匹配")
	require.Equal(t, "9999", response.Port, "端口应该匹配")

	t.Logf("探测失败 - 错误: %s", response.Error)
}

// TestEnumTlsVersions 测试TLS版本枚举内部方法
// 验证enumTlsVersions方法的正确性
func TestEnumTlsVersions(t *testing.T) {
	// 创建服务实例
	service, err := New(&clients.Options{
		ScanMode: "ctls",
		Timeout:  15,
		Retries:  2,
	})
	require.NoError(t, err, "创建服务不应该出错")

	tests := []struct {
		name        string                  // 测试用例名称
		host        string                  // 目标主机
		ip          string                  // 目标IP
		port        string                  // 目标端口
		options     clients.ConnectOptions  // 连接选项
		expectError bool                    // 是否期望出现错误
		description string                  // 测试用例描述
	}{
		{
			name: "枚举百度支持的TLS版本",
			host: "www.baidu.com",
			ip:   "**************",
			port: "443",
			options: clients.ConnectOptions{
				EnumMode: clients.Version,
			},
			expectError: false,
			description: "应该能够枚举百度支持的TLS版本",
		},
		{
			name: "枚举不存在端口的TLS版本",
			host: "www.baidu.com",
			ip:   "**************",
			port: "9999",
			options: clients.ConnectOptions{
				EnumMode: clients.Version,
			},
			expectError: false,
			description: "不存在的端口应该返回空的版本列表",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 执行TLS版本枚举
			versions, err := service.enumTlsVersions(tt.host, tt.ip, tt.port, tt.options)

			if tt.expectError {
				require.Error(t, err, "应该返回错误: %s", tt.description)
			} else {
				require.NoError(t, err, "不应该返回错误: %s", tt.description)
				require.NotNil(t, versions, "版本列表不应该为nil")

				if len(versions) > 0 {
					t.Logf("枚举到的TLS版本: %v", versions)

					// 验证版本格式
					for _, version := range versions {
						require.NotEmpty(t, version, "TLS版本不应该为空")
					}
				} else {
					t.Logf("未枚举到TLS版本（可能是连接失败）")
				}
			}
		})
	}
}

// TestEnumTlsCiphers 测试密码套件枚举内部方法
// 验证enumTlsCiphers方法的正确性
func TestEnumTlsCiphers(t *testing.T) {
	// 创建服务实例
	service, err := New(&clients.Options{
		ScanMode:       "ctls",
		Timeout:        20,
		Retries:        2,
		TLsCipherLevel: []string{"all"}, // 设置密码套件级别
	})
	require.NoError(t, err, "创建服务不应该出错")

	tests := []struct {
		name        string                  // 测试用例名称
		host        string                  // 目标主机
		ip          string                  // 目标IP
		port        string                  // 目标端口
		options     clients.ConnectOptions  // 连接选项
		expectError bool                    // 是否期望出现错误
		description string                  // 测试用例描述
	}{
		{
			name: "枚举TLS 1.2密码套件",
			host: "www.baidu.com",
			ip:   "**************",
			port: "443",
			options: clients.ConnectOptions{
				EnumMode:   clients.Cipher,
				VersionTLS: "1.2",
			},
			expectError: false,
			description: "应该能够枚举TLS 1.2的密码套件",
		},
		{
			name: "枚举TLS 1.3密码套件",
			host: "www.baidu.com",
			ip:   "**************",
			port: "443",
			options: clients.ConnectOptions{
				EnumMode:   clients.Cipher,
				VersionTLS: "1.3",
			},
			expectError: false,
			description: "应该能够枚举TLS 1.3的密码套件",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 执行密码套件枚举
			ciphers, err := service.enumTlsCiphers(tt.host, tt.ip, tt.port, tt.options)

			if tt.expectError {
				require.Error(t, err, "应该返回错误: %s", tt.description)
			} else {
				require.NoError(t, err, "不应该返回错误: %s", tt.description)
				require.NotNil(t, ciphers, "密码套件列表不应该为nil")

				if len(ciphers) > 0 {
					t.Logf("枚举到 %d 个密码套件", len(ciphers))

					// 验证密码套件格式
					for _, cipher := range ciphers {
						require.NotEmpty(t, cipher, "密码套件名称不应该为空")
					}

					// 显示前几个密码套件作为示例
					maxShow := 3
					if len(ciphers) < maxShow {
						maxShow = len(ciphers)
					}
					t.Logf("示例密码套件: %v", ciphers[:maxShow])
				} else {
					t.Logf("未枚举到密码套件（可能是版本不支持或连接失败）")
				}
			}
		})
	}
}

// TestCipherLevelConfiguration 测试不同密码套件级别配置
// 验证不同安全级别的密码套件枚举配置
func TestCipherLevelConfiguration(t *testing.T) {
	tests := []struct {
		name         string   // 测试用例名称
		cipherLevels []string // 密码套件级别配置
		description  string   // 测试用例描述
	}{
		{
			name:         "只枚举安全密码套件",
			cipherLevels: []string{"secure"},
			description:  "应该只枚举安全级别的密码套件",
		},
		{
			name:         "只枚举弱密码套件",
			cipherLevels: []string{"weak"},
			description:  "应该只枚举弱级别的密码套件",
		},
		{
			name:         "只枚举不安全密码套件",
			cipherLevels: []string{"insecure"},
			description:  "应该只枚举不安全级别的密码套件",
		},
		{
			name:         "枚举所有密码套件",
			cipherLevels: []string{"all"},
			description:  "应该枚举所有级别的密码套件",
		},
		{
			name:         "混合级别配置",
			cipherLevels: []string{"secure", "weak"},
			description:  "应该枚举安全和弱级别的密码套件",
		},
		{
			name:         "未知级别默认为all",
			cipherLevels: []string{"unknown"},
			description:  "未知级别应该默认为all",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建服务实例
			service, err := New(&clients.Options{
				ScanMode:       "ctls",
				Timeout:        15,
				Retries:        2,
				TLsCipherLevel: tt.cipherLevels,
			})
			require.NoError(t, err, "创建服务不应该出错")

			// 测试密码套件枚举配置
			options := clients.ConnectOptions{
				EnumMode:   clients.Cipher,
				VersionTLS: "1.2",
			}

			ciphers, err := service.enumTlsCiphers("www.baidu.com", "**************", "443", options)
			require.NoError(t, err, "密码套件枚举不应该出错")
			require.NotNil(t, ciphers, "密码套件列表不应该为nil")

			t.Logf("配置 %v 枚举到 %d 个密码套件", tt.cipherLevels, len(ciphers))
		})
	}
}

// TestRetryMechanism 测试重试机制
// 验证不同扫描模式下的重试逻辑
func TestRetryMechanism(t *testing.T) {
	tests := []struct {
		name        string // 测试用例名称
		scanMode    string // 扫描模式
		retries     int    // 重试次数
		description string // 测试用例描述
	}{
		{
			name:        "ctls模式重试机制",
			scanMode:    "ctls",
			retries:     3,
			description: "ctls模式应该进行重试",
		},
		{
			name:        "ztls模式重试机制",
			scanMode:    "ztls",
			retries:     2,
			description: "ztls模式应该进行重试",
		},
		{
			name:        "auto模式无重试",
			scanMode:    "auto",
			retries:     3,
			description: "auto模式内部有fallback机制，不需要额外重试",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建服务实例
			service, err := New(&clients.Options{
				ScanMode: tt.scanMode,
				Timeout:  5,
				Retries:  tt.retries,
			})

			// 对于openssl模式，如果不可用则跳过
			if err != nil && tt.scanMode == "openssl" {
				t.Skipf("OpenSSL不可用，跳过测试: %v", err)
			}
			require.NoError(t, err, "创建服务不应该出错")

			// 测试连接到一个可能失败的地址（使用不存在的端口）
			response, err := service.Connect("www.baidu.com", "**************", "9999")

			// 验证重试机制的行为
			if tt.scanMode == "auto" {
				// auto模式可能成功（因为有fallback）或失败
				t.Logf("auto模式结果 - 错误: %v, 响应: %v", err, response != nil)
			} else {
				// 其他模式应该失败（因为端口不存在）
				require.Error(t, err, "连接不存在的端口应该失败")
			}

			t.Logf("%s模式重试测试完成", tt.scanMode)
		})
	}
}

// TestInputValidation 测试输入参数验证
// 验证各种无效输入的处理
func TestInputValidation(t *testing.T) {
	// 创建服务实例
	service, err := New(&clients.Options{
		ScanMode: "ctls",
		Timeout:  5,
		Retries:  2,
	})
	require.NoError(t, err, "创建服务不应该出错")

	tests := []struct {
		name        string // 测试用例名称
		host        string // 目标主机
		ip          string // 目标IP
		port        string // 目标端口
		description string // 测试用例描述
	}{
		{
			name:        "空主机和IP",
			host:        "",
			ip:          "",
			port:        "443",
			description: "主机和IP都为空应该返回错误",
		},
		{
			name:        "空端口",
			host:        "www.baidu.com",
			ip:          "**************",
			port:        "",
			description: "端口为空应该返回错误",
		},
		{
			name:        "只有空格的主机名",
			host:        "   ",
			ip:          "",
			port:        "443",
			description: "只有空格的主机名应该被视为无效",
		},
		{
			name:        "只有空格的端口",
			host:        "www.baidu.com",
			ip:          "",
			port:        "   ",
			description: "只有空格的端口应该被视为无效",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 测试Connect方法
			response, err := service.Connect(tt.host, tt.ip, tt.port)
			require.Error(t, err, "应该返回错误: %s", tt.description)
			require.Nil(t, response, "出错时响应应该为nil")

			// 测试ConnectWithOptions方法
			response, err = service.ConnectWithOptions(tt.host, tt.ip, tt.port, clients.ConnectOptions{})
			require.Error(t, err, "应该返回错误: %s", tt.description)
			require.Nil(t, response, "出错时响应应该为nil")

			t.Logf("输入验证测试通过: %s", tt.description)
		})
	}
}

// TestComplexScenarios 测试复杂场景
// 验证多种功能组合使用的情况
func TestComplexScenarios(t *testing.T) {
	tests := []struct {
		name        string           // 测试用例名称
		options     *clients.Options // 服务配置选项
		description string           // 测试用例描述
	}{
		{
			name: "全功能启用测试",
			options: &clients.Options{
				ScanMode:         "ctls",
				Timeout:          30,
				Retries:          2,
				Jarm:             true,
				TlsVersionsEnum:  true,
				TlsCiphersEnum:   true,
				TLsCipherLevel:   []string{"all"},
				ProbeStatus:      true,
			},
			description: "启用所有功能的综合测试",
		},
		{
			name: "安全扫描配置",
			options: &clients.Options{
				ScanMode:         "ctls",
				Timeout:          20,
				Retries:          3,
				TlsVersionsEnum:  true,
				TlsCiphersEnum:   true,
				TLsCipherLevel:   []string{"secure", "weak"},
			},
			description: "专注于安全性的扫描配置",
		},
		{
			name: "快速扫描配置",
			options: &clients.Options{
				ScanMode: "ctls",
				Timeout:  5,
				Retries:  1,
				Jarm:     true,
			},
			description: "快速扫描配置，只获取基本信息和JARM指纹",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建服务实例
			service, err := New(tt.options)
			require.NoError(t, err, "创建服务不应该出错")

			// 执行连接测试
			response, err := service.Connect("www.baidu.com", "**************", "443")
			require.NoError(t, err, "连接不应该出错")
			require.NotNil(t, response, "响应不应该为nil")

			// 验证基本信息
			require.Equal(t, "www.baidu.com", response.Host, "主机名应该匹配")
			require.Equal(t, "443", response.Port, "端口应该匹配")
			require.NotEmpty(t, response.Version, "TLS版本不应该为空")
			require.NotEmpty(t, response.Cipher, "密码套件不应该为空")

			// 验证JARM指纹（如果启用）
			if tt.options.Jarm {
				require.NotEmpty(t, response.JarmHash, "JARM哈希不应该为空")
				require.Len(t, response.JarmHash, 62, "JARM哈希应该是62个字符")
			}

			// 验证版本枚举（如果启用）
			if tt.options.TlsVersionsEnum {
				require.NotEmpty(t, response.VersionEnum, "版本枚举结果不应该为空")
				require.Contains(t, response.VersionEnum, response.Version, "枚举结果应该包含当前版本")
			}

			// 验证密码套件枚举（如果启用）
			if tt.options.TlsCiphersEnum {
				require.NotEmpty(t, response.TlsCiphers, "密码套件枚举结果不应该为空")
			}

			t.Logf("复杂场景测试完成 - %s", tt.description)
		})
	}
}

// TestErrorHandling 测试错误处理
// 验证各种错误情况的正确处理
func TestErrorHandling(t *testing.T) {
	tests := []struct {
		name        string           // 测试用例名称
		options     *clients.Options // 服务配置选项
		host        string           // 目标主机
		ip          string           // 目标IP
		port        string           // 目标端口
		expectError bool             // 是否期望出现错误
		description string           // 测试用例描述
	}{
		{
			name: "连接超时测试",
			options: &clients.Options{
				ScanMode: "ctls",
				Timeout:  1, // 极短的超时时间
				Retries:  1,
			},
			host:        "www.baidu.com",
			ip:          "**************",
			port:        "443",
			expectError: false, // 可能成功也可能失败，取决于网络状况
			description: "极短超时时间可能导致连接失败",
		},
		{
			name: "无效主机名测试",
			options: &clients.Options{
				ScanMode: "ctls",
				Timeout:  10,
				Retries:  2,
			},
			host:        "invalid-host-that-does-not-exist-12345.com",
			ip:          "",
			port:        "443",
			expectError: true,
			description: "无效主机名应该导致连接失败",
		},
		{
			name: "无效IP地址测试",
			options: &clients.Options{
				ScanMode: "ctls",
				Timeout:  10,
				Retries:  2,
			},
			host:        "",
			ip:          "999.999.999.999", // 无效的IP地址
			port:        "443",
			expectError: true,
			description: "无效IP地址应该导致连接失败",
		},
		{
			name: "不存在的端口测试",
			options: &clients.Options{
				ScanMode: "ctls",
				Timeout:  5,
				Retries:  1,
			},
			host:        "www.baidu.com",
			ip:          "**************",
			port:        "99999", // 不存在的端口
			expectError: true,
			description: "连接不存在的端口应该失败",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建服务实例
			service, err := New(tt.options)
			require.NoError(t, err, "创建服务不应该出错")

			// 执行连接测试
			response, err := service.Connect(tt.host, tt.ip, tt.port)

			if tt.expectError {
				require.Error(t, err, "应该返回错误: %s", tt.description)
				t.Logf("预期错误测试通过 - 错误: %v", err)
			} else {
				// 对于可能成功也可能失败的情况，记录结果
				if err != nil {
					t.Logf("连接失败（可能是网络原因）- 错误: %v", err)
				} else {
					require.NotNil(t, response, "成功时响应不应该为nil")
					t.Logf("连接成功 - TLS版本: %s", response.Version)
				}
			}
		})
	}
}

// TestServiceStructure 测试Service结构体
// 验证Service结构体的内部状态
func TestServiceStructure(t *testing.T) {
	// 创建服务实例
	service, err := New(&clients.Options{
		ScanMode: "ctls",
		Timeout:  10,
		Retries:  3,
	})
	require.NoError(t, err, "创建服务不应该出错")

	// 验证Service结构体的字段
	require.NotNil(t, service, "service不应该为nil")
	require.NotNil(t, service.options, "service.options不应该为nil")
	require.NotNil(t, service.client, "service.client不应该为nil")

	// 验证配置选项
	require.Equal(t, "ctls", service.options.ScanMode, "扫描模式应该匹配")
	require.Equal(t, 10, service.options.Timeout, "超时时间应该匹配")
	require.Equal(t, 3, service.options.Retries, "重试次数应该匹配")
	require.NotNil(t, service.options.Fastdialer, "Fastdialer应该被自动创建")

	t.Log("Service结构体验证通过")
}

// TestConcurrentConnections 测试并发连接
// 验证服务在并发环境下的稳定性
func TestConcurrentConnections(t *testing.T) {
	// 创建服务实例
	service, err := New(&clients.Options{
		ScanMode: "ctls",
		Timeout:  15,
		Retries:  2,
	})
	require.NoError(t, err, "创建服务不应该出错")

	// 并发连接数
	concurrency := 3
	results := make(chan error, concurrency)

	// 启动多个并发连接
	for i := 0; i < concurrency; i++ {
		go func(id int) {
			response, err := service.Connect("www.baidu.com", "**************", "443")
			if err != nil {
				results <- err
				return
			}

			// 验证响应
			if response == nil {
				results <- fmt.Errorf("response is nil for goroutine %d", id)
				return
			}

			if response.Version == "" {
				results <- fmt.Errorf("empty version for goroutine %d", id)
				return
			}

			results <- nil
		}(i)
	}

	// 收集结果
	successCount := 0
	for i := 0; i < concurrency; i++ {
		err := <-results
		if err == nil {
			successCount++
		} else {
			t.Logf("并发连接 %d 失败: %v", i, err)
		}
	}

	// 至少应该有一半的连接成功
	require.Greater(t, successCount, 0, "至少应该有一个并发连接成功")
	t.Logf("并发测试完成 - 成功: %d/%d", successCount, concurrency)
}

// TestMemoryUsage 测试内存使用
// 验证服务不会造成内存泄漏
func TestMemoryUsage(t *testing.T) {
	// 创建和销毁多个服务实例
	for i := 0; i < 10; i++ {
		service, err := New(&clients.Options{
			ScanMode: "ctls",
			Timeout:  5,
			Retries:  1,
		})
		require.NoError(t, err, "创建服务不应该出错")

		// 执行一次连接
		_, _ = service.Connect("www.baidu.com", "**************", "443")

		// 服务实例会在循环结束时被垃圾回收
	}

	t.Log("内存使用测试完成")
}

// TestEdgeCases 测试边界情况
// 验证各种边界条件的处理
func TestEdgeCases(t *testing.T) {
	tests := []struct {
		name        string           // 测试用例名称
		options     *clients.Options // 服务配置选项
		description string           // 测试用例描述
	}{
		{
			name: "零重试次数",
			options: &clients.Options{
				ScanMode: "ctls",
				Timeout:  10,
				Retries:  0, // 零重试
			},
			description: "零重试次数应该正常工作",
		},
		{
			name: "极大重试次数",
			options: &clients.Options{
				ScanMode: "ctls",
				Timeout:  5,
				Retries:  100, // 极大重试次数
			},
			description: "极大重试次数应该正常工作",
		},
		{
			name: "极小超时时间",
			options: &clients.Options{
				ScanMode: "ctls",
				Timeout:  1, // 1秒超时
				Retries:  1,
			},
			description: "极小超时时间应该正常工作",
		},
		{
			name: "空密码套件级别",
			options: &clients.Options{
				ScanMode:         "ctls",
				Timeout:          10,
				Retries:          2,
				TlsCiphersEnum:   true,
				TLsCipherLevel:   []string{}, // 空级别列表
			},
			description: "空密码套件级别应该正常工作",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建服务实例
			service, err := New(tt.options)
			require.NoError(t, err, "创建服务不应该出错")
			require.NotNil(t, service, "service不应该为nil")

			// 验证服务可以正常工作
			response, err := service.Connect("www.baidu.com", "**************", "443")

			// 对于可能失败的边界情况，记录结果但不强制要求成功
			if err != nil {
				t.Logf("边界情况连接失败（可能是预期的）- 错误: %v", err)
			} else {
				require.NotNil(t, response, "成功时响应不应该为nil")
				t.Logf("边界情况测试成功 - %s", tt.description)
			}
		})
	}
}

// TestNilOptions 测试nil选项处理
// 验证传入nil选项时的错误处理
func TestNilOptions(t *testing.T) {
	// 测试传入nil选项
	service, err := New(nil)

	// 应该返回错误或panic（取决于实现）
	if err != nil {
		require.Error(t, err, "传入nil选项应该返回错误")
		require.Nil(t, service, "出错时service应该为nil")
		t.Logf("nil选项正确返回错误: %v", err)
	} else {
		// 如果没有返回错误，说明代码处理了nil情况
		t.Log("代码能够处理nil选项")
	}
}

// TestServiceReuse 测试服务重用
// 验证同一个服务实例可以多次使用
func TestServiceReuse(t *testing.T) {
	// 创建服务实例
	service, err := New(&clients.Options{
		ScanMode: "ctls",
		Timeout:  10,
		Retries:  2,
	})
	require.NoError(t, err, "创建服务不应该出错")

	// 多次使用同一个服务实例
	for i := 0; i < 3; i++ {
		response, err := service.Connect("www.baidu.com", "**************", "443")
		require.NoError(t, err, "第%d次连接不应该出错", i+1)
		require.NotNil(t, response, "第%d次响应不应该为nil", i+1)
		require.NotEmpty(t, response.Version, "第%d次TLS版本不应该为空", i+1)

		t.Logf("第%d次连接成功 - TLS版本: %s", i+1, response.Version)
	}

	t.Log("服务重用测试完成")
}

// TestDifferentPorts 测试不同端口连接
// 验证连接到不同端口的能力
func TestDifferentPorts(t *testing.T) {
	// 创建服务实例
	service, err := New(&clients.Options{
		ScanMode: "ctls",
		Timeout:  10,
		Retries:  2,
	})
	require.NoError(t, err, "创建服务不应该出错")

	// 测试不同的端口
	ports := []string{"443", "8443"}

	for _, port := range ports {
		t.Run("端口"+port, func(t *testing.T) {
			response, err := service.Connect("www.baidu.com", "**************", port)

			if err != nil {
				t.Logf("端口 %s 连接失败（可能该端口未开放）: %v", port, err)
			} else {
				require.NotNil(t, response, "成功时响应不应该为nil")
				require.Equal(t, port, response.Port, "端口应该匹配")
				t.Logf("端口 %s 连接成功 - TLS版本: %s", port, response.Version)
			}
		})
	}
}
