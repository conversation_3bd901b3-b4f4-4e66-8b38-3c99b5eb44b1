// Author: chenjb
// Version: V1.0
// Date: 2025-06-23 20:09:07
// FilePath: /yaml_scan/pkg/tlsx/output/file_writer.go
// Description: 文件输出写入器，提供高效的缓冲文件写入功能

// Package output 的文件写入器模块
// 提供基于缓冲区的高效文件写入功能，用于TLS扫描结果的文件输出
// 通过缓冲机制减少系统调用次数，提高写入性能
package output

import (
	"bufio" // 缓冲I/O操作
	"os"    // 操作系统文件接口
)

// fileWriter 并发安全的基于文件的输出写入器
// 使用缓冲写入机制提高文件I/O性能，适用于大量数据的输出场景
type fileWriter struct {
	file   *os.File       // 底层文件句柄，用于文件操作
	writer *bufio.Writer  // 缓冲写入器，减少系统调用提高性能
}

// newFileOutputWriter 为指定文件创建新的缓冲写入器
// 该函数创建或覆盖指定文件，并初始化缓冲写入器
//
// 参数:
//   - file: 目标文件路径，如果文件存在将被覆盖
//
// 返回值:
//   - *fileWriter: 初始化完成的文件写入器实例
//   - error: 文件创建过程中的错误，成功时为nil
//
// 特性:
//   - 自动创建目标文件（如果不存在）
//   - 覆盖已存在的文件内容
//   - 使用缓冲写入提高I/O性能
//   - 支持并发安全的写入操作
func newFileOutputWriter(file string) (*fileWriter, error) {
	// 创建或覆盖目标文件
	output, err := os.Create(file)
	if err != nil {
		return nil, err
	}

	// 创建文件写入器实例，包含文件句柄和缓冲写入器
	return &fileWriter{
		file:   output,                    // 保存文件句柄
		writer: bufio.NewWriter(output),   // 创建缓冲写入器
	}, nil
}

// Write 将数据写入到底层文件
// 该方法将字节数据写入缓冲区，并自动添加换行符
//
// 参数:
//   - data: 要写入的字节数据
//
// 返回值:
//   - error: 写入过程中的错误，成功时为nil
//
// 写入特性:
//   - 使用缓冲写入，数据首先写入内存缓冲区
//   - 自动在每行数据后添加换行符
//   - 缓冲区满时或调用Flush时才实际写入磁盘
//   - 提高写入性能，减少系统调用次数
//
// 注意:
//   - 数据可能暂存在缓冲区中，需要调用Close或Flush确保写入磁盘
//   - 每次调用都会在数据后添加一个换行符
func (w *fileWriter) Write(data []byte) error {
	// 将数据写入缓冲区
	_, err := w.writer.Write(data)
	if err != nil {
		return err
	}

	// 自动添加换行符，确保每行数据独立
	_, err = w.writer.WriteRune('\n')
	return err
}

// Close 关闭底层写入器并将所有数据刷新到磁盘
// 该方法执行完整的关闭流程，确保数据完整性和资源释放
//
// 返回值:
//   - error: 关闭过程中的错误，成功时为nil
//
// 关闭流程:
//   1. 刷新缓冲区：将缓冲区中的所有数据写入文件
//   2. 同步文件：强制操作系统将数据写入磁盘
//   3. 关闭文件：释放文件句柄和相关资源
//
// 数据安全性:
//   - Flush确保缓冲区数据不丢失
//   - Sync确保数据持久化到磁盘
//   - Close释放系统资源
//
// 注意:
//   - 忽略Sync的错误，因为在某些文件系统上可能不支持
//   - 即使Sync失败，文件关闭仍会继续执行
//   - 调用后文件写入器不可再使用
func (w *fileWriter) Close() error {
	// 刷新缓冲区，将所有缓存数据写入文件
	w.writer.Flush()

	// 强制同步到磁盘，确保数据持久化
	//nolint:errcheck // 忽略sync错误，因为某些文件系统可能不支持
	w.file.Sync()

	// 关闭文件句柄，释放系统资源
	return w.file.Close()
}

